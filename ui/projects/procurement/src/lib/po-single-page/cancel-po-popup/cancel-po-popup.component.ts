import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { PoCancelDto, PoCancelResponseDto } from '@j3-procurement/dtos/po';
import {
  NotificationService,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { IJbDialog, IJbTextArea, UserService } from 'jibe-components';

import { takeUntil } from 'rxjs/operators';
import { ePrcSuccessMessages } from '../../models/enums/prc-messages.enum';
import { POItemsService } from '../../services/po/po-items.service';
import { POService } from '../../services/po/po.service';
import { PrcSharedService } from '../../services/prc-shared.service';
import { WorkflowBtnService } from '../../workflow/services/workflow-btn.service';

@Component({
  selector: 'prc-cancel-po-popup',
  templateUrl: './cancel-po-popup.component.html',
  styleUrls: ['./cancel-po-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CancelPoPopupComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public content: IJbDialog = {
    dialogHeader: 'Cancel PO',
    dialogWidth: 500,
    showHeader: true,
    closableIcon: true,
  };
  public reasonTextarea: IJbTextArea = {
    placeholder: 'Text',
    id: 'reason',
    rows: 15,
    maxlength: 2000,
  };
  public isCancelingPO = false;

  form: TypedFormGroup<PoCancelDto>;

  @Input() poUid: string;
  @Input() sendToSupplier: boolean;
  @Output() close = new EventEmitter<PoCancelResponseDto | void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly notificationService: NotificationService,
    private readonly poItemsService: POItemsService,
    private readonly poService: POService,
    private readonly prcSharedService: PrcSharedService,
    private readonly userService: UserService,
    private readonly workflowBtnService: WorkflowBtnService
  ) {
    super();
    this.form = this.fb.group<PoCancelDto>({
      reason: [null, [Validators.required]],
    });
  }

  ngOnInit(): void {
    this.poItemsService
      .getPOItemsQty(this.poUid)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((poItemsQty) => {
        const enableReturnItemsToRequisition = !!poItemsQty.find(
          (item) =>
            item.itemOrderedQuantity - item.orderedQuantity < item.itemQuantity
        );
        this.form.addControl(
          'returnItemsToRequisition',
          this.fb.control({
            value: enableReturnItemsToRequisition,
            disabled: !enableReturnItemsToRequisition,
          })
        );
        this.cdr.markForCheck();
      });
  }

  returnItemsToRequisitionChanged(event: { checked?: boolean }): void {
    this.form.controls.returnItemsToRequisition.setValue(event.checked);
  }

  async cancelPo(): Promise<void> {
    this.isCancelingPO = true;
    const { reason, returnItemsToRequisition } = this.form.value;
    const { UserID, User_FullName, user_uid } =
      this.userService.getUserDetails();
    const [{ fullName, mailID, mobileNumber }, { clientUid }, { jcdsUrl }] =
      await Promise.all([
        this.prcSharedService.getUserDetailsByUserId(UserID).toPromise(),
        this.prcSharedService.getClientUid().toPromise(),
        this.prcSharedService.getJCDSUrl().toPromise(),
      ]);

    const purchaserDetails = {
      contactNumber: mobileNumber,
      email: mailID,
      name: fullName,
    };

    const cancelPoData: PoCancelDto = {
      clientUid,
      jcdsUrl,
      purchaserDetails,
      reason,
      returnItemsToRequisition,
      sendToSupplier: this.sendToSupplier,
      user: { userName: User_FullName, userUid: user_uid },
    };
    try {
      await this.workflowBtnService.runAction('CANCEL', {
        forceRework: false,
        reason,
      });
      // todo move to backend
      const result = await this.poService
        .cancelPo(this.poUid, cancelPoData)
        .toPromise();
      this.notificationService.success(ePrcSuccessMessages.PoCancelSuccess);
      this.close.emit(result);
    } catch (err) {
      const { message } = err.error ?? {};
      this.notificationService.error(message ?? err?.message);
      this.isCancelingPO = false;
    }
  }
}
