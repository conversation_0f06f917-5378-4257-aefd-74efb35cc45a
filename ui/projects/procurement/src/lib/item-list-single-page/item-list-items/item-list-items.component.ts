import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  Optional,
  Self,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NgControl } from '@angular/forms';
import {
  ItemListItemDto,
  ItemListItemFilterOptionsDto,
  ItemListStatus,
  UpdateItemDto,
} from '@j3-procurement/dtos/item-list';
import {
  eGridEvents,
  GridAction,
  GridService,
  GridShareDataService,
} from 'jibe-components';

import { TagDto } from '@j3-prc-catalog/dtos/tag';
import { SimpleChangesTyped } from 'j3-prc-components';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { filter, first, takeUntil } from 'rxjs/operators';
import { NO, UNKNOWN, YES } from '../../models/constants';
import { ePrcPermission } from '../../models/enums/prc-permission.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import { HeaderStatus } from '../../models/interfaces/header-status';
import { JbCellChangeEvent } from '../../models/interfaces/jb-cell-change-event';
import { ItemListService } from '../../services/item-list/item-list.service';
import { PermissionService } from '../../services/permission/permission.service';
import { RobService } from '../../services/rob/rob.service';
import {
  GridControlValueAccessor,
  RowChanges,
} from '../../shared/grid-control-value-accessor';
import { BaseSectionService } from '../../shared/single-page-sections/base-section/base-section.service';
import { RowChangesService } from '../../shared/single-page-sections/base-section/row-changes.service';
import { parseTyped } from '../../utils/parse-typed';
import { DB_ROB_STATUSES, editableFields, eGridRowActions } from './constants';
import {
  additionalColumns,
  additionalFilters,
  columns,
  filters,
  getActions,
  getFiltersLists,
  gridName,
  searchFields,
} from './grid-inputs';
import { ColumnKey, ItemEvent, ItemListItemGridData } from './types';

@Component({
  selector: 'prc-item-list-items',
  templateUrl: './item-list-items.component.html',
  styleUrls: ['./item-list-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridShareDataService, BaseSectionService, RowChangesService],
})
export class ItemListItemsComponent
  extends GridControlValueAccessor
  implements OnInit, OnChanges
{
  public vesselUid$ = new BehaviorSubject<string>(null);
  public robs$ = new Observable<number[]>();
  public showDbRob: boolean;
  @Input() event$: Observable<ItemEvent>;
  @Input() isEditMode: boolean;
  @Input() itemListUid: string;

  @Input() set vesselUid(value: string) {
    this.vesselUid$.next(value);
  }

  @Input() set status(value: HeaderStatus<ItemListStatus>) {
    this.showDbRob = DB_ROB_STATUSES.includes(value.text);
    if (this.showDbRob) {
      this.gridService.refreshGrid('Table', gridName);
    }
  }

  @ViewChild('itemNameTemplate', { static: true })
  itemNameTemplate: TemplateRef<HTMLElement>;

  @ViewChild('robTemplate', { static: true })
  robTemplate: TemplateRef<HTMLElement>;

  @ViewChild('tagsTemplate', { static: true })
  tagsTemplate: TemplateRef<HTMLElement>;

  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, ItemListItemDto>;
  public UNKNOWN = UNKNOWN;
  public itemSinglePageRoute = ePageRoutes.ItemSinglePage;

  private removedItemUids = new Set<string>();

  constructor(
    @Optional() @Self() public ngControl: NgControl,
    private readonly baseSectionService: BaseSectionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly gridService: GridService,
    private readonly itemListService: ItemListService,
    private readonly permissionService: PermissionService,
    private readonly rowChangesService: RowChangesService,
    private readonly robService: RobService
  ) {
    super(ngControl);
  }

  ngOnInit(): void {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      itemName: this.itemNameTemplate,
      rob: this.robTemplate,
      tagsLabels: this.tagsTemplate,
    };

    this.gridInputs = {
      gridName,
      actions: getActions(),
      columns: [...columns, ...additionalColumns].map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      filters: [...filters, ...additionalFilters],
      filtersLists: getFiltersLists([], [], [], []),
      request: this.itemListService.getItemsRequest(this.itemListUid),
    };

    combineLatest([
      this.vesselUid$.pipe(
        filter((val) => !!val),
        first()
      ),
      this.gridService.matrixDataChanged.pipe(
        filter((event) => event.gridName === gridName)
      ),
    ])
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(([vesselUid, { matrixValues }]) => {
        if (!matrixValues?.length) {
          return;
        }

        const itemUids: string[] = [];
        const newMatrixValues: ItemListItemGridData[] = matrixValues.map(
          (matrixValue) => {
            itemUids.push(matrixValue.itemUid);
            const rowChanges = this.rowChangesService.getRowChanges(
              matrixValue.uid
            );
            return {
              ...matrixValue,
              dangerousGoodsText: matrixValue.dangerousGoodsUid ? YES : NO,
              ihmText: matrixValue.ihm ? YES : NO,
              tags: parseTyped<TagDto[]>(matrixValue.tagsLabels) ?? [],
              typeAttributes: null,
              ...rowChanges,
            };
          }
        );
        this.robs$ = this.robService.getRobs(itemUids, vesselUid);
        this.gridService.storeData$.next({
          gridName,
          data: newMatrixValues,
        });
      });

    this.ngControl.control.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((v) => !v)
      )
      .subscribe((_) => this.rowChangesService.resetChanges());

    if (this.event$) {
      this.event$
        .pipe(
          takeUntil(this.componentDestroyed$),
          filter(({ type }) => type === 'refreshGrid')
        )
        .subscribe(() => {
          this.gridInputs.request.body = undefined;
          this.gridService.refreshGrid(eGridEvents.Table, gridName);
        });
    }

    this.setFiltersLists();
    this.toggleActions();
  }

  ngOnChanges({ isEditMode }: SimpleChangesTyped<this>): void {
    if (isEditMode && !this.ngControl?.disabled) {
      this.switchGridEditMode(isEditMode.currentValue);
    }
  }

  private hideRemovedItems(): void {
    this.gridInputs = {
      ...this.gridInputs,
      data: this.baseSectionService.filterRemovedRows(this.removedItemUids),
      request: {
        ...this.gridInputs.request,
        body: { uidsToExclude: [...this.removedItemUids.values()] },
      },
    };
  }

  public onCellChange(event: JbCellChangeEvent<ItemListItemDto>): void {
    const { catalogUid, itemUid, uid } = event.rowData;
    const change: Partial<UpdateItemDto> = {};

    switch (event.cellName.FieldName as ColumnKey) {
      case 'qty':
        change.qty = Number(event.cellvalue);
        break;
      case 'remarks':
        change.remarks = event.cellvalue && String(event.cellvalue);
        break;
      default:
        return;
    }
    this.updateRowData(uid, {
      ...change,
      catalogUid,
      itemUid,
    });
  }

  public onGridAction({
    payload,
    type,
  }: GridAction<eGridRowActions, ItemListItemDto>): void {
    const { catalogUid, itemUid, uid } = payload;
    const changes: Partial<UpdateItemDto> = {};

    switch (type) {
      case eGridRowActions.DeleteRemark:
        changes.remarks = '';
        payload.remarks = '';
        break;
      case eGridRowActions.RemoveItem:
        this.removedItemUids.add(uid);
        changes.activeStatus = false;
        this.hideRemovedItems();
        break;
      default:
        return;
    }

    this.updateRowData(uid, {
      ...changes,
      catalogUid,
      itemUid,
    });
  }

  private async setFiltersLists(): Promise<void> {
    const filterOptions: ItemListItemFilterOptionsDto =
      await this.itemListService.getItemListItemFilters(this.itemListUid);

    this.gridInputs.filtersLists = getFiltersLists(
      filterOptions?.brands.map(({ uid, name }) => ({
        label: name,
        value: uid,
      })),
      filterOptions?.models.map((modelNumber) => ({
        label: modelNumber,
        value: modelNumber,
      })),
      [],
      filterOptions?.catalogs.map(({ uid, name }) => ({
        label: name,
        value: uid,
      }))
    );

    this.baseSectionService.setFilters(this.gridInputs);
  }

  private switchGridEditMode(value: boolean): void {
    if (!this.gridInputs) {
      return;
    }

    this.gridInputs.columns = this.gridInputs.columns.map((column) => ({
      ...column,
      Editable: value && editableFields.includes(column.FieldName),
    }));

    setTimeout(() => {
      this.gridService.refreshGrid(
        eGridEvents.StaticColumns,
        this.gridInputs.gridName
      );
    });
  }

  private async toggleActions(): Promise<void> {
    const canDeleteItems = await this.permissionService.hasPermissions(
      ePrcPermission.ListDeleteItems
    );
    this.gridInputs.actions = getActions(!canDeleteItems);

    this.cdr.markForCheck();
  }

  public updateRowData(uid: string, rowChanges: RowChanges): void {
    const mergedRowChanges = this.baseSectionService.mergeRowChanges(
      uid,
      rowChanges
    );
    this.rowChangesService.setRowChanges(uid, mergedRowChanges);
    this.onChange(Object.values(this.rowChangesService.getChanges()));
  }
}
