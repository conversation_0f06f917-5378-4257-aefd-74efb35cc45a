<div class="contact-form">
  <prc-form-label
    [required]="true"
    label="Supplier Status"
    class="details-label"
  >
    <jb-single-select-dropdown
      [content]="supplierStatusDropdown"
      (selectedSubOperation)="selectedSupplierStatus()"
    >
    </jb-single-select-dropdown>
  </prc-form-label>
  <form *ngIf="isSupplierConfirmation" [formGroup]="supplierConfirmationForm">
    <div class="two-columns">
      <div class="column">
        <prc-form-label
          label="Date Confirmed"
          class="details-label"
          [required]="true"
        >
          <div class="date-confirmed">
            <p-calendar
              [showIcon]="true"
              formControlName="confirmDate"
              [monthNavigator]="true"
              placeholder="Date"
              [dateFormat]="userDateControl"
              [readonlyInput]="true"
              appendTo="body"
              showButtonBar="true"
              [required]="true"
            ></p-calendar>
          </div>
        </prc-form-label>

        <prc-form-label
          label="Order Confirmation Number"
          class="details-label"
          [required]="true"
        >
          <div class="borderPix">
            <input
              class="jb-text"
              pInputText
              formControlName="orderConfirmationNumber"
              autocomplete="off"
              placeholder="Text"
            />
          </div>
        </prc-form-label>

        <prc-form-label
          label="Ready Date"
          class="details-label"
          [required]="true"
        >
          <div class="date-confirmed">
            <p-calendar
              [showIcon]="true"
              formControlName="readyDate"
              [monthNavigator]="true"
              placeholder="Date"
              [dateFormat]="userDateControl"
              [readonlyInput]="true"
              appendTo="body"
              showButtonBar="true"
              [required]="true"
            ></p-calendar>
          </div>
        </prc-form-label>

        <prc-form-label label="Remarks" class="details-label">
          <div class="borderPix">
            <jb-textarea [content]="remarkTxtArea" formControlName="remark">
            </jb-textarea>
          </div>
        </prc-form-label>

        <prc-form-label label="Upload" class="details-label"> </prc-form-label>
      </div>
    </div>
    <jb-uploads
      class="file-picker"
      [uploads]="attachments"
      [filesToBeUpload]="filesToBeUpload"
      [size]="attachments.size"
      [parentControl]="true"
    ></jb-uploads>

    <div>
      <prc-form-label
        class="form-label"
        [label]="fileGroupNames[fg.key]"
        *ngFor="let fg of fileGroups$ | async | keyvalue"
      >
        <div class="files-container">
          <div
            *ngFor="let f of fg.value; trackBy: trackByUid"
            class="file"
            (click)="download(f.uid)"
          >
            <i class="icons8-pdf"></i>
            <a>{{ f.name }}</a>
          </div>
        </div>
      </prc-form-label>
    </div>
  </form>

  <form *ngIf="isSupplierDeclined" [formGroup]="declinedForm">
    <div class="two-columns">
      <div class="column">
        <prc-form-label
          label="Declined Date"
          class="details-label"
          [required]="true"
        >
          <div class="date-confirmed">
            <p-calendar
              [showIcon]="true"
              formControlName="declineDate"
              [monthNavigator]="true"
              placeholder="Date"
              [dateFormat]="userDateControl"
              [readonlyInput]="true"
              appendTo="body"
              showButtonBar="true"
              [required]="true"
            ></p-calendar>
          </div>
        </prc-form-label>

        <prc-form-label
          [required]="true"
          label="Order Decline Reason"
          class="details-label"
        >
          <jb-multi-select-dropdown
            formControlName="declineReasons"
            [content]="declineReasonDropdown"
          >
          </jb-multi-select-dropdown>
        </prc-form-label>
      </div>
    </div>
  </form>
</div>
