<jb-details-layout-figma [formGroup]="itemListForm">
  <prc-generic-header
    alayout-header
    class="single-page-header"
    formControlName="name"
    [buttons]="headerButtons"
    [editMode]="sectionEditMode.headerDetails"
    [sections]="headerSections"
    [settingsOptions]="headerMenu"
    [showThreeDot]="itemListUid"
    [status]="headerStatus"
    (click)="setSectionEditMode('headerDetails')"
  >
    <prc-item-list-header
      [createDate]="itemListCreateDate"
      [isEditMode]="sectionEditMode.headerDetails"
      [itemListUid]="itemListUid"
      [vesselId]="vessel?.vesselId"
      [numberOfItems]="itemListCount"
      (handoverChange)="onHandoverChange($event)"
    ></prc-item-list-header>
  </prc-generic-header>

  <div class="section main" alayout-content>
    <ng-container *ngIf="currentView === 'list-details'">
      <jb-layout-widget
        id="general-information"
        titleText="General Information"
        titleIconClass="icons8-resume-template"
        (click)="setSectionEditMode('generalInformation')"
      >
        <prc-item-list-general-information
          awidget-content
          [isEditMode]="sectionEditMode.generalInformation"
          [segmentUids]="segmentUids"
        ></prc-item-list-general-information>
      </jb-layout-widget>

      <jb-layout-widget
        id="contributing-team"
        titleText="Contributing Team"
        titleIconClass="icons8-resume-template"
        (click)="setSectionEditMode('contributingTeam')"
      >
        <prc-item-list-contributing-team
          awidget-content
          [isEditMode]="sectionEditMode.contributingTeam"
        ></prc-item-list-contributing-team>
      </jb-layout-widget>

      <prc-check-list
        id="checklist"
        formControlName="tasks"
      ></prc-check-list>

      <jb-layout-widget
        *ngIf="attachmentConfig && vessel"
        id="attachments"
        titleText="Attachments"
        titleIconClass="icons8-resume-template"
      >
        <jb-button
          awidget-header-buttons
          [disabled]="itemListForm.disabled"
          type="NoButton"
          (click)="attachments.dialogOnDemand()"
          label="Attach file"
        ></jb-button>

        <jb-attachments
          awidget-content
          #attachments
          [actionRow]="attachmentConfig.actions"
          [attachConfig]="attachmentConfig"
          [syncTo]="vessel?.vesselId.toString()"
        ></jb-attachments>
      </jb-layout-widget>
    </ng-container>

    <ng-container *ngIf="currentView === 'items'">
      <jb-layout-widget
        *ngIf="itemListUid"
        id="items"
        titleText="Items"
        titleIconClass="icons8-shopping-cart-2"
        (click)="setSectionEditMode('items')"
      >
        <jb-button
          awidget-header-buttons
          type="NoButton"
          (click)="navigateToGeneralBrowsing(['Spares', 'Stores'])"
          label="Browse Item"
          [disabled]="disabledFeature.browseItems"
        ></jb-button>

        <prc-item-list-items
          awidget-content
          formControlName="items"
          [event$]="itemEvent$"
          [isEditMode]="sectionEditMode.items"
          [itemListUid]="itemListUid"
          [vesselUid]="vessel?.uid"
          [status]="headerStatus"
        ></prc-item-list-items>
      </jb-layout-widget>

      <jb-layout-widget
        *ngIf="itemListUid"
        id="services"
        titleText="Services"
        titleIconClass="icons8-user-2"
        (click)="setSectionEditMode('jobs')"
      >
        <ng-container awidget-header-buttons>
          <jb-button
            type="NoButton"
            (click)="navigateToGeneralBrowsing('Services')"
            label="Browse Services"
            [disabled]="disabledFeature.browseJobs"
          ></jb-button>
        </ng-container>

        <prc-item-list-jobs
          awidget-content
          formControlName="jobs"
          [event$]="jobEvent$"
          [isEditMode]="sectionEditMode.jobs"
          [itemListUid]="itemListUid"
        ></prc-item-list-jobs>
      </jb-layout-widget>

      <jb-layout-widget
        *ngIf="itemListUid"
        id="machinery"
        titleText="Machinery"
        titleIconClass="sm-machinery"
      >
        <prc-machinery
          awidget-content
          [refreshSubject$]="refreshMachinery$"
          [objectUid]="itemListUid"
          class="requisition-machinery"
        >
        </prc-machinery>
      </jb-layout-widget>
    </ng-container>

    <jb-layout-widget
      *ngIf="currentView === 'linked-records'"
      id="linked-records"
      titleText="Linked Records"
      titleIconClass="icons8-clipboard"
    >
      <jb-tm-linked-records
        *ngIf="linkedRecordsDetails"
        awidget-content
        [hideHeader]="true"
        [taskManagerDetails]="linkedRecordsDetails"
        taskType="supplier_invoice"
      ></jb-tm-linked-records>
    </jb-layout-widget>
  </div>

  <jb-discussion-feed
    *ngIf="feedDetail"
    alayout-aside
    class="feed-discussion"
    [detail]="feedDetail"
    [canAdd]="true"
    [function_code]="functionCode"
    [module_code]="moduleCode"
    [vessel_uid]="vessel?.uid"
  ></jb-discussion-feed>
</jb-details-layout-figma>

<prc-item-list-create-requisition
  *ngIf="createRequisitionDto && vessel"
  [itemListUid]="itemListUid"
  [itemList]="createRequisitionDto"
  [vessel]="vessel"
  [segmentUids]="segmentUids"
  [(popupOpen)]="createRequisitionDto"
  (close)="onCloseCreateRequisitionPopup()"
  (requsitionCreated)="completeItemList()"
></prc-item-list-create-requisition>

<prc-create-item-list-popup
  *ngIf="duplicateItemListPopupData"
  [data]="duplicateItemListPopupData"
  [confirmationDialogParams]="confirmationDialogParams"
  dialogHeader="Duplicate List"
  (close)="onCloseDuplicateItemListPopup($event)"
></prc-create-item-list-popup>
