import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  GridRowActions,
  IExportAdditionalColumn,
} from 'jibe-components';

import { PoJobDto } from '@j3-procurement/dtos/po';
import { createColumns } from 'j3-prc-components';
import { eGridRowActions } from './grid-constants';
import { ColumnKey } from './types';

export const gridName = 'jobSection';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  [
    'Code',
    'code',
    { width: eGridColumnsWidth.LongDescription, hyperlink: true },
  ],
  ['Title', 'jobName', { width: eGridColumnsWidth.LongDescription }],
  ['Ordered', 'orderedQuantity', { width: eGridColumnsWidth.LongNumber }],
  ['Status', 'status', { width: eGridColumnsWidth.ShortDescription }],
  [
    'Date',
    'deliveryDate',
    {
      ControlType: eFieldControlType.Date,
      width: eGridColumnsWidth.Date,
      FieldType: eGridCellType.Date,
    },
  ],
  [
    'Delivery Port',
    'deliveryPortId',
    { width: eGridColumnsWidth.LongDescription },
  ],
  [
    'Service Performance',
    'servicePerformanceUid',
    { width: eGridColumnsWidth.LongDescription },
  ],
  ['', 'attachment', { width: eGridColumnsWidth.YesNo, isSortable: false }],
]);

export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['Job Action', 'action', { width: eGridColumnsWidth.LongDescription }],
    ['Brand / Make', 'brandName', { width: eGridColumnsWidth.LongDescription }],
    ['Model', 'modelNumber', { width: eGridColumnsWidth.LongDescription }],
    [
      'Due Date',
      'dueDate',
      {
        ControlType: eFieldControlType.Date,
        width: eGridColumnsWidth.Date,
      },
    ],
    [
      'Description',
      'description',
      { width: eGridColumnsWidth.LongDescription },
    ],
  ],
  { IsVisible: false }
);

export const searchFields: ColumnKey[] = [
  'code',
  'jobName',
  'description',
  'catalogName',
];

export function getActions(
  isActionsDisabled: boolean,
  isOfficeApp: boolean
): GridRowActions[] {
  const actions = {
    Open: {
      name: eGridRowActions.MarkAsPending,
      icon: eIconNames.Cancel,
      color: eColor.JbBlue,
      disabled: isActionsDisabled,
    },
    Confirm: {
      name: eGridRowActions.MarkAsCompleted,
      icon: eIconNames.Checked,
      color: eColor.JbLightGreen,
      disabled: isActionsDisabled,
    },
    Delete: {
      name: eGridRowActions.MarkAsCancelled,
      icon: eIconNames.Cancel,
      color: eColor.JbRed,
      disabled: isActionsDisabled,
    },
    Update: {
      name: eGridRowActions.UpdateCompletionDetails,
      icon: eIconNames.Setting,
      color: eColor.JbBlack,
      disabled: isActionsDisabled,
    },
    Log: {
      name: eGridRowActions.ViewIntegrationLog,
      icon: 'jibe-purchase-order',
      color: eColor.JbBlack,
    },
  };

  return [
    {
      ...actions.Confirm,
      actionFunction: ({ status }: PoJobDto) => {
        let result = [];

        switch (status) {
          case 'Pending':
            result = [actions.Confirm, actions.Delete];
            break;
          case 'Completed':
            result = [actions.Update, actions.Open, actions.Delete];
            break;
          case 'Cancelled':
            result = [actions.Confirm, actions.Open];
            break;
          default:
            result = [actions.Confirm];
        }
        return isOfficeApp ? [...result, actions.Log] : result;
      },
    },
  ];
}

const hideExportColumns: IExportAdditionalColumn[] = [
  {
    DisplayText: 'Delivery Port',
    FieldName: 'deliveryPortId',
    HideColumn: true,
  },
  {
    DisplayText: 'Service Performance',
    FieldName: 'servicePerformanceUid',
    HideColumn: true,
  },
];

const extraExportColumns: IExportAdditionalColumn[] = [
  {
    DisplayText: 'Delivery Port Name',
    FieldName: 'deliveryPort.name',
    HideColumn: false,
  },
  {
    DisplayText: 'Delivery Port Country',
    FieldName: 'deliveryPort.country',
    HideColumn: false,
  },
  {
    DisplayText: 'Service Performance',
    FieldName: 'servicePerformance.name',
    HideColumn: false,
  },
];

export const exportExtraColumns = [...hideExportColumns, ...extraExportColumns];
