import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { RolesPipe } from 'j3-prc-components';
import { QuotationModule } from '../quotation/quotation.module';
import { SharedModule } from '../shared/shared.module';
import { WorkflowModule } from '../workflow/workflow.module';
import { CreateRequisitionPopupComponent } from './create-requisition-popup/create-requisition-popup.component';
import { DuplicateRequisitionPopupComponent } from './duplicate-requisition/duplicate-requisition.component';
import { FinanceAdditionalChargesComponent } from './requisition-finance/finance-additional-charges/finance-additional-charges.component';
import { FinanceItemConfigurationComponent } from './requisition-finance/finance-item-configuration/finance-item-configuration.component';
import { FinanceTableComponent } from './requisition-finance/finance-table/finance-table.component';
import { IsMandatoryPipe } from './requisition-finance/finance-table/is-mandatory.pipe';
import { SplitPopupComponent } from './requisition-finance/finance-table/split-popup/split-popup.component';
import { RequisitionFinanceSummaryComponent } from './requisition-finance/requisition-finance-summary/requisition-finance-summary.component';
import { RequisitionFinanceComponent } from './requisition-finance/requisition-finance.component';
import { VoyageNumberPopupComponent } from './requisition-finance/voyagenumber-popup/voyagenumber-popup.component';
import { RequisitionInformationComponent } from './requisition-information/requisition-information.component';
import { RequisitionItemsComponent } from './requisition-items/requisition-items.component';
import { RequisitionPurchaseOrderComponent } from './requisition-purchase-order/requisition-purchase-order.component';
import { RequisitionSinglePageComponent } from './requisition-single-page.component';
import { CcEmailsPopupComponent } from './requisition-supplier/cc-emails-popup/cc-emails-popup.component';
import { RequisitionSupplierAttachmentsComponent } from './requisition-supplier/requisition-supplier-attachments/requisition-supplier-attachments.component';
import { RequisitionSupplierComponent } from './requisition-supplier/requisition-supplier.component';
import { SupplierItemsComponent } from './requisition-supplier/supplier-items/supplier-items.component';
import { SupplierJobsComponent } from './requisition-supplier/supplier-jobs/supplier-jobs.component';
import { SupplierPopupComponent } from './requisition-supplier/supplier-popup/supplier-popup.component';
import { SelectItemListPopupComponent } from './select-item-list-popup/select-item-list-popup.component';

@NgModule({
  declarations: [
    IsMandatoryPipe,
    CcEmailsPopupComponent,
    CreateRequisitionPopupComponent,
    DuplicateRequisitionPopupComponent,
    FinanceAdditionalChargesComponent,
    FinanceItemConfigurationComponent,
    FinanceTableComponent,
    RequisitionFinanceComponent,
    RequisitionFinanceSummaryComponent,
    RequisitionInformationComponent,
    RequisitionItemsComponent,
    RequisitionPurchaseOrderComponent,
    RequisitionSinglePageComponent,
    RequisitionSupplierAttachmentsComponent,
    RequisitionSupplierComponent,
    SelectItemListPopupComponent,
    SplitPopupComponent,
    SupplierItemsComponent,
    SupplierJobsComponent,
    SupplierPopupComponent,
    VoyageNumberPopupComponent,
  ],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    QuotationModule,
    WorkflowModule,
  ],
  exports: [RequisitionFinanceComponent],
  providers: [RolesPipe],
})
export class RequisitionSinglePageModule {}
