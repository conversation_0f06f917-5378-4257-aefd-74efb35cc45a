import {
  Column,
  eColor,
  eGridCellType,
  eIconNames,
  Filter,
  FilterListSet,
  GridRowActions,
  SearchField,
  WebApiRequest,
} from 'jibe-components';

import { createColumns, createFilters } from 'j3-prc-components';
import ODataFilterBuilder from 'odata-filter-builder';
import { GET_UPCOMING_PORTS_REQ } from '../../services/operations.service';

export const gridName = 'upcomingPorts';

export function getUpcomingPorts(vesselId?: number): WebApiRequest {
  let filter = new ODataFilterBuilder()
    .eq('statusConfig', 'Planned')
    .or(new ODataFilterBuilder().eq('statusConfig', 'Next Port'));
  if (vesselId) {
    filter = filter.eq('Vessel_ID', vesselId);
  }
  return {
    ...GET_UPCOMING_PORTS_REQ,
    odata: {
      filter,
      orderby: 'arrival desc',
      skip: '0',
      top: '100',
    },
  };
}

export const columnArray: string[] = ['port', 'country', 'arrival'];

export const columns: Column[] = createColumns<string, string>([
  ['Port Name', 'port'],
  ['Port Country', 'country'],
  [
    'Arrival',
    'arrival',
    {
      FieldType: eGridCellType.Date,
      DataType: 'datetime',
    },
  ],
  [
    'Departure',
    'departure_date',
    {
      FieldType: eGridCellType.Date,
      DataType: 'datetime',
    },
  ],
]);

export const filters: Filter[] = createFilters<string, string>(
  [
    [
      'Arrival From',
      'fromArrivalDate',
      {
        CoupleID: 3,
        CoupleLabel: 'Arrival',
      },
    ],
    [
      'Arrival To',
      'toArrivalDate',
      {
        CoupleID: 3,
        CoupleLabel: 'Arrival',
      },
    ],
    [
      'Departure From',
      'fromDepartureDate',
      {
        CoupleID: 4,
        CoupleLabel: 'Departure',
      },
    ],
    [
      'Departure To',
      'toDepartureDate',
      {
        CoupleID: 4,
        CoupleLabel: 'Departure',
      },
    ],
  ],
  {
    gridName,
    type: eGridCellType.Date,
    DataType: 'datetime',
    FieldType: eGridCellType.Date,
  }
);

export const portsSearchFields: SearchField[] = [
  { field: 'port', pattern: 'startsWith' },
  { field: 'country', pattern: 'startsWith' },
];

export const portsGridActions: GridRowActions[] = [
  {
    name: 'txt',
    icon: eIconNames.Edit,
    color: eColor.JbBlack,
  },
];

export const filtersLists: FilterListSet = {
  fromArrivalDate: {
    type: eGridCellType.Date,
    odataKey: 'arrival',
    includeFilter: true,
    dateMethod: 'ge',
  },
  toArrivalDate: {
    type: eGridCellType.Date,
    odataKey: 'arrival',
    includeFilter: true,
    dateMethod: 'le',
  },
  fromDepartureDate: {
    type: eGridCellType.Date,
    odataKey: 'arrival',
    includeFilter: true,
    dateMethod: 'ge',
  },
  toDepartureDate: {
    type: eGridCellType.Date,
    odataKey: 'arrival',
    includeFilter: true,
    dateMethod: 'le',
  },
};
