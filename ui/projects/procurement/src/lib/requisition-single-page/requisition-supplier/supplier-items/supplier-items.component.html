<jb-dialog
  [dialogContent]="supplierItemDialogContent"
  [(dialogVisible)]="supplierItemDialog"
  (closeDialog)="closeSupplierItemDialog()"
>
  <p-tabView jb-dialog-body class="supplier-item-container">
    <p-tabPanel header="Items">
      <jb-grid
        [advancedSettings]="customAdvancedSettings"
        [colData]="gridInputs.columns"
        [filterData]="gridInputs.filters"
        [filterListsSet]="gridInputs.filtersLists"
        [getStyleByContainer]="true"
        [gridName]="gridInputs.gridName"
        [searchFields]="gridInputs.searchFields"
        [showSettings]="customShowSettings"
        [sortField]="gridInputs.sortField"
        [tableDataReq]="gridInputs.request"
        (cellChange)="onSupplierItemsChange($event)"
        (rowChange)="onRowChange($event)"
      >
      </jb-grid>
    </p-tabPanel>

    <p-tabPanel header="Services">
      <prc-supplier-jobs
        [requisitionUid]="requisitionUid"
        [suppliersMap]="supplierInfoMap"
        (selectionChange)="onJobSelectionChanged($event)"
      ></prc-supplier-jobs>
    </p-tabPanel>

    <p-tabPanel header="Attachments">
      <prc-requisition-supplier-attachments
        *ngIf="suppliers?.length"
        [suppliersInfo]="suppliers"
        [requisitionUid]="requisitionUid"
        (attachmentSelectionChange)="onAttachmentSelectionChanged($event)"
      ></prc-requisition-supplier-attachments>
    </p-tabPanel>
  </p-tabView>

  <jb-dialog-footer
    jb-dialog-footer
    cancelBtnLabel="Save"
    [hideSavebutton]="true"
    (cancel)="addSupplierItems()"
  ></jb-dialog-footer>
</jb-dialog>
