import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  OnInit,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';

import {
  AccountDto,
  AdditionalChargeAccountDto,
} from '@j3-prc-catalog/dtos/account-mapping';
import { CatalogAccountDto } from '@j3-prc-catalog/dtos/catalog';
import {
  directPOCheck,
  getExportControlStatus,
  PrcFunctionCodes,
  PrcModuleCodes,
  SegmentTypes,
  supplant,
} from '@j3-procurement/dtos';
import { UpdateDeliveryInstructionDto } from '@j3-procurement/dtos/delivery-instruction';
import { EcStatus } from '@j3-procurement/dtos/export-control';
import { FinanceAdditionalChargeDto } from '@j3-procurement/dtos/finance';
import { CreateItemListDto } from '@j3-procurement/dtos/item-list';
import { ObjectCountDto } from '@j3-procurement/dtos/object-count';
import {
  ApprovePoDto,
  QuotationDto,
  QuotationItemDetails,
  QuotationItemStateDto,
} from '@j3-procurement/dtos/quotation';
import {
  CloseRequisitionDto,
  CreateRequisitionDto,
  MovePendingItemsJobsToListDto,
  MovePendingItemsJobsToRequisitionDto,
  RequisitionDetailsDto,
  RequisitionItemDto,
  RequisitionItemFinanceDto,
  RequisitionSupplierDto,
  UpdateRequisitionDto,
} from '@j3-procurement/dtos/requisition';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import {
  DeactivationGuarded,
  localToUTC,
  ModalDialogOpenParameters,
  ModalDialogService,
  NotificationService,
  UnsubscribeComponent,
  utcToLocal,
} from 'j3-prc-components';
import { TypedFormGroup } from 'j3-prc-components';
import {
  CentralizedDataService,
  eGridEvents,
  GridService,
  IJbAttachment,
  ITaskMangerDetails,
  JbControlOutputService,
  JbDatePipe,
  WebApiRequest,
} from 'jibe-components';
import { MenuItem } from 'primeng';
import {
  BehaviorSubject,
  combineLatest,
  from,
  Observable,
  of,
  Subject,
} from 'rxjs';
import {
  catchError,
  filter,
  map,
  startWith,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';

import { SegmentType } from '@j3-procurement/dtos';
import { eApiResponseType } from '../models/enums/prc-api-response-type.enum';
import {
  ePrcConfirmLabel,
  ePrcErrorMessages,
  ePrcModalMessages,
  ePrcSuccessMessages,
  ePrcWarnMessages,
} from '../models/enums/prc-messages.enum';

import { StatusLabel } from '@j3-procurement/dtos/label';
import { RfqActionType, SendRfqRequestDto } from '@j3-procurement/dtos/rfq';
import { SupplierStatus } from '@j3-procurement/dtos/supplier';
import { ActionLabel, ActionResultDto } from '@j3-procurement/dtos/workflow';
import { ePrcRequestEntity } from '../models/enums/prc-request-entity.enum';
import { ePageRoutes } from '../models/enums/prc-routes.enum';
import { ePrcWorklistType } from '../models/enums/prc-worklist-type.enum';
import { RequisitionPermission } from '../models/enums/requisition-permission.enum';
import { IDiscussionWorkflow } from '../models/interfaces/discussion.interface';
import { HeaderStatus } from '../models/interfaces/header-status';
import { HistoryGraphKey } from '../models/interfaces/history-graph-key';
import { calculateAmount } from '../quotation/quotation-evaluation/supplier-table/utils';
import { QuotationService } from '../quotation/quotation.service';
import { FeedDiscussionService } from '../services/feed-discussion.service';
import { FinanceService } from '../services/finance.service';
import { PrcHelpMaterialService } from '../services/help-button/prc-help-material.service';
import { JCDSService } from '../services/jcds.service';
import { PermissionService } from '../services/permission/permission.service';
import { PrcSharedService } from '../services/prc-shared.service';
import { RequisitionAttachmentsService } from '../services/requisition/requisition-attachments.service';
import { RequisitionService } from '../services/requisition/requisition.service';
import { SidebarMenuService } from '../services/sidebar-menu/sidebar-menu.service';
import { SpinnerService } from '../services/spinner.service';
import { SupplierService } from '../services/supplier/supplier.service';
import { CreateItemListPopupData } from '../shared/create-item-list-popup/types';
import { FormStateManager } from '../shared/form-state-manager';
import { HeaderButton } from '../shared/generic-header/header-button';
import { HeaderSection } from '../shared/generic-header/header-section';
import { RequisitionCreatedPopupData } from '../shared/requisition-created-popup/types';
import { JobActions } from '../shared/single-page-sections/job-section/types';
import { getHeaderSections } from '../shared/single-page-sub-header/utils';
import { getEnvironmentByRecordNumber } from '../utils/entity-environment';
import {
  ActionHook,
  ApprovalMatrixDetails,
  WorkflowMenuContext,
} from '../workflow/services/types';
import { WorkflowApmBtnService } from '../workflow/services/workflow-apm-btn.service';
import { closeOptions, MenuItemId } from './close-options';
import {
  CREATE_ITEM_LIST_CONFIRMATION_HEADER,
  CREATE_ITEM_LIST_CONFIRMATION_TEXT,
  headerControlKeys,
  readonlyFields,
} from './constants';
import { CreateRequisitionPopupData } from './create-requisition-popup/types';
import { gridName as supplierGridName } from './requisition-supplier/requisition-supplier-grid-inputs';
import { RouteConstants } from './router-constants';
import {
  defaultSectionEditMode,
  SectionEditMode,
  SectionKey,
} from './section-edit-mode';
import { SelectItemListPopupEvent } from './select-item-list-popup/types';
import { sidebarMenu } from './sidebar-menu';
import { statusOptions } from './status-options';
import {
  ActionValidationResult,
  RequisitionDetailsForm,
  Section,
  SendRfqEvent,
  View,
} from './types';
import {
  finalAmountByStateCalculation,
  getButtonTooltip,
  mergeNewData,
} from './utils';
import {
  bulkPurchaseFinanceValidation,
  validateApprovedSuppliers,
  validateFinance,
} from './validations';

@Component({
  selector: 'lib-requisition-single-page',
  templateUrl: './requisition-single-page.component.html',
  styleUrls: ['./requisition-single-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [JbDatePipe, PrcHelpMaterialService, WorkflowApmBtnService],
})
export class RequisitionSinglePageComponent
  extends UnsubscribeComponent
  implements OnInit, DeactivationGuarded
{
  additionalChargesData: FinanceAdditionalChargeDto[];
  parentCatalogUids$: Observable<string[]>;
  public catalogGLAccounts$ = new BehaviorSubject<
    Record<string, CatalogAccountDto[]>
  >({});
  public additionalChargeAccountsMap$ = new BehaviorSubject<
    Map<string, AccountDto[]>
  >(new Map());
  public currentView: View = 'requisition-details';

  financeItemsRequest: WebApiRequest;
  additionalChargesRequest: WebApiRequest;
  feedDetail: IDiscussionWorkflow;
  functionCode = PrcFunctionCodes.RequisitionDetails;
  itemConfigurationData: RequisitionItemFinanceDto[];
  itemData: RequisitionItemDto[];
  moduleCode = PrcModuleCodes.Procurement;
  requisition: RequisitionDetailsDto;
  requisitionUid: string;
  selecetedItemsCost: number;
  statusGraphKeys: HistoryGraphKey;
  supplierData: RequisitionSupplierDto[];
  deliveryInstructionData: UpdateDeliveryInstructionDto;
  timestamp: Date;

  public approvalMatrixDetails$: Observable<ApprovalMatrixDetails>;
  public headerButtons$: Observable<HeaderButton[]>;
  public headerStatus$: Observable<HeaderStatus>;
  public settingsOptions$: Observable<MenuItem[]>;
  public linkedRecordsDetails: ITaskMangerDetails;
  public isSendingRfq = false;

  private saveBtn: HeaderButton = {
    buttonClass: 'save',
    buttonType: 'Standard',
    command: () => this.save(),
    label: 'Save',
  };

  private duplicateMenuItem: MenuItem = {
    id: MenuItemId.Duplicate,
    label: 'Duplicate',
    visible: true,
    disabled: false,
    command: () => this.duplicateRequisitionPopup(),
  };

  private preWfActionMap = new Map<WorkflowType, ActionHook>([
    [
      'APPROVE',
      (_, forceRework) =>
        forceRework ? undefined : this.buildApproveContext(),
    ],
    [
      'CLOSE',
      (_, forceRework) => (forceRework ? undefined : this.closeRequisition()),
    ],
    [
      'REVIEW',
      (_, forceRework) => (forceRework ? undefined : this.sendForApproval()),
    ],
  ]);

  private postWfActionMap = new Map<WorkflowType, ActionHook>([
    [
      'IN PROGRESS',
      (_, forceRework) => (forceRework ? undefined : this.sendRfq()),
    ],
  ]);

  private rfqUids: string[];
  private rfqActionType: RfqActionType;
  private isSaving$ = new BehaviorSubject<boolean>(false);

  public attachmentConfig: IJbAttachment;
  public getJobsRequest: WebApiRequest;
  public isQuotationEvaluationChanged: boolean;
  public isDeliveryInstructionChanged: boolean;
  public jobPermissions: Partial<Record<JobActions, RequisitionPermission>> = {
    deleteJobs: RequisitionPermission.AddEditServices,
  };
  public createItemListPopupData: CreateItemListPopupData;
  public createRequisitionPopupData: CreateRequisitionPopupData;
  public openDuplicatePopup = false;
  public openSelectItemListPopup = false;
  public confirmationDialogParams: ModalDialogOpenParameters = {
    confirmButtonLabel: ePrcConfirmLabel.Confirm,
    text: CREATE_ITEM_LIST_CONFIRMATION_TEXT,
    jbDialog: {
      dialogHeader: CREATE_ITEM_LIST_CONFIRMATION_HEADER,
    },
  };
  public headerSections: HeaderSection[];
  public permission = RequisitionPermission;
  public requisitionCreatedPopupData: RequisitionCreatedPopupData;
  public requisitionForm: TypedFormGroup<RequisitionDetailsForm>;
  public refreshMachinery$ = new Subject();
  public readonly: boolean;
  public segmentUids: string[];
  public glAccountUids$: Observable<string[]>;
  public sectionEditMode: SectionEditMode = {
    ...defaultSectionEditMode,
    generalInformation: true,
  };
  public showGeneralInformation: boolean;
  private formStateManager: FormStateManager<RequisitionDetailsForm>;
  public reviewMode = false;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly cdService: CentralizedDataService,
    private readonly feedDiscussionService: FeedDiscussionService,
    private readonly financeService: FinanceService,
    private readonly formBuilder: FormBuilder,
    private readonly gridService: GridService,
    private readonly helpMaterialService: PrcHelpMaterialService,
    private readonly jbControlOutputService: JbControlOutputService,
    private readonly jcdsService: JCDSService,
    private readonly modalDialogService: ModalDialogService,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly prcSharedService: PrcSharedService,
    private readonly quotationService: QuotationService,
    private readonly requisitionAttachmentsService: RequisitionAttachmentsService,
    private readonly requisitionService: RequisitionService,
    private readonly router: Router,
    private readonly sidebarMenuService: SidebarMenuService<View, Section>,
    private readonly spinnerService: SpinnerService,
    private readonly supplierService: SupplierService,
    private readonly titleService: Title,
    private readonly workflowBtnService: WorkflowApmBtnService
  ) {
    super();
    this.requisitionForm = this.formBuilder.group<RequisitionDetailsForm>({
      approvalFlow: [],
      assignee: [],
      deliveryDate: [],
      deliveryPort: [{ id: null }],
      department: [],
      description: [''],
      items: [],
      jobs: [],
      poType: [null, Validators.required],
      titleNumber: [{ value: '', disabled: true }],
      totalItems: [],
      urgency: [],
    });
  }

  @HostListener('window:beforeunload')
  canDeactivate(): boolean {
    return !(this.hasViewChanges() || this.requisitionForm.dirty);
  }

  async ngOnInit(): Promise<void> {
    const { snapshot } = this.activatedRoute;
    this.requisitionUid = snapshot.params[RouteConstants.requisitionUid];
    // must be defined before calling setPageState
    this.additionalChargesRequest =
      this.financeService.getAdditionalChargesRequest(
        this.requisitionUid,
        ePrcRequestEntity.Requisition
      );
    this.financeItemsRequest = this.financeService.getFinancialItemsRequest(
      this.requisitionUid,
      ePrcRequestEntity.Requisition
    );
    this.setJobsRequest(this.requisitionUid);
    const [canEditGeneralInfo] = await Promise.all([
      this.permissionService.hasPermissions(
        RequisitionPermission.EditGeneralInfo
      ),
      this.loadRequisition(this.requisitionUid),
      this.setPageState(),
    ]);

    if (!canEditGeneralInfo) {
      this.requisitionForm.get('deliveryPort').disable();
      this.requisitionForm.get('titleNumber').disable();
    }

    this.parentCatalogUids$ =
      this.requisitionService.getRequisitionParentCatalogs(this.requisitionUid);
    this.getCatalogGLAccounts();
    this.getAdditionalChargeGLAccounts();

    this.formStateManager = new FormStateManager(
      this.requisitionForm,
      headerControlKeys
    );

    this.initWorkflow();
    this.loadSidebarMenu();

    this.setFeedDetailAndStatusGraph();
    this.setLinkedRecordsDetails();
    this.helpMaterialService.init(PrcFunctionCodes.RequisitionDetails);

    this.glAccountUids$ = this.requisitionService.pageState$.pipe(
      map(({ financialItems, additionalCharges }) => {
        const financeItems = [...financialItems, ...additionalCharges]
          .map(({ glAccountUid }) => glAccountUid)
          .filter(Boolean);
        return [...new Set(financeItems)];
      })
    );

    // adding a setTimeout with a delay because  users can quickly switch between tabs(views) before the records are inserted into the INF_UPLOAD table or refersh the page
    // Create a task for Infra team to provide response from JB Attachment component
    this.jbControlOutputService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((control) => {
        if (control.type === 'upload') {
          const uploadUids = control.payload.map((item) => item.upload_uid);
          if (uploadUids.length) {
            setTimeout(() => {
              this.requisitionAttachmentsService
                .copyRfqAttachments(this.requisitionUid)
                .toPromise();
            }, 2000);
          }
        }
      });
    this.cdr.markForCheck();
  }

  public async onCloseSelectItemListPopup(
    event: SelectItemListPopupEvent
  ): Promise<void> {
    this.openSelectItemListPopup = false;
    if (!event) {
      return;
    }

    const { itemListUid, itemListName, createNewList } = event;
    if (createNewList) {
      return this.showCreateItemListPopup();
    }

    await this.closeRequisition({
      movePendingItemsJobs: { target: 'list', itemListUid },
    });

    this.notificationService.success(
      supplant(ePrcSuccessMessages.PendingRequisitionItemsMoved, {
        itemListName,
      })
    );

    this.refreshPageData();
  }

  private async buildCloseOptions(action: ActionLabel): Promise<MenuItem[]> {
    const CLOSE_ID = 'CLOSE';
    if (action?.id !== CLOSE_ID) {
      return [];
    }

    const { additional_steps } = action.configDetails ?? {};
    const showCloseOption = additional_steps?.some(
      (step) => step.workflow_action === CLOSE_ID
    );
    const showMoveOptions =
      showCloseOption && (await this.hasPendingItems(this.requisitionUid));

    const { close, movePendingToList, movePendingToReq } = closeOptions;
    const options: MenuItem[] = [];

    if (showCloseOption) {
      options.push({ ...close, command: () => this.closeRequisition() });
    }

    if (showMoveOptions) {
      options.push(
        {
          ...movePendingToList,
          command: () => (this.openSelectItemListPopup = true),
        },
        {
          ...movePendingToReq,
          command: () => {
            this.createRequisitionPopupData = {
              deliveryDate: this.requisition.deliveryDate,
              deliveryPortId: this.requisition.deliveryPort?.id,
              description: this.requisition.description,
              poTypeUid: this.requisition.poType?.uid,
              urgencyUid: this.requisition.urgency?.uid,
              vesselUid: this.requisition.vesselUid,
            };
          },
        }
      );
    }

    return options;
  }

  private async buildHeaderMenuItems({
    action,
    state,
  }: ActionResultDto): Promise<MenuItem[]> {
    const menuItems: MenuItem[] = [];
    if (state.isRework) {
      menuItems.push({
        command: () => this.workflowBtnService.reworkAction(),
        label: 'Rework',
      });
    }
    const closeAndMoveItems = await this.buildCloseOptions(action);
    return [...menuItems, ...closeAndMoveItems];
  }

  private async closeRequisition(dto?: {
    movePendingItemsJobs:
      | MovePendingItemsJobsToListDto
      | MovePendingItemsJobsToRequisitionDto;
  }): Promise<CloseRequisitionDto> {
    const [{ clientUid }, { jcdsUrl }] = await Promise.all([
      this.prcSharedService.getClientUid().toPromise(),
      this.prcSharedService.getJCDSUrl().toPromise(),
    ]);
    const userInfo = this.cdService.userDetails;
    return this.requisitionService.closeRequisition(this.requisitionUid, {
      ...dto,
      clientUid,
      jcdsUrl,
      user: {
        userName: userInfo.User_FullName,
        userUid: userInfo.user_uid,
      },
    });
  }

  private async hasPendingItems(requisitionUid: string): Promise<boolean> {
    try {
      const count = await this.requisitionService.countPendingItemsAndJobs(
        requisitionUid
      );
      return count > 0;
    } catch ({ error, message }) {
      this.notificationService.error(error?.message || message);
    }
  }

  private showCreateItemListPopup(): void {
    const { urgency, vesselUid } = this.requisition;
    this.createItemListPopupData = { urgency, vesselUid };
  }

  public async onCloseCreateItemListPopup(
    itemList?: CreateItemListDto
  ): Promise<void> {
    if (itemList) {
      await this.closeRequisition({
        movePendingItemsJobs: { target: 'list', itemList },
      });

      this.notificationService.success(
        supplant(ePrcSuccessMessages.PendingRequisitionItemsMoved, {
          itemListName: itemList.name,
        })
      );

      this.refreshPageData();
    }

    this.createItemListPopupData = undefined;
  }

  public async onCloseCreateRequisitionPopup(
    requisition?: CreateRequisitionDto
  ): Promise<void> {
    if (requisition) {
      const { targetObjectUid, requisitionNumber } =
        await this.closeRequisition({
          movePendingItemsJobs: { target: 'requisition', requisition },
        });

      this.requisitionCreatedPopupData = {
        requisitionNumber,
        requisitionUid: targetObjectUid,
        isDirectPO: directPOCheck(requisition.poType?.type),
      };
    }

    this.createRequisitionPopupData = undefined;
  }

  public onCloseRequisitionCreatedPopup(): void {
    this.requisitionCreatedPopupData = undefined;
  }

  /**
   * Initializes the workflow for the requisition by setting up the context and configuring pre and post workflow action maps.
   * It also updates the header buttons based on the current workflow state.
   */
  private initWorkflow(): void {
    const context: WorkflowMenuContext = {
      objectUid: this.requisitionUid,
      objectNumber: this.requisition.requisitionNumber,
      vesselId: this.requisition.vesselId,
      vesselUid: this.requisition.vessel?.uid,
      wfList: ePrcWorklistType.Requisition,
    };
    this.workflowBtnService.initWorkflow({
      context,
      preRunActionMap: this.preWfActionMap,
      postRunActionMap: this.postWfActionMap,
    });
    this.setHeaderButtons();
  }

  private async loadRequisition(requisitionUid: string): Promise<void> {
    const requisition = await this.requisitionService
      .getRequisition(requisitionUid)
      .toPromise();

    const {
      deliveryDate,
      requisitionNumber,
      segmentUids,
      timestamp,
      taskUid,
      statusId,
      statusDisplayName,
      ...requisitionProps
    } = requisition;

    this.requisition = requisition;
    this.segmentUids = segmentUids;
    this.timestamp = timestamp;
    this.requisitionForm.patchValue({
      ...requisitionProps,
      deliveryDate: utcToLocal(deliveryDate),
      titleNumber: requisitionNumber,
    });
    this.headerSections = getHeaderSections(requisition.vessel?.name);
    this.titleService.setTitle(requisitionNumber);
    this.setDuplicatePermission();
  }

  private setHeaderButtons(): void {
    this.headerButtons$ = combineLatest([
      this.workflowBtnService.getWfButton(),
      this.isSaving$,
      this.requisitionService.pageState$,
    ]).pipe(
      map(([wfButton, isSaving]) => {
        const saveDisabled = isSaving || this.readonly;
        const saveBtn = { ...this.saveBtn, disabled: saveDisabled };
        if (!wfButton) {
          return [saveBtn];
        }
        const actionId = wfButton.id as WorkflowType;
        const actionValid = this.actionValid(actionId);
        const disabled =
          wfButton.disabled || actionValid !== ActionValidationResult.None;
        // customise default tooltip only when the action is invalid.
        const tooltip = getButtonTooltip(actionValid, wfButton);
        return [saveBtn, { ...wfButton, disabled, tooltip }];
      }),
      startWith([{ ...this.saveBtn, disabled: this.readonly }])
    );

    this.headerStatus$ = this.workflowBtnService.wfState$.pipe(
      filter((state) => Boolean(state)),
      tap((state) => this.updatePageMode(state?.id)),
      map(({ id, name }) => ({
        color: statusOptions[id]?.color,
        text: name ?? id,
      }))
    );

    this.approvalMatrixDetails$ = this.workflowBtnService.wfAction$.pipe(
      filter((res) => Boolean(res)),
      map(({ apm }) => {
        if (!apm?.nextApproverRoles?.length) {
          return null;
        }
        const { approvalFlowName, nextApproverRoles } = apm;
        return { approvalFlowName, nextApproverRoles };
      })
    );

    this.settingsOptions$ = this.workflowBtnService.wfAction$.pipe(
      filter((actionRes) => Boolean(actionRes)),
      switchMap((actionRes) =>
        from(this.buildHeaderMenuItems(actionRes)).pipe(
          map((menuItems) => [this.duplicateMenuItem, ...menuItems]),
          catchError(() => of([this.duplicateMenuItem]))
        )
      )
    );
  }

  private setLinkedRecordsDetails(): void {
    this.linkedRecordsDetails = {
      function_code: this.functionCode,
      module_code: this.moduleCode,
      uid: this.requisitionUid,
      Vessel_ID: this.requisition.vesselId,
      Vessel_Name: this.requisition.vessel.name,
      vessel_uid: this.requisition.vesselUid,
      WL_TYPE: ePrcWorklistType.Requisition,
    };
  }

  private async setDuplicatePermission(): Promise<void> {
    const hasPermission = await this.permissionService.hasPermissions(
      RequisitionPermission.CloseDuplicateRequisition
    );
    this.duplicateMenuItem.disabled = !hasPermission;
  }

  private async getAdditionalChargeGLAccounts(): Promise<void> {
    const result = await this.financeService
      .getAdditionalChargeGLAccounts()
      .pipe(takeUntil(this.componentDestroyed$))
      .toPromise();
    this.setAdditionalChargeGLAccounts(result);
  }

  private setAdditionalChargeGLAccounts(
    additionalChargeAccounts: AdditionalChargeAccountDto[]
  ): void {
    const chargeAccounts: Map<string, AccountDto[]> =
      additionalChargeAccounts.reduce((acc, { accounts, uid }) => {
        return acc.set(uid, accounts);
      }, new Map<string, AccountDto[]>());

    this.additionalChargeAccountsMap$.next(chargeAccounts);
  }

  private async getCatalogGLAccounts(): Promise<void> {
    const result = await this.financeService
      .getCatalogsGLAccounts(this.requisitionUid, ePrcRequestEntity.Requisition)
      .pipe(takeUntil(this.componentDestroyed$))
      .toPromise();
    this.setCatalogGLAccounts(result);
  }

  private setCatalogGLAccounts(
    catalogGLAccounts: Record<string, CatalogAccountDto[]>
  ): void {
    this.catalogGLAccounts$.next(catalogGLAccounts);
  }

  private discardViewChanges(): void {
    switch (this.currentView) {
      case 'finance':
        this.additionalChargesData = undefined;
        this.itemConfigurationData = undefined;
        break;

      case 'requisition-details':
        this.formStateManager.discardViewChanges();
        break;

      case 'suppliers':
        this.supplierData = undefined;
        break;

      case 'quotation-evaluation':
        this.isQuotationEvaluationChanged = false;
        break;

      case 'delivery-instruction':
        this.isDeliveryInstructionChanged = false;
        break;
    }
  }

  private hasViewChanges(): boolean {
    switch (this.currentView) {
      case 'finance':
        return this.isFinanceDataChanged();

      case 'requisition-details':
        return this.formStateManager.hasViewChanges();

      case 'suppliers':
        return this.isSupplierDataChanged();

      case 'quotation-evaluation':
        return this.isQuotationEvaluationChanged;

      case 'delivery-instruction':
        return this.isDeliveryInstructionChanged;

      default:
        return false;
    }
  }

  private isFinanceDataChanged(): boolean {
    return (
      this.additionalChargesData?.length > 0 ||
      this.itemConfigurationData?.length > 0
    );
  }

  private isSupplierDataChanged(): boolean {
    return this.supplierData?.length > 0;
  }

  public quotationEvaluationHasChanged(isChanged: boolean): void {
    this.isQuotationEvaluationChanged = isChanged;
  }

  public deliveryInstructionHasChanged(
    updatedDeliveryInstruction: UpdateDeliveryInstructionDto
  ): void {
    this.isDeliveryInstructionChanged = true;
    this.deliveryInstructionData = updatedDeliveryInstruction;
  }

  private updatePageMode(stateId: WorkflowType): void {
    if (!stateId) {
      return;
    }
    this.readonly = stateId === 'CLOSE';
    this.reviewMode = ['REVIEW', 'APPROVE'].includes(stateId);
    this.initializeAttachments(this.requisitionUid, this.readonly);
    if (this.readonly) {
      return this.requisitionForm.disable();
    }
    this.enableRequisitionForm();
  }

  private enableRequisitionForm(): void {
    const controlFields = Object.keys(this.requisitionForm.controls);
    controlFields.forEach((key: keyof RequisitionDetailsForm) => {
      if (!readonlyFields.includes(key)) {
        this.requisitionForm.get(key).enable();
      }
    });
  }

  getSupplierData(event): void {
    this.supplierData = event;
  }

  saveRequisitionSupplierData(): void {
    this.save(false);
  }

  updateTimestamp(timestamp: Date): void {
    this.timestamp = timestamp;
  }

  public async navigateToGeneralBrowsing(
    segmentTypes: SegmentType | SegmentType[]
  ): Promise<void> {
    if (this.requisitionForm.dirty) {
      const confirmed = await this.modalDialogService.openDialog({
        jbDialog: {
          dialogHeader: ePrcConfirmLabel.Save,
        },
        text: ePrcWarnMessages.SaveListConfirmation,
        confirmButtonLabel: ePrcConfirmLabel.Save,
        rejectButtonLabel: ePrcConfirmLabel.Cancel,
      });
      if (!confirmed) {
        return;
      }
      await this.save(false);
    }

    segmentTypes = this.requisition?.bulkPurchase
      ? SegmentTypes.Stores
      : segmentTypes;

    this.router.navigate(
      [ePageRoutes.GeneralBrowsing, 'requisition', this.requisitionUid],
      { queryParams: { segmentTypes, vesselUid: this.requisition?.vesselUid } }
    );
  }

  loadSidebarMenu(): void {
    this.sidebarMenuService.init(sidebarMenu, this.componentDestroyed$);
    this.sidebarMenuService.selectedView$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((nextView: View) => {
        if (nextView === this.currentView) {
          return;
        }
        const hasChanges = this.hasViewChanges();
        if (hasChanges) {
          if (!confirm(ePrcModalMessages.UnsavedChanges)) {
            return;
          }
          this.discardViewChanges();
        }
        this.sectionEditMode = { ...defaultSectionEditMode };
        this.currentView = nextView;
        this.cdr.markForCheck();
      });
  }

  public async sendForApproval(): Promise<void> {
    const poTypeMap = await this.jcdsService.getDataMap('poType');
    const currentPoType = poTypeMap.get(this.requisition.poType.uid)?.types;
    const selectedPoTypeField = this.requisitionForm.get('poType').value;
    const selectedPoType = poTypeMap.get(selectedPoTypeField.uid)?.types;
    const { quotationTimestamps } = this.requisitionService.pageState$.value;

    if (currentPoType !== selectedPoType) {
      this.notificationService.error(ePrcErrorMessages.PurchaseTypeChanged);
      return;
    }

    await this.saveRequisition();
    const [{ clientUid }, { jcdsUrl }] = await Promise.all([
      this.prcSharedService.getClientUid().toPromise(),
      this.prcSharedService.getJCDSUrl().toPromise(),
    ]);
    const userInfo = this.cdService.userDetails;
    await this.quotationService
      .initiateApprovalProccess(this.requisitionUid, {
        clientUid,
        jcdsUrl,
        user: {
          userName: userInfo.User_FullName,
          userUid: userInfo.user_uid,
        },
        quotationTimestamps,
      })
      .toPromise();
  }

  /**
   * Builds the context for the "APPROVE" workflow action.
   *
   * This context is passed to `workflowBtnService.runAction()` and sent as `RunActionDto.context`.
   */
  private buildApproveContext(): ApprovePoDto {
    return { approverName: this.cdService.userDetails.User_FullName };
  }

  private async setPageState(): Promise<void> {
    const [quotationList, financialItems, additionalCharges] =
      await Promise.all([
        this.quotationService.getQuotationList(this.requisitionUid).toPromise(),
        this.financeService
          .getFinancialItems(this.financeItemsRequest)
          .toPromise(),
        this.financeService
          .getAdditionalCharges(this.additionalChargesRequest)
          .toPromise(),
        this.financeService
          .getRequiredQuotationCharges(this.requisitionUid)
          .toPromise(),
      ]);

    const quotationUids = [];
    const quotations = quotationList.records ?? [];
    const suppliers: StatusLabel<SupplierStatus>[] = quotations
      .filter(({ isSelected }) => isSelected)
      .map((quotation) => {
        const { supplierUid, uid, supplierStatus, supplierRegisteredName } =
          quotation;
        quotationUids.push(uid);
        return {
          uid: supplierUid,
          name: supplierRegisteredName,
          status: supplierStatus,
        };
      });

    const { itemDetails, state: quotationItemState } = quotationUids?.length
      ? await this.quotationService
          .getQuotationEvaluationDetails({
            quotationUids,
            requisitionUid: this.requisitionUid,
          })
          .toPromise()
      : { itemDetails: {}, state: [] };

    if (quotationUids.length && quotationItemState.length) {
      this.initializeCost(
        quotationList.records,
        quotationItemState,
        itemDetails
      );
    }
    const quotationTimestamps = quotationList.records?.map(
      ({ uid, timestamp }) => ({ uid, timestamp })
    );
    this.requisitionService.pageState$.next({
      quotationItemState,
      financialItems,
      additionalCharges,
      itemDetails,
      suppliers,
      quotationTimestamps,
    });
    this.financeService.setAdditionalChargesQuotationMap(additionalCharges);
  }

  private initializeCost(
    quotationList: QuotationDto[],
    quotationItemState: QuotationItemStateDto[],
    itemDetails: Record<string, QuotationItemDetails>
  ): void {
    const quotationsAmount = this.getQuotationsAmountMap(
      quotationItemState,
      itemDetails
    );
    const finalAmount = finalAmountByStateCalculation(
      quotationList,
      quotationsAmount
    );
    this.selecetedItemsCost = finalAmount;
    this.cdr.markForCheck();
  }

  private getQuotationsAmountMap(
    quotationItemState: QuotationItemStateDto[],
    itemDetails: Record<string, QuotationItemDetails>
  ): Record<string, number> {
    return quotationItemState.reduce((acc, quotationItem) => {
      const { quotationUid, quotationItemUid, orderQty } = quotationItem;
      const currentAcc = acc[quotationUid] ?? 0;
      const {
        unPrice = 0,
        discount = 0,
        uomQty = 1,
        uomConversion = 1,
      } = itemDetails[quotationItemUid] ?? {};
      return {
        ...acc,
        [quotationUid]:
          currentAcc +
          calculateAmount(discount, unPrice, uomQty, uomConversion, orderQty),
      };
    }, {});
  }

  public setSectionEditMode(key: SectionKey): void {
    this.sectionEditMode = {
      ...defaultSectionEditMode,
      [key]: true,
    };
  }

  onSelectedItemsCostChanged(event: number): void {
    this.selecetedItemsCost = event;
    this.cdr.markForCheck();
  }

  async saveRequisition(): Promise<void> {
    if (this.requisitionForm.invalid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }
    const { totalItems, deliveryDate, titleNumber, deliveryPort, ...rest } =
      this.requisitionForm.value;

    const requisition: UpdateRequisitionDto = {
      ...rest,
      deliveryDate: localToUTC(deliveryDate),
      deliveryPort: deliveryPort ?? this.requisition.deliveryPort,
      deliveryInstruction: this.deliveryInstructionData,
      requisitionSuppliers: this.supplierData,
      requisitionItemsConfiguration: this.itemConfigurationData,
      requisitionAdditionalCharges: this
        .additionalChargesData as unknown as FinanceAdditionalChargeDto[],
      timestamp: this.timestamp,
    };

    await this.updateRequisition(requisition);
    this.requisitionForm.markAsPristine();
    this.formStateManager.updateOriginalFormValue();
    this.supplierData = undefined;
    this.deliveryInstructionData = undefined;
    this.isDeliveryInstructionChanged = false;
    const { pageState$ } = this.requisitionService;
    const { financialItems, additionalCharges } = pageState$.value;
    const financialItemsMerged = mergeNewData(
      financialItems,
      this.itemConfigurationData,
      'uid'
    );

    const additionalChargesMerged = mergeNewData(
      additionalCharges,
      this.additionalChargesData,
      'uid'
    );
    pageState$.next({
      ...pageState$.value,
      financialItems: financialItemsMerged,
      additionalCharges: additionalChargesMerged,
    });

    this.additionalChargesData = undefined;
    this.itemConfigurationData = undefined;
    this.cdr.markForCheck();
    if (directPOCheck(requisition.poType?.type)) {
      this.navigateToDirectPO();
    }
  }

  private async save(showNotification: boolean = true): Promise<void> {
    this.isSaving$.next(true);
    this.spinnerService.show();
    try {
      await this.saveRequisition();
      if (showNotification) {
        this.notificationService.success(
          ePrcSuccessMessages.RecordSavedSuccess
        );
      }
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message
      );
    } finally {
      this.isSaving$.next(false);
      this.spinnerService.hide();
    }
  }

  sendItemConfigurationData(event: RequisitionItemFinanceDto[]): void {
    this.itemConfigurationData = event;
  }

  sendAdditionalChargesData(event: FinanceAdditionalChargeDto[]): void {
    this.additionalChargesData = event;
  }

  /**
   * Validates whether the given actionId can be performed.
   * @param actionId - The actionId to be validated.
   * @returns true if the action is valid, false otherwise.
   */
  private actionValid(actionId: WorkflowType): ActionValidationResult {
    if (actionId !== 'REVIEW') {
      return ActionValidationResult.None;
    }

    const pageState = this.requisitionService.pageState$.value;
    if (!pageState) {
      return ActionValidationResult.None;
    }

    const {
      additionalCharges,
      financialItems,
      mandatoryQuotationCharges,
      quotationItemState,
      suppliers,
    } = pageState;

    this.financeService.setAdditionalChargesQuotationMap(
      additionalCharges,
      mandatoryQuotationCharges
    );

    const quotationFinanceValid = this.quotationFinanceValidation(
      financialItems,
      additionalCharges,
      quotationItemState,
      this.requisition?.bulkPurchase
    );

    if (quotationFinanceValid) {
      return quotationFinanceValid;
    }

    return validateApprovedSuppliers(suppliers)
      ? ActionValidationResult.None
      : ActionValidationResult.Suppliers;
  }

  private quotationFinanceValidation(
    financeItems: RequisitionItemFinanceDto[],
    additionalCharges: FinanceAdditionalChargeDto[],
    quotationItemState: QuotationItemStateDto[],
    isBulkPurchase?: boolean
  ): ActionValidationResult {
    if (isBulkPurchase) {
      const bulkPurchaseInValid = bulkPurchaseFinanceValidation(
        financeItems,
        additionalCharges,
        quotationItemState
      );
      if (bulkPurchaseInValid) {
        return bulkPurchaseInValid;
      }

      // for the next validation(glAccount validation), pass additionalCharges only whose amount is greater than 0
      const filteredAdditionalCharges = additionalCharges.filter(
        ({ amount }) => amount
      );
      if (filteredAdditionalCharges?.length) {
        additionalCharges = filteredAdditionalCharges;
      }
    }
    const quotationFinanceValid =
      quotationItemState.length > 0 &&
      validateFinance(financeItems) &&
      this.financeService.validateAdditionalCharges(additionalCharges);

    if (!quotationFinanceValid) {
      return ActionValidationResult.QuotationFinance;
    }

    return;
  }

  duplicateRequisitionPopup(): void {
    this.openDuplicatePopup = true;
  }

  private initializeAttachments(id: string, readonly: boolean): void {
    this.attachmentConfig = {
      Module_Code: PrcModuleCodes.Procurement,
      Function_Code: PrcFunctionCodes.RequisitionAttachments,
      Key1: id,
      // for some reason jb-components doesn't show actions by default, these are a copy from jb-attachment.json
      actions: [
        { name: 'Edit', icon: 'icons8-edit', disabled: readonly },
        { name: 'Delete', icon: 'icons8-delete', disabled: readonly },
        { name: 'Download', icon: 'icons8-download', disabled: readonly },
      ],
    };
  }

  private async refreshSegments(): Promise<void> {
    this.segmentUids = await this.requisitionService
      .getRequisitionSegments(this.requisitionUid)
      .toPromise();
    this.cdr.markForCheck();
  }

  private setFeedDetailAndStatusGraph(): void {
    const entityEnvironment = getEnvironmentByRecordNumber(
      this.requisition.requisitionNumber
    );
    this.feedDetail = this.feedDiscussionService.getDetails(
      this.requisitionUid,
      this.requisition.vesselId,
      entityEnvironment,
      ePrcWorklistType.Requisition,
      this.requisition.requisitionNumber
    );
    this.statusGraphKeys = {
      key1: this.requisitionUid,
      key2: entityEnvironment.toString(),
      key3: this.requisition.vesselId?.toString(),
    };
  }

  private setJobsRequest(requisitionUid: string): void {
    this.getJobsRequest =
      this.requisitionService.getJobsRequest(requisitionUid);
  }

  private async updateRequisition(
    requisition: UpdateRequisitionDto
  ): Promise<void> {
    const { timestamp, total } = await this.requisitionService
      .updateRequisition(this.requisitionUid, { ...requisition })
      .toPromise();

    this.timestamp = timestamp;
    this.requisition = { ...this.requisition, ...requisition };
    if (total !== undefined && total !== this.requisition.totalItems) {
      this.requisition = { ...this.requisition, totalItems: total };
      this.refreshSegments();
      this.refreshMachinery$.next(true);
    }
    this.requisitionForm.get('items').reset();
    this.requisitionForm.get('jobs').reset();
  }

  updateEcIconHeader(ecCount: ObjectCountDto): void {
    const status: EcStatus = getExportControlStatus(ecCount);
    this.requisition = { ...this.requisition, ecStatus: status };
  }

  private async refreshPageData(): Promise<void> {
    this.requisition = await this.requisitionService
      .getRequisition(this.requisitionUid)
      .toPromise();
    this.refreshSegments();
    this.refreshMachinery$.next(true);
    this.cdr.markForCheck();
  }

  public onSendRfq({ uids, actionType }: SendRfqEvent): Promise<unknown> {
    this.rfqUids = uids;
    this.rfqActionType = actionType;
    this.isSendingRfq = true;
    this.spinnerService.show(); // this one is hidden in sendRfq() or workflow action method.

    const statusId = this.workflowBtnService.getState()?.id;
    return statusId === 'RAISE'
      ? this.workflowBtnService.runAction('IN PROGRESS')
      : this.sendRfq();
  }

  private navigateToDirectPO(): void {
    this.router.navigate(
      [ePageRoutes.DirectPoSinglePage, this.requisitionUid],
      { state: { activated: true } }
    );
  }

  private async sendRfq(): Promise<void> {
    if (!this.rfqUids?.length) {
      this.spinnerService.hide();
      return;
    }
    this.isSendingRfq = true;
    try {
      const { UserID, User_FullName, user_uid } =
        this.cdService.userDetails ?? {};

      const [clientData, jcdsData, userDetails] = await Promise.all([
        this.prcSharedService.getClientUid().toPromise(),
        this.prcSharedService.getJCDSUrl().toPromise(),
        this.prcSharedService.getUserDetailsByUserId(UserID).toPromise(),
      ]);

      const { fullName, mailID, mobileNumber } = userDetails ?? {};
      const purchaserDetails = {
        name: fullName,
        email: mailID,
        contactNumber: mobileNumber,
      };

      const rfqDetails: SendRfqRequestDto = {
        rfqUids: this.rfqUids,
        clientUid: clientData?.clientUid,
        jcdsUrl: jcdsData?.jcdsUrl,
        purchaserDetails,
        user: { userName: User_FullName, userUid: user_uid },
        requisitionUid: this.requisition.uid,
        rfqActionType: this.rfqActionType,
      };
      const { requisitionTimestamp } = await this.supplierService
        .sendRFQ(rfqDetails)
        .toPromise();

      this.timestamp = requisitionTimestamp;
      this.rfqUids = [];
      this.rfqActionType = null;

      this.notificationService.success(ePrcSuccessMessages.SendRfqSuccess);
      this.gridService.refreshGrid(eGridEvents.Table, supplierGridName);
    } catch (e) {
      this.notificationService.error(e?.message);
      throw e;
    } finally {
      this.isSendingRfq = false;
      this.spinnerService.hide();
    }
  }
}
