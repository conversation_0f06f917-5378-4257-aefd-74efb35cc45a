@import "node_modules/j3-prc-components/styles/_variables";

prc-form-label {
  margin: 0px 0px $prc-form-label-margin;
  padding: 0px;

  &.form-label-details {
    margin: 0px;
  }
}

.two-columns {
  display: flex;
  gap: 70px;

  .column {
    flex: 1;
  }
}

/** 
  * TODO: To display dropdown lists outside the dialog body, use [appendTo]="body".
  * Note we don't have the latest design changes when this property is applied.
  */
:host jb-dialog::ng-deep .ui-dialog {
  overflow: auto;
  .ui-dialog-content {
    overflow: visible !important;
  }
}

.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 16px 40px;
  border-top: 1px solid var(--jbGreyBlue200);

  .cancel {
    margin-right: 8px;
  }
}

.requisition-dialog-body {
  width: 100%;
}
