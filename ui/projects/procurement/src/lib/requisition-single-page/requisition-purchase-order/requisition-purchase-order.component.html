<div class="purchase-grid">
  <jb-grid
    [advancedSettings]="customAdvancedSettings"
    [colData]="gridInputs.columns"
    [filterData]="gridInputs.filters"
    [filterListsSet]="gridInputs.filtersLists"
    [getStyleByContainer]="true"
    [gridName]="gridInputs.gridName"
    [searchFields]="gridInputs.searchFields"
    [showSettings]="advanceSettings"
    [tableDataReq]="gridInputs.request"
  ></jb-grid>
</div>
<ng-template #poTemplate let-rowData>
  <span>
    <a
      class="jb_grid_topCellValue jb-link-600-14 text-ellipsis"
      [prcNavigationLink]="[poSinglePageLink, rowData.uid]"
      [queryParams]="{ tab_title: rowData.poNumber }"
      [prcTooltip]="rowData.name"
      target="_blank"
      >{{ rowData.poNumber }}
    </a>
  </span>
</ng-template>
