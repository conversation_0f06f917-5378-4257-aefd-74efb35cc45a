<jb-grid
  [colData]="gridInputs.columns"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [isDisplayAdvancedFilter]="false"
  [setActions]="gridInputs.actions"
  [tableData]="gridInputs.data"
  (action)="onAction($event)"
  (cellChange)="onCellChange($event)"
></jb-grid>

<ng-template #itemNameTemplate let-rowData>
  <div class="item-name">
    <span
      class="text-ellipsis name"
      [prcTooltip]="rowData.itemName"
      appendTo="body"
    >
    <a class="jb_grid_topCellValue jb-link-600-14"
      target="_blank"
      [prcNavigationLink]="[itemSinglePageRoute, rowData.itemUid]"
      > {{rowData.itemName}}</a>
    </span>
    <span class="icons">
      <img
        *ngIf="rowData.criticality"
        alt="critical-icon"
        class="icons8-copyright critical-icon"
        src="assets/images/criticality.svg"
      />
      <img
        *ngIf="rowData.dg"
        alt="dg-icon"
        class="icons8-falling-objects-hazard warning-icon"
        src="assets/images/DG-icon.svg"
      />
      <img
        *ngIf="rowData.ihm"
        alt="ihm-icon"
        class="icons8-high-priority-3 info-icon"
        src="assets/images/ihm.svg"
      />
    </span>
  </div>
  <div
    *ngIf="rowData.partNumber || rowData.drawingNumber"
    class="jb-breadcrumb"
  >
    <div class="jb_grid_botCellValue text-ellipsis">
      {{ rowData.partNumber }} | {{ rowData.drawingNumber }}
    </div>
  </div>
</ng-template>

<ng-template #dgTemplate let-rowData>
  {{ rowData.dg === true ? "Yes" : "No" }}
</ng-template>

<ng-template #ihmTemplate let-rowData>
  {{ rowData.ihm === true ? "Yes" : "No" }}
</ng-template>

<ng-template #activeStatusTemplate let-rowData>
  {{ rowData.activeStatus ? "Active" : "Removed" }}
</ng-template>

<ng-template #robTemplate let-rowData>
  <span *ngIf="showDbRob; else showUpdatedRob">
    {{ rowData.rob }}
  </span>
  <ng-template #showUpdatedRob>
    <span *ngIf="(robs$ | async)?.length && rowData.itemUid as itemUid">{{
      itemUid | rob | async
    }}</span>
  </ng-template>
</ng-template>
