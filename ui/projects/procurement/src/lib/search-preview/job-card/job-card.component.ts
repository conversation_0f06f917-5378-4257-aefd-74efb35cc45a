import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
} from '@angular/core';
import { supplant, WARNING } from '@j3-procurement/dtos';
import {
  ExtNavigationService,
  ModalDialogService,
  NotificationService,
  SimpleChangesTyped,
} from 'j3-prc-components';
import {
  ePrcConfirmLabel,
  ePrcInfoMessages,
} from '../../models/enums/prc-messages.enum';

import { JobDto } from '@j3-prc-catalog/dtos/job';
import { GeneralBrowsingJobDto } from '@j3-prc-catalog/dtos/search';
import { AddJobDto } from '@j3-procurement/dtos/browsing';
import { ePrcPermission } from '../../models/enums/prc-permission.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { RequisitionPermission } from '../../models/enums/requisition-permission.enum';
import { RelName } from '../../models/interfaces/browsing/rel-type';
import { BrowsingService } from '../../services/browsing/browsing.service';
import { generateNavigationUrl } from '../../utils/generate-url';
import { JOB_ALREADY_ADDED } from '../item-card/constants';
import { PrcError } from '../types';
import { addButtonPermissionsMap } from './constants';

@Component({
  selector: 'prc-job-card',
  templateUrl: './job-card.component.html',
  styleUrls: ['./job-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class JobCardComponent implements OnInit, OnChanges {
  @Input() advanced = true;
  @Input() job: GeneralBrowsingJobDto | JobDto;

  public addButtonPermissions: ePrcPermission[] | RequisitionPermission[];
  public jobSinglePageRoute = ePageRoutes.JobSinglePage;
  public brandName: string;
  public navigationUrl?: string;

  private ownerTypeName: RelName;

  constructor(
    private browsingService: BrowsingService,
    private extNavigationService: ExtNavigationService,
    private modalDialogService: ModalDialogService,
    private notificationService: NotificationService
  ) {}

  ngOnInit(): void {
    this.ownerTypeName = this.browsingService.getOwnerTypeName();
    this.brandName =
      'brandName' in this.job
        ? this.job.brandName
        : (this.job as JobDto).brand?.name;

    this.addButtonPermissions =
      addButtonPermissionsMap[this.browsingService.owner?.rel] ?? [];
  }

  ngOnChanges({ job }: SimpleChangesTyped<this>): void {
    if (job) {
      this.navigationUrl = this.extNavigationService.transformUrl(
        generateNavigationUrl(this.job)
      );
    }
  }

  public getCatalogName = () =>
    'catalogName' in this.job ? this.job.catalogName : this.job.catalog?.name;

  public async onAddJob(replaceExisting?: boolean): Promise<void> {
    if (!this.job) {
      return;
    }

    const addJob: AddJobDto = {
      catalogUid:
        'catalogUid' in this.job ? this.job.catalogUid : this.job.catalog?.uid,
      jobUid: this.job.uid,
      replaceExisting,
    };

    try {
      await this.browsingService.addJob(addJob).toPromise();
      this.notificationService.info(
        supplant(ePrcInfoMessages.JobAddInfo, {
          ownerType: this.ownerTypeName,
        })
      );
    } catch (err) {
      await this.handleAddJobErrors(err);
    }
  }

  private async handleAddJobErrors({
    error,
    message,
  }: PrcError): Promise<void> {
    if (error?.type === WARNING) {
      const confirmed = await this.modalDialogService.openDialog({
        jbDialog: {
          dialogHeader: JOB_ALREADY_ADDED,
        },
        text: error.message,
        confirmButtonLabel: ePrcConfirmLabel.Confirm,
        rejectButtonLabel: ePrcConfirmLabel.Cancel,
      });

      if (confirmed) {
        this.onAddJob(true);
      }
    } else {
      this.notificationService.error(error?.message ?? message);
    }
  }
}
