<prc-basic-table
  class="items-table"
  [value]="quotationEvaluationItemsData"
  [columns]="columns"
  [tableProps]="tableProps"
  (toggleTable)="onToggleTableMode($event)"
  [isCollapsedMode]="collapsedMode"
  [cellTemplates]="cellTemplates"
  [expansionComponent]="expansionComponent"
  [rowKey]="getRowKeyFn"
>
</prc-basic-table>
<ng-template #runningNoTemplate let-rowData>
  <label>{{ getRunningNumber(rowData) }}</label>
</ng-template>
<ng-template #itemNameTemplate let-rowData>
  <div *ngIf="rowData">
    <div class="item-name">
      <a
        class="jb_grid_topCellValue jb-link-600-14 text-ellipsis"
        [prcNavigationLink]="[itemSinglePageRoute, rowData.itemUid]"
        [prcTooltip]="rowData.itemName"
        target="_blank"
        >{{ rowData.itemName }}</a
      >
      <div class="icons">
        <i
          *ngIf="rowData.criticality"
          class="icons8-copyright critical-icon"
        ></i>
        <i *ngIf="rowData.ihm" class="icons8-high-priority-3 info-icon"></i>
        <img
          *ngIf="rowData.dg"
          class="dangerous-goods-icon"
          src="assets/images/DG-icon.svg"
        />
      </div>
    </div>
    <div class="item-number">
      <div>{{ rowData.partNumber }}</div>
      <div class="separator" *ngIf="rowData.drawingNumber">|</div>
      <div class="drawing-number" *ngIf="rowData.drawingNumber">
        {{ rowData.itemSerialNumber }}
      </div>
    </div>
  </div>
</ng-template>
<ng-template #orderedQuantityTemplate let-rowData>
  <div
    class="order-qty-template"
    [prcTooltip]="summaryReqItemsOrderQty[rowData?.uid] || 0"
  >
    {{ summaryReqItemsOrderQty[rowData?.uid] || 0 | number : "1.0-4" }}
  </div>
</ng-template>
<ng-template #uomTemplate let-rowData>
  {{ rowData?.uomName }}
</ng-template>
<ng-template #expansionComponent let-rowData>
  <div class="remarks" *ngIf="rowData?.itemRemark as remark">
    <i class="icons8-comments remark-icon"></i>
    <div class="remark-text" class="text-ellipsis" [prcTooltip]="remark">
      {{ remark }}
    </div>
  </div>
</ng-template>

<ng-template #robTemplate let-rowData>
  <span *ngIf="showDbRob; else showUpdatedRob">
    {{ rowData.rob }}
  </span>
  <ng-template #showUpdatedRob>
    <span *ngIf="(robs$ | async)?.length && rowData.itemUid as itemUid">{{
      itemUid | rob | async
    }}</span>
  </ng-template>
</ng-template>
