<jb-dialog
  [dialogContent]="content"
  [dialogVisible]="true"
  (dialogVisibleChange)="onClose()"
>
  <ng-container jb-dialog-body [formGroup]="deliveryDetailsForm">
    <div class="column">
      <prc-form-label label="Delivery Date" [required]="true">
        <p-calendar
          formControlName="deliveryDate"
          [monthNavigator]="true"
          inputStyleClass="jb-text"
          [maxDate]="maxDateValue"
          [dateFormat]="userDateControl"
        ></p-calendar>
      </prc-form-label>
    </div>
    <div class="column">
      <prc-form-label label="Delivery Port" [required]="true">
        <prc-generic-port-select
          [portObject]="this.data.deliveryPort"
          (selectedPortChange)="onDeliveryPortChange($event)"
        ></prc-generic-port-select>
      </prc-form-label>
    </div>

    <div class="column">
      <prc-form-label label="Service Performance" [required]="true">
        <jb-single-select-dropdown
          [content]="servicePerformanceDropdown"
          (selectedSubOperation)="selectedServicePerformance($event)"
        ></jb-single-select-dropdown>
      </prc-form-label>
    </div>
    <div class="column">
      <prc-form-label label="Attachment">
        <jb-uploads
          #jbUploadsComponent
          [uploads]="attachments"
          [size]="attachments.size"
          [doCompression]="true"
          (onUploadDone)="onUploadDone($event.fileList)"
          [syncTo]="vesselId"
        ></jb-uploads>
      </prc-form-label>
    </div>
  </ng-container>
  <ng-container jb-dialog-footer>
    <jb-dialog-footer
      [isOkBtnDisabled]="deliveryDetailsForm.invalid || isSaving"
      okBtnLabel="OK"
      cancelBtnLabel="Cancel"
      (ok)="onSave()"
      (cancel)="onClose()"
    ></jb-dialog-footer>
  </ng-container>
</jb-dialog>
