import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { IdLabel, Label } from '@j3-procurement/dtos/label';
import {
  DuplicateRequisitionDto,
  RequisitionDetailsDto,
  ValidationError,
} from '@j3-procurement/dtos/requisition';
import {
  ExtNavigationService,
  localToUTC,
  ModalDialogService,
  NotificationService,
  TypedFormGroup,
} from 'j3-prc-components';
import { UnsubscribeComponent } from 'j3-prc-components';
import {
  IJbDialog,
  IJbTextArea,
  ISingleSelectDropdown,
  JbControlOutputService,
} from 'jibe-components';
import { takeUntil } from 'rxjs/operators';

import { DirectPoPermission } from '../../models/enums/direct-po-permission.enum';
import {
  ePrcConfirmLabel,
  ePrcErrorMessages,
  ePrcModalMessages,
} from '../../models/enums/prc-messages.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { ePrcWorklistType } from '../../models/enums/prc-worklist-type.enum';
import { GridInputsWithData } from '../../models/interfaces/grid-inputs';
import { Vessel } from '../../models/interfaces/vessel';
import { PermissionService } from '../../services/permission/permission.service';
import { PoTypeService } from '../../services/po/po-type.service';
import { PrcSharedService } from '../../services/prc-shared.service';
import { RequisitionService } from '../../services/requisition/requisition.service';
import { TaskManagerService } from '../../services/task-manager.service';
import { VesselService } from '../../services/vessel/vessel.service';
import { GenericPort } from '../../shared/generic-port-select/generic-port';
import { UpcomingPort } from '../../shared/upcoming-port-select/upcoming-port';
import { mapToLabel } from '../../utils/label-utils';
import { getVesselsDropdown, urgencyDropdown } from './dropdowns';
import {
  customPatternValidator,
  DuplicateRequisitionForm,
} from './duplicate-requisition-form';
import { ColumnKey, columns, gridName } from './grid-inputs';

const jcdsPropMap = new Map<string, string>([
  ['poType', 'po_types'],
  ['urgency', 'display_name'],
]);
@Component({
  selector: 'prc-duplicate-requisition-popup',
  templateUrl: './duplicate-requisition.component.html',
  styleUrls: ['./duplicate-requisition.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DuplicateRequisitionPopupComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() requisition: RequisitionDetailsDto;
  @Input() set popupOpen(isOpen: boolean) {
    this.isOpen = isOpen;
  }
  @Output() popupOpenChange = new EventEmitter<boolean>();
  @Output() requsitionDuplicated = new EventEmitter<void>();
  public isOpen = true;
  public duplicateRequisitionDialog: IJbDialog = {
    dialogHeader: 'Duplicate Requisition',
    dialogWidth: 920,
    showHeader: true,
    closableIcon: true,
  };
  public form: TypedFormGroup<DuplicateRequisitionForm>;
  public poTypeDropdown: ISingleSelectDropdown;
  public vesselDropdown: ISingleSelectDropdown;
  public urgencyDropdown = { ...urgencyDropdown, id: 'urgency-req' };

  public gridInputs: GridInputsWithData<ColumnKey, ValidationError> = {
    columns,
    gridName,
  };

  public subjectTextArea: IJbTextArea = {
    placeholder: 'Subject',
    id: 'description',
    maxlength: 200,
    rows: 6,
    tooltipValue: 'Text Range: Minimum: 1 Maximum: 2000',
  };
  public genericPort: GenericPort;

  public isDirectPO = false;
  hasCreateDirectPOAccess: boolean;

  public vessel: Vessel;

  public canDuplicate = true;

  constructor(
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private jbControlService: JbControlOutputService,
    private modalDialogService: ModalDialogService,
    private notificationService: NotificationService,
    private requsitionService: RequisitionService,
    private prcSharedService: PrcSharedService,
    private taskManagerService: TaskManagerService,
    private readonly vesselService: VesselService,
    private readonly permissionService: PermissionService,
    private readonly extNavigationService: ExtNavigationService,
    private readonly poTypeService: PoTypeService
  ) {
    super();
    this.form = this.fb.group<DuplicateRequisitionForm>({
      vessel: [null, [Validators.required]],
      poType: [null, [Validators.required]],
      urgency: [null, [Validators.required]],
      deliveryPort: [null, [Validators.required]],
      deliveryUpcomingPort: [],
      movement: [],
      departureDate: [],
      deliveryDate: [null, [Validators.required]],
      description: [
        '',
        [
          Validators.required,
          Validators.minLength(1),
          Validators.maxLength(200),
          customPatternValidator(/\S/g),
        ],
      ],
    });
  }

  async ngOnInit(): Promise<void> {
    this.hasCreateDirectPOAccess = await this.permissionService.hasPermissions(
      DirectPoPermission.CreateDirectPo
    );
    this.setPoTypeDropdown(this.requisition?.vesselUid);
    this.vesselDropdown = getVesselsDropdown();

    const poTypeCtrl = this.form.controls.poType;
    const urgencyCtrl = this.form.controls.urgency;
    const vesselCtrl = this.form.controls.vessel;

    poTypeCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((label) => {
        this.updatePoTypeDropdown({
          selectedValue: label?.uid,
        });
        this.cdr.markForCheck();
      });

    urgencyCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((label) => {
        this.updateUrgencyDropdown({
          selectedValue: label?.uid,
        });
        this.cdr.markForCheck();
      });

    vesselCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((vesselUid) => {
        this.updateVesselDropdown({ selectedValue: vesselUid });
        this.validateDuplicate(vesselUid);
      });

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ id, dataSource, selectedValue }) => {
        const controlName = id?.split('-')[0];
        const propName = jcdsPropMap.get(controlName);
        if (!propName) {
          return;
        }
        const labels: Label = mapToLabel(dataSource, selectedValue, propName);
        const ctrl = this.form.get(controlName);
        ctrl.patchValue(labels, { emitEvent: false });
        ctrl.markAsDirty();
      });

    this.vesselService
      .getVessels()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((vessels) => {
        this.vesselDropdown.dataSource = vessels;
        if (this.requisition.vesselUid) {
          this.form.controls.vessel.setValue(this.requisition.vesselUid);
        }
        this.cdr.markForCheck();
      });

    this.form.patchValue({
      poType: this.requisition.poType,
      urgency: this.requisition.urgency,
      description: this.requisition.description,
    });

    this.cdr.markForCheck();
  }

  private async validateDuplicate(vesselUid: string): Promise<void> {
    const body = await this.prepareBody(
      vesselUid,
      this.form.value,
      this.genericPort
    );
    const validationRes =
      await this.requsitionService.getDuplicateRequisitionValidation(
        this.requisition.uid,
        body
      );
    this.gridInputs.data = validationRes.errors;
    this.canDuplicate = validationRes.canDuplicate;
    this.cdr.markForCheck();
  }

  public onDeliveryPortChange(event: GenericPort): void {
    if (!event) {
      return;
    }

    if (
      (this.genericPort &&
        this.genericPort.Port_ID !== event?.PORT_ID &&
        this.genericPort.PORT_ID !== event?.PORT_ID) ||
      !this.genericPort
    ) {
      this.form.get('deliveryUpcomingPort').reset();
      const port: GenericPort = {
        PORT_ID: event.PORT_ID,
        port_country: event.port_country,
        port_name: event.port_name,
      };
      this.genericPort = port;
      this.form.patchValue({ departureDate: null, movement: 'other' });
    }
  }

  public onUpcomingPortChange(event: UpcomingPort): void {
    if (!event) {
      return;
    }
    const port: GenericPort = {
      PORT_ID: event.Port_ID,
      port_country: event.country,
      port_name: event.Port_Name,
    };
    this.genericPort = port;

    if (event.arrival) {
      this.form.get('deliveryDate').setValue(new Date(event.arrival));
    }
    if (event.departure_date) {
      this.form.patchValue({ departureDate: new Date(event.departure_date) });
    }
    this.form.patchValue({ movement: 'port' });
    this.form
      .get('deliveryPort')
      .setValue(port.PORT_ID, { emitViewToModelChange: false });
  }

  private async prepareBody(
    vesselUid: string,
    form: DuplicateRequisitionForm,
    genericPort: GenericPort
  ): Promise<DuplicateRequisitionDto> {
    const { deliveryPort, deliveryDate, deliveryUpcomingPort, ...rest } = form;

    const [vesselDetails, { clientUid }] = await Promise.all([
      this.vesselService.getVesselDetailsByUid(vesselUid).toPromise(),
      this.prcSharedService.getClientUid().toPromise(),
    ]);

    return {
      ...rest,
      deliveryDate: localToUTC(deliveryDate),
      deliveryPort: this.buildPortLabel(deliveryPort, genericPort),
      vessel: {
        uid: vesselUid,
        name: vesselDetails.vesselName,
        shortName: vesselDetails.shortName,
        vesselId: vesselDetails.vesselId,
        managementCompanyCode: vesselDetails.managementCompanyCode,
        managementCompanyName: vesselDetails.managementCompanyName,
        vesselClass: vesselDetails.vesselClass,
        vesselHullNo: vesselDetails.vesselHullNo,
        yardNumber: vesselDetails.yardNumber,
        vesselName: vesselDetails.vesselName,
        imoNo: vesselDetails.imoNo,
        vesselOwnerCode: vesselDetails.vesselOwnerCode,
        vesselOwnerName: vesselDetails.vesselOwnerName,
        vesselManagerName: vesselDetails.vesselManagerName,
        vesselManagerPhoneNo: vesselDetails.vesselManagerPhoneNo,
        vesselManagerEmail: vesselDetails.vesselManagerEmail,
      },
      clientUid,
    };
  }

  public async onSave(): Promise<void> {
    if (!this.form.valid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }

    const isConfirmed = await this.modalDialogService.openDialog({
      confirmButtonLabel: ePrcConfirmLabel.Confirm,
      text: ePrcModalMessages.DuplicateRequisitionPopup,
      jbDialog: {
        dialogHeader: ePrcModalMessages.DuplicateRequisition,
      },
    });

    if (!isConfirmed) {
      return;
    }

    try {
      const vesselUid = this.vessel.uid;
      const body = await this.prepareBody(
        vesselUid,
        this.form.value,
        this.genericPort
      );

      const { requisitionUid, requisitionNumber } = await this.requsitionService
        .duplicateRequisition(this.requisition.uid, body)
        .toPromise();
      this.redirectToRequisition(requisitionUid);

      this.taskManagerService.createWorkflows(
        [{ uid: requisitionUid, jobCardNo: requisitionNumber }],
        vesselUid,
        body.deliveryDate,
        ePrcWorklistType.Requisition,
        body.description
      );
      this.closeDuplicateRequisitionPopup();
    } catch ({ error, message }) {
      this.notificationService.error(error?.message ?? message);
    }
  }

  public onCancel(): void {
    this.isOpen = false;
  }

  public closeDuplicateRequisitionPopup(): void {
    this.isOpen = false;
    this.popupOpenChange.emit(false);
    this.cdr.markForCheck();
  }

  private buildPortLabel(portId: number, genericPort: GenericPort): IdLabel {
    if (portId) {
      const { port_country: country, port_name: name } = genericPort ?? {};
      return { id: portId, name, country };
    }
  }

  private updatePoTypeDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.poTypeDropdown = {
      ...this.poTypeDropdown,
      ...config,
    };
  }

  private updateUrgencyDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.urgencyDropdown = {
      ...this.urgencyDropdown,
      ...config,
    };
  }

  private updateVesselDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.vessel = this.vesselService.getVesselByUid(config.selectedValue);
    this.vesselDropdown = {
      ...this.vesselDropdown,
      ...config,
    };
    this.setPoTypeDropdown(this.vessel?.uid);
  }

  private redirectToRequisition(requisitionUid: string): void {
    this.extNavigationService.navigate([
      ePageRoutes.RequisitionSinglePage,
      requisitionUid,
    ]);
  }

  private async setPoTypeDropdown(vesselUid: string): Promise<void> {
    /**
     * TODO: Fix the duplicate requisition API to correctly create a direct-PO when specified.
     * Currently, direct-PO creation is disabled for duplicate requisitions.
     */
    const poTypeDropdown = await this.poTypeService.getPoTypeDropdown(
      vesselUid,
      true /* excludeDirectPoType */
    );

    this.poTypeDropdown = { ...this.poTypeDropdown, ...poTypeDropdown };
    this.cdr.markForCheck();
  }
}
