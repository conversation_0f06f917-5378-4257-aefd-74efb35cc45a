import { IdLabel, Label } from '@j3-procurement/dtos/label';
import { DeliveryStatus, PoJobStatus } from '@j3-procurement/dtos/po';
import { jobActions } from '../../shared/single-page-sections/job-section/grid-constants';

export type ColumnKey =
  | 'action'
  | 'attachment'
  | 'brandName'
  | 'catalogName'
  | 'catalogPath'
  | 'code'
  | 'deliveredQuantity'
  | 'department'
  | 'description'
  | 'dueDate'
  | 'jobName'
  | 'modelNumber'
  | 'pendingQuantity'
  | 'runningNumber'
  | 'orderedQuantity'
  | 'undeliveredQuantity'
  | 'status'
  | 'deliveryDate'
  | 'deliveryPortId'
  | 'servicePerformance'
  | 'servicePerformanceUid';

export type JobActions = (typeof jobActions)[number];

export interface JobActionDetails {
  deliveryUid: string;
  action: DeliveryStatus;
}

export interface DeliveryDetails {
  attachments?: Boolean;
  uids: string[];
  servicePerformance?: Label;
  deliveryDate?: Date;
  deliveryPort?: IdLabel;
  status?: PoJobStatus;
  deliveryUid?: string;
  newStatusId?: string;
}

export type DeliveryDetailsForm = Pick<
  DeliveryDetails,
  'servicePerformance' | 'deliveryDate' | 'deliveryPort' | 'attachments'
>;
