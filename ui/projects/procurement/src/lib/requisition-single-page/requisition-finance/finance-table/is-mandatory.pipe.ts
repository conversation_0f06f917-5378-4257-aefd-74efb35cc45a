import { Pipe, PipeTransform } from '@angular/core';
import { AdditionalChargeKey } from '@j3-procurement/dtos/finance';
import { FinanceService } from '../../../services/finance.service';

@Pipe({ name: 'isMandatory', pure: true })
export class IsMandatoryPipe implements PipeTransform {
  constructor(private readonly financeService: FinanceService) {}

  transform = (additionalCost: AdditionalChargeKey): boolean =>
    this.financeService.isAdditionalChargeRequired(additionalCost);
}
