import { FinanceAdditionalChargeDto } from '@j3-procurement/dtos/finance';
import { StatusLabel } from '@j3-procurement/dtos/label';
import { QuotationItemStateDto } from '@j3-procurement/dtos/quotation';
import { RequisitionItemFinanceDto } from '@j3-procurement/dtos/requisition';
import { buttonTooltip } from './constants';
import { ActionValidationResult } from './types';

export function bulkPurchaseFinanceValidation(
  financeItems: RequisitionItemFinanceDto[],
  additionalCharges: FinanceAdditionalChargeDto[],
  quotationItemState: QuotationItemStateDto[]
): ActionValidationResult {
  if (validateFinanceItemSplit(financeItems)) {
    return ActionValidationResult.FinanceItemsSplit;
  }
  if (validateAdditionalChargesSplit(additionalCharges)) {
    return ActionValidationResult.FinanceAdditionalChargesSplit;
  }
  if (financeItemsQtysValidate(financeItems, quotationItemState)) {
    return ActionValidationResult.QuotationFinanceItemQty;
  }

  return;
}

function financeItemsQtysValidate(
  financeItems: RequisitionItemFinanceDto[],
  quotationItems: QuotationItemStateDto[]
): boolean {
  const financeItemsQtyMap = financeItems.reduce(
    (acc, { recordUid, runningNumber, orderQty }) => {
      const accQty = acc.get(recordUid) ?? {
        totalQty: 0,
        runningNumbers: [],
      };
      accQty.totalQty += +orderQty;
      accQty.runningNumbers.push(runningNumber);
      return acc.set(recordUid, accQty);
    },
    new Map()
  );

  const errors: string[] = [];

  quotationItems.forEach(({ itemUid, orderQty }) => {
    const financeItem = financeItemsQtyMap.get(itemUid);
    if (financeItem && +financeItem.totalQty.toFixed(2) != orderQty) {
      errors.push(
        `${financeItem.runningNumbers.join(
          ','
        )} total quantity must be equal to ${orderQty}. `
      );
    }
  });

  if (errors?.length) {
    const errMsg = 'Financial configuration lines';
    buttonTooltip.set(
      ActionValidationResult.QuotationFinanceItemQty,
      `${errMsg} ${errors.join('\n')}`
    );
  }

  return Boolean(errors.length);
}

export function validateApprovedSuppliers(list: StatusLabel[]): boolean {
  return list.length && list.every(({ status }) => status === 'Approved');
}

function validateAdditionalChargesSplit(
  additionalCharges: FinanceAdditionalChargeDto[]
): boolean {
  const quotatedAdditionalCharges = additionalCharges.filter(
    ({ amount }) => amount
  );

  if (!quotatedAdditionalCharges?.length) {
    return false;
  }

  return quotatedAdditionalCharges.some(
    ({ amount, vesselUid }) => !amount || !vesselUid
  );
}

function validateFinanceItemSplit(list: RequisitionItemFinanceDto[]): boolean {
  const bulkItems = list.filter(({ orderQty }) => orderQty);
  if (!bulkItems?.length) {
    return true;
  }
  return bulkItems.some(({ orderQty, vesselUid }) => !orderQty || !vesselUid);
}

export function validateFinance(
  list: (FinanceAdditionalChargeDto | RequisitionItemFinanceDto)[] = []
): boolean {
  return list.length && list.every(({ glAccount }) => glAccount?.uid);
}
