import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { AccountDto } from '@j3-prc-catalog/dtos/account-mapping';
import { CatalogAccountDto } from '@j3-prc-catalog/dtos/catalog';
import { Maybe } from '@j3-prc-shared/dtos';
import { BudgetDto } from '@j3-procurement/dtos/budgets';
import { RequisitionItemFinanceDto } from '@j3-procurement/dtos/requisition';
import {
  Column,
  eGridCellType,
  eGridEvents,
  GridAction,
  GridService,
  WebApiRequest,
} from 'jibe-components';
import { filter, map, switchMap, takeUntil } from 'rxjs/operators';

import { NotificationService, UnsubscribeComponent } from 'j3-prc-components';
import { from, Observable, of, Subject } from 'rxjs';
import {
  ePrcSinglePageGridNames,
  REQUISITION_PROCUREMENT_TYPE,
} from '../../../models/enums/prc-single-page.enum';
import { RequisitionPermission } from '../../../models/enums/requisition-permission.enum';
import { AnalysisCode } from '../../../models/interfaces/accounting/finance';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../../models/interfaces/grid-inputs';
import { JbDropdownOption } from '../../../models/interfaces/jb-dropdown-option';
import { DirectPOService } from '../../../services/direct-po/direct-po.service';
import { FinanceService } from '../../../services/finance.service';
import { PermissionService } from '../../../services/permission/permission.service';
import {
  FinanceItemAction,
  getActions,
  getColumns,
  gridName,
  searchFields,
  sortField,
} from './grid-inputs';
import { ColumnKey, ItemFinancesUpdate, PurchaseRequestType } from './types';
import { generateGLAccountKey } from './utils';

// TODO: refactor to reuse bindListToDropdown
@Component({
  selector: 'prc-finance-item-configuration',
  templateUrl: './finance-item-configuration.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinanceItemConfigurationComponent
  extends UnsubscribeComponent
  implements OnInit, OnDestroy
{
  @Input() bulkPurchase: boolean;
  @Input() deliveryDate: Date;
  @Input() isBudgetReadOnly: boolean;
  @Input() isPOIssued: boolean;
  @Input() financeItemsRequest: WebApiRequest;
  @Input() procurementType: PurchaseRequestType;
  @Input() set readonly(value: boolean) {
    this.readonlyMode = value;
    this.setColumnsEditable();
  }
  @Input() vesselUid: string;
  @Input() catalogGLAccounts: Record<string, CatalogAccountDto[]> = {};
  @Output() getItemConfigurationData = new EventEmitter<ItemFinancesUpdate[]>();
  data: RequisitionItemFinanceDto[] = [];
  saveFinanceItemsData: Map<string, ItemFinancesUpdate> = new Map();
  private hasFinancialConfigurationAccess = false;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, RequisitionItemFinanceDto>;
  public selectedAccountsMap: Map<string, AccountDto[]>;
  readonlyMode: boolean;
  public splitItemUid: string;
  private matrixDataChangedSubject = new Subject<RequisitionItemFinanceDto[]>();
  private paginationFirst = 0;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly financeService: FinanceService,
    private readonly gridService: GridService,
    private readonly permissionService: PermissionService,
    private readonly directPOService: DirectPOService,
    private readonly notificationService: NotificationService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const isRequisition = this.procurementType === REQUISITION_PROCUREMENT_TYPE;
    this.gridInputs = {
      actions: getActions(this.bulkPurchase && isRequisition),
      columns: getColumns(this.bulkPurchase),
      gridName,
      searchFields,
      sortField,
      request: this.financeItemsRequest,
    };

    this.setEditTableDropDownValues();
    await this.setHasFinancialConfigurationAccess();
    this.setReadOrEditableColumns();
    this.setBudgets();
  }

  private loadBudgetsAndCodes(): Observable<RequisitionItemFinanceDto[]> {
    return this.matrixDataChangedSubject.pipe(
      filter((matrixValues) => matrixValues?.length > 0),
      switchMap((matrixValues): Observable<RequisitionItemFinanceDto[]> => {
        const glAccountUids = this.getUniqueGLAccounts(matrixValues)?.filter(
          (accounts) => accounts?.length
        );
        if (!glAccountUids?.length) {
          return of([]);
        }
        const loadBudgets$ = this.bulkPurchase
          ? this.financeService.loadBulkPOBudgets(
              this.deliveryDate,
              glAccountUids,
              matrixValues,
              this.bulkPurchase
            )
          : from(
              this.financeService.loadBudgetsAndCodes({
                vessel_uid: this.vesselUid,
                delivery_date: this.deliveryDate,
                gl_accounts: glAccountUids,
              })
            );

        return loadBudgets$.pipe(map(() => matrixValues));
      })
    );
  }

  private setBudgets(): void {
    if (this.isPOIssued) {
      return;
    }

    this.gridService.storeState$
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter(
          (state) =>
            state.gridName === this.gridInputs.gridName &&
            state.type === eGridEvents.Pagination
        )
      )
      .subscribe(({ payload: { first } }) => {
        this.paginationFirst = first ?? 0;
      });

    this.loadBudgetsAndCodes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((matrixValues) =>
        !this.isBudgetReadOnly
          ? this.bindBudgetAndCodes(matrixValues, this.paginationFirst)
          : null
      );
  }

  private getSelectedAccounts(data: RequisitionItemFinanceDto[]): void {
    if (this.catalogGLAccounts) {
      this.selectedAccountsMap = new Map<string, AccountDto[]>(
        data?.map((item) => [
          item.uid,
          this.catalogGLAccounts[item.requisitionItemUid],
        ])
      );
    }
  }

  private async setHasFinancialConfigurationAccess(): Promise<void> {
    this.hasFinancialConfigurationAccess =
      await this.permissionService.hasPermissions(
        RequisitionPermission.FinancialConfiguration
      );
  }

  setReadOrEditableColumns(): void {
    const isDirectPODisabled = this.directPOService.isReadonly;
    this.gridInputs.columns.forEach((column) => {
      if (column.hasOwnProperty(eGridCellType.Editable)) {
        const editableBudgetColumn =
          column.FieldName === 'budgetUid' &&
          !(this.isBudgetReadOnly || this.isPOIssued);
        column.Editable =
          !this.readonlyMode &&
          (this.procurementType === REQUISITION_PROCUREMENT_TYPE ||
            editableBudgetColumn) &&
          !isDirectPODisabled &&
          this.hasFinancialConfigurationAccess;
      }
      if (column.FieldName === 'orderQty') {
        column.Editable =
          this.bulkPurchase &&
          this.procurementType === REQUISITION_PROCUREMENT_TYPE;
      }
    });
  }

  getUniqueGLAccounts(gridData: RequisitionItemFinanceDto[]): string[] {
    this.getSelectedAccounts(gridData);
    const glAccounts = Object.values(this.catalogGLAccounts ?? {}).reduce<
      string[]
    >((acc, val) => [...acc, ...val.map(({ accountUid }) => accountUid)], []);
    return [
      ...new Set([...glAccounts, ...this.getGLAccountsFromGrid(gridData)]),
    ];
  }

  getGLAccountsFromGrid(gridData: RequisitionItemFinanceDto[]): string[] {
    const glAccountUids = gridData
      ?.map(({ glAccount }) => glAccount?.uid)
      .filter(Boolean);
    return glAccountUids?.length ? [...new Set(glAccountUids)] : [];
  }

  private bindBudgetAndCodes(
    gridData: RequisitionItemFinanceDto[],
    paginationFirst: number
  ): void {
    const budgetMap: Map<string, BudgetDto[]> =
      this.financeService.accountBudgets;

    const codeMap: Map<string, AnalysisCode[]> =
      this.financeService.analisysCodes;

    gridData?.forEach((item, i) => {
      const { glAccountUid, vesselUid } = item;
      const glAccountKey = generateGLAccountKey(
        glAccountUid,
        vesselUid,
        this.bulkPurchase
      );

      const budgets = budgetMap?.get(glAccountKey);
      const codes = codeMap?.get(item?.glAccountUid);
      const rowIndex = paginationFirst + i;

      const editBudgetList = budgets?.map(({ budget_name, uid }) => ({
        label: budget_name,
        value: uid,
      }));
      const editCodeList = codes?.map(({ analysisCode, uid }) => ({
        label: analysisCode,
        value: uid,
      }));
      this.bindListToDropdown('analysisCodeUid', editCodeList, rowIndex);
      this.bindListToDropdown('budgetUid', editBudgetList, rowIndex);
    });
  }

  private bindListToDropdown(
    columnName: keyof RequisitionItemFinanceDto,
    list: JbDropdownOption[],
    rowIndex: number
  ): void {
    const listColumn = this.getColumn(columnName);
    this.gridService.storeCellData[listColumn.DisplayText] = null;

    this.gridService.gridCellListChangeReq.next({
      gridName: this.gridInputs.gridName,
      columnFieldName: columnName,
      rowIndex,
      dataSet: list,
      dontChangeAll: true,
      setListEmpty: !list?.length,
      detectChanges: true,
    });

    this.cdr.markForCheck();
  }

  getEditableData(rowData: ItemFinancesUpdate): void {
    this.saveFinanceItemsData.set(rowData.uid, rowData);
    this.getItemConfigurationData.emit([...this.saveFinanceItemsData.values()]);

    const newMatrixValues = this.data?.map((item) =>
      item.uid === rowData.uid ? { ...item, ...rowData } : item
    );
    this.data = newMatrixValues;
    const { poPageState$ } = this.financeService;
    poPageState$.next({
      ...poPageState$.value,
      financialItems: newMatrixValues,
    });
  }

  private getColumn(columnName: string): Maybe<Column> {
    return this.gridInputs.columns.find((col) => col.FieldName === columnName);
  }

  private setColumnsEditable(): void {
    if (!this.gridInputs) {
      return;
    }
    this.gridInputs.columns = this.gridInputs?.columns?.map((column) =>
      column.hasOwnProperty(eGridCellType.Editable)
        ? { ...column, Editable: !this.readonlyMode }
        : column
    );
    this.gridInputs = { ...this.gridInputs };
    setTimeout(() => {
      this.gridService.refreshGrid(
        eGridEvents.StaticColumns,
        this.gridInputs.gridName
      );
      this.cdr.markForCheck();
    });
  }

  private setEditTableDropDownValues(): void {
    const projectColumn = this.getColumn('projectUid');
    this.gridService.setEditTableDropDownValue(projectColumn);
  }

  public handleMatrixDataChange(
    matrixValues: RequisitionItemFinanceDto[]
  ): void {
    if (!matrixValues?.length) {
      return;
    }
    this.data = matrixValues;
    const { poPageState$ } = this.financeService;
    poPageState$.next({
      ...poPageState$.value,
      financialItems: matrixValues,
    });

    this.matrixDataChangedSubject.next(matrixValues);
  }

  public onAction(
    actions: GridAction<FinanceItemAction, RequisitionItemFinanceDto>
  ): void {
    if (actions.type !== 'Split Between Vessel') {
      return;
    }
    this.splitItemUid = actions.payload.uid;
    this.cdr.markForCheck();
  }

  public async onSplit(vesselUids: string[]): Promise<void> {
    try {
      await this.financeService
        .splitItem(this.splitItemUid, { vesselUids })
        .toPromise();
      this.closeSplitDialog(true);
    } catch ({ error, message }) {
      this.notificationService.error(error?.message ?? message);
    }
  }
  public closeSplitDialog(refreshGrid?: boolean): void {
    this.splitItemUid = undefined;
    if (refreshGrid) {
      this.gridService.refreshGrid(
        eGridEvents.Table,
        ePrcSinglePageGridNames.FinanceItemConfigGrid
      );
    }
  }
}
