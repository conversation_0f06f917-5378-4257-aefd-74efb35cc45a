<jb-details-layout-figma [formGroup]="poFormGroup">
  <prc-generic-header
    alayout-header
    formControlName="titleNumber"
    [buttons]="headerButtons$ | async"
    [editMode]="sectionEditMode.headerDetails"
    [readonly]="readonly"
    [sections]="headerSections"
    [settingsOptions]="settingsOptions$ | async"
    [status]="headerStatus$ | async"
    (click)="setSectionEditMode('headerDetails')"
  >
    <prc-single-page-sub-header
      *ngIf="po"
      isDisable="purchaseOrder"
      [dropdownDetails]="po"
      [poEditGeneralInfo]="canEditGeneralInfo"
      [isEditMode]="sectionEditMode.headerDetails"
      [permission]="permission"
      [readonly]="readonly"
    ></prc-single-page-sub-header>
  </prc-generic-header>
  <div class="section main" alayout-content>
    <jb-layout-widget
      *ngIf="currentView === 'purchase-order-details'"
      id="general-information"
      titleText="General Information"
      titleIconClass="icons8-resume-template"
      (click)="setSectionEditMode('generalInformation')"
    >
      <prc-po-information
        awidget-content
        [isEditMode]="sectionEditMode.generalInformation"
        [segmentUids]="segmentUids"
        [po]="po"
      ></prc-po-information>
    </jb-layout-widget>

    <jb-layout-widget
      *ngIf="currentView === 'purchase-order-details'"
      id="delivery-instruction"
      titleText="Delivery Instruction"
      (click)="setSectionEditMode('deliveryInstruction')"
    >
      <prc-delivery-instruction
        awidget-content
        [objectUid]="poUid"
        [poIssueUid]="poIssueUid"
        [readonly]="po?.statusId != 'RAISE' || !canEditDeliveryInstructionInfo"
        [slfPermission]="po?.slfPermission"
        (deliveryInstructionData)="sendDeliveryInstructionData($event)"
      >
      </prc-delivery-instruction>
    </jb-layout-widget>

    <prc-po-order-status
      *ngIf="currentView === 'purchase-order-details'"
      [hideLink]="!isPOIssued"
      [poIssueUid]="poIssueUid"
      [poUid]="poUid"
      [readonly]="readonly || !isPOIssued"
      [statusBranch]="statusBranch$ | async"
      [vesselUid]="po?.vesselUid"
      [canUpdateOrderStatus]="canUpdateOrderStatus"
      [slfPermission]="po?.slfPermission"
    ></prc-po-order-status>

    <jb-layout-widget
      *ngIf="currentView === 'items'"
      #items
      titleText="Items in PO"
      titleIconClass="icons8-clipboard"
    >
      <prc-po-item
        awidget-content
        [poUid]="poUid"
        [poItemsRequest]="poItemsRequest"
        [readonly]="readonly"
        [slfPermission]="po?.slfPermission"
        [vesselUid]="po?.vesselUid"
        (ecStatusChange)="onEcStatusChanged($event)"
      ></prc-po-item>
    </jb-layout-widget>

    <jb-layout-widget
      *ngIf="currentView === 'items' && !po?.bulkPurchase"
      id="jobs"
      titleText="Services"
      titleIconClass="icons8-user-2"
      (click)="setSectionEditMode('jobs')"
    >
      <prc-po-job
        awidget-content
        class="po-services"
        [getJobsRequest]="getJobsRequest"
        [isEditMode]="sectionEditMode.jobs"
        [isGridActionsDisabled]="isPOIssued && canEditService"
        [poUid]="poUid"
        [readonly]="readonly"
        [vesselId]="po?.vesselId"
        (actionChange)="onJobActionChange($event)"
      ></prc-po-job>
    </jb-layout-widget>

    <jb-inventory-delivery
      *ngIf="
        currentView === 'items' || currentView === 'purchase-order-details'
      "
      id="delivery-records"
      [poUid]="poUid"
      [disabled]="
        readonly || !(isUpdateDeliveryVisible && hasPoItems && canAddDelivery)
      "
      (deliveryChanged)="onDeliveryChangeHandler($event)"
    ></jb-inventory-delivery>

    <div #Finance>
      <prc-requisition-finance
        *ngIf="currentView === 'finance'"
        [bulkPurchase]="po?.bulkPurchase"
        [objectUid]="poUid"
        [vesselUid]="po?.vesselUid"
        [deliveryDate]="po?.deliveryDate"
        [isPOIssued]="isPOIssued"
        [isBudgetReadOnly]="!canEditFinanceConfiguration || po?.parentPoUid"
        prcType="purchaseOrder"
        [financeItemsRequest]="financeItemsRequest"
        [additionalChargesRequest]="additionalChargesRequest"
        (sendItemConfigurationData)="sendItemConfigurationData($event)"
        (sendAdditionalChargesData)="sendAdditionalChargesData($event)"
      ></prc-requisition-finance>
    </div>

    <jb-layout-widget
      *ngIf="currentView === 'attachment' && attachmentConfig"
      #attachment
      id="attachments"
      titleText="Attachments"
      titleIconClass="icons8-resume-template"
    >
      <jb-button
        awidget-header-buttons
        label="Attach file"
        type="NoButton"
        [disabled]="readonly || !canAddEditAttachment"
        (click)="openAttachmentsDialog()"
      ></jb-button>

      <jb-attachments
        awidget-content
        #attachments
        [attachConfig]="attachmentConfig"
        [actionRow]="attachmentConfig.actions"
        [syncTo]="po?.vesselId.toString()"
      ></jb-attachments>
    </jb-layout-widget>

    <jb-layout-widget
      *ngIf="currentView === 'item-certificates'"
      titleText="Item Certificates"
    >
      <jb-button
        awidget-header-buttons
        [disabled]="readonly || !canAddCertificate"
        type="NoButton"
        label="Add Certificate"
        (click)="openAddCertificateDialog()"
      ></jb-button>

      <div awidget-content>
        <prc-po-item-certificates
          [event$]="poCertificateEvent$"
          [poUid]="poUid"
          [readonly]="readonly"
          (detailsIconClick)="addCertificate.showAddCertificatePopup($event)"
        ></prc-po-item-certificates>
      </div>
    </jb-layout-widget>

    <jb-layout-widget *ngIf="currentView === 'audit-trail'">
      <jb-event-history
        awidget-content
        [Key1]="poUid"
        [ModuleCode]="moduleCode"
        [FunctionCode]="functionCode"
        [statusGraphKeys]="statusGraphKeys"
      ></jb-event-history>
    </jb-layout-widget>

    <jb-layout-widget
      *ngIf="currentView === 'linked-records'"
      id="linked-records"
      titleText="Linked Records"
      titleIconClass="icons8-clipboard"
    >
      <jb-tm-linked-records
        *ngIf="linkedRecordsDetails"
        awidget-content
        [hideHeader]="true"
        [taskManagerDetails]="linkedRecordsDetails"
        taskType="supplier_invoice"
      ></jb-tm-linked-records>
    </jb-layout-widget>
  </div>

  <jb-discussion-feed
    *ngIf="feedDetail"
    alayout-aside
    class="feed-discussion"
    [detail]="feedDetail"
    [canAdd]="true"
    [function_code]="functionCode"
    [module_code]="moduleCode"
    [vessel_uid]="po?.vesselUid"
  ></jb-discussion-feed>
</jb-details-layout-figma>

<jb-document-preview
  [documentContent]="documentContent"
  [(isDocumentPreview)]="isDocumentPreview"
  [modal]="true"
></jb-document-preview>

<jb-integration-log
  *ngIf="viewIntegrationLog"
  [dialogVisible$]="showIntegrationLog$"
  [functionCode]="viewIntegrationLog.functionCode"
  [moduleCode]="viewIntegrationLog.moduleCode"
  [entityUid]="viewIntegrationLog.entityUid"
></jb-integration-log>

<prc-raise-po-popup
  *ngIf="isShowRaisePurchaseOrder"
  [poUid]="poUid"
  [po]="po"
  [isUpdateDelivery]="isUpdateDelivery"
  (close)="closeRaisePoPopup($event)"
></prc-raise-po-popup>

<prc-cancel-po-popup
  *ngIf="isCancelPoPopupVisible"
  [poUid]="poUid"
  [sendToSupplier]="po.sendToSupplier"
  (close)="closeCancelPo($event)"
></prc-cancel-po-popup>

<prc-add-certificate
  #addCertificate
  [poUid]="poUid"
  [vesselUid]="po?.vesselUid"
  (certificateSaved)="onSaveCertificate()"
></prc-add-certificate>
