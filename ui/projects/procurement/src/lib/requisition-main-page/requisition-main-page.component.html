<div class="requisition-grid">
  <jb-grid
    selectionMode="selectionMode"
    [colData]="gridInputs.columns"
    [filterData]="gridInputs.filters"
    [filterListsSet]="gridInputs.filtersLists"
    [getStyleByContainer]="true"
    [gridName]="gridInputs.gridName"
    [searchFields]="gridInputs.searchFields"
    [setActions]="reqGridAction"
    [sidebarFloat]="sidebarFloatConfig"
    [tableDataReq]="gridInputs.request"
    [toolTipFields]="requisitionToolTipFields"
    (action)="requisitionAlertAction($event)"
  >
    <ng-template #sidebarFloat>
      <prc-requisition-right-pane
        [requisitionUid]="selectedRow.requisitionUid"
        (close)="onCloseRightPane()"
      ></prc-requisition-right-pane>
    </ng-template>
  </jb-grid>
</div>

<ng-template #accountsTemplate let-rowData>
  <div
    *ngIf="rowData.glAccountNames | prcJoin as account"
    class="text-ellipsis"
    [prcTooltip]="account"
  >
    {{ account }}
  </div>
</ng-template>

<ng-template #creationDateTemplate let-rowData>
  <div class="template-column">
    <span class="text-ellipsis" [prcTooltip]="rowData.creationDate | jbDate">
      {{ rowData.creationDate | jbDate }}</span
    >
  </div>
</ng-template>

<ng-template #deliveryDetailsTemplate let-rowData>
  <div class="text-ellipsis" [prcTooltip]="rowData | formatPort">
    <span class="name-wrapper">
      <a
        class="jb_grid_topCellValue jb-link-600-14"
        *ngIf="rowData | formatPort as portDetails"
        [prcTooltip]="portDetails"
      >
        {{ rowData | formatPort }}</a
      >
    </span>
    <div class="jb-breadcrumb">
      <div
        class="jb_grid_botCellValue text-ellipsis"
        [title]="rowData?.deliveryDate"
      >
        {{ rowData.deliveryDate | utcToLocal | jbDate }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #rfqTemplate let-rowData>
  <div [prcOverlay]="overlay" *ngIf="!rowData.noQuotation">
    <div class="quoat">
      <table>
        <tr>
          <td *ngIf="rowData.rfq">{{ rowData.rfq }}</td>
          <td *ngIf="rowData.pendingRfq">
            <i class="icons8-clock"></i> {{ rowData.pendingRfq }}
          </td>
          <td *ngIf="rowData.receivedRfq">
            <i class="icons8-check-mark-symbol"></i> {{ rowData.receivedRfq }}
          </td>
          <td *ngIf="rowData.declinedRfq">
            <i class="icons8-cancel"></i> {{ rowData.declinedRfq }}
          </td>
        </tr>
      </table>
    </div>

    <prc-column-overlay #overlay>
      <p-table
        [value]="rowData.rfqStatus"
        class="rfq-status-grid"
        [tableStyle]="{ 'min-width': '50rem' }"
      >
        <ng-template pTemplate="header">
          <tr>
            <th scope="col">Supplier Name</th>
            <th scope="col">Status</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rfqStatus>
          <tr>
            <td>{{ rfqStatus.supplierName }}</td>

            <td
              class="rfq-status"
              [ngClass]="rfqStatus.rfqStatusUid | prcGetRfqStatus | async"
            >
              <i class="icons8-new-moon"></i>
              {{ rfqStatus.rfqStatusUid | prcGetRfqDisplayName | async }}
            </td>
          </tr>
        </ng-template>
      </p-table>
    </prc-column-overlay>
  </div>
</ng-template>

<ng-template #subjectTemplate let-rowData>
  <div class="template-column">
    <a
      class="jb_grid_topCellValue jb-link-600-14 text-ellipsis"
      [prcNavigationLink]="[
        rowData.noQuotation ? directPOLink : requisitionRouterLink,
        rowData.requisitionUid
      ]"
      [queryParams]="{ tab_title: rowData.requisitionNumber }"
      [prcTooltip]="rowData.description"
      target="_blank"
      >{{ rowData.description }}
    </a>
  </div>
</ng-template>

<prc-icon-template
  [gridColumns]="gridInputs.columns"
  [iconColumn]="iconColumn"
  [attachmentColumn]="attachmentColumn"
  type="Requisition"
></prc-icon-template>
