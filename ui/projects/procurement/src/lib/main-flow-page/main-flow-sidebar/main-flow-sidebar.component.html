<ng-container
  *ngTemplateOutlet="isSidebarOpen ? maxTemplate : minTemplate"
></ng-container>

<div class="layout-menu-icon">
  <i
    [ngClass]="isSidebarOpen ? 'arrow-close' : 'arrow-open'"
    (click)="toggleSidebar()"
  ></i>
</div>

<ng-template #maxTemplate>
  <div class="max-container">
    <jb-multi-select-dropdown
      appendTo="body"
      class="vessel-dropdown"
      defaultLabel="Select Vessel"
      [content]="vesselContent"
      [virtualScroll]="true"
      (onClearValues)="onSelectedVesselChange($event)"
    ></jb-multi-select-dropdown>

    <section
      *ngFor="let menuGroup of menuGroups; trackBy: trackMenuGroupsByFn"
      class="menu-group"
    >
      <header>
        <img alt="header" [src]="path + menuGroup.file" />
        <span class="header-title">{{ menuGroup.title }}</span>
      </header>

      <div
        *ngFor="let menu of menuGroup.menus; trackBy: trackMenusByFn"
        class="menu-item"
        [class.selected-item]="menu.id === selectedMenuItemId"
        (click)="onMenuClick(menu)"
      >
        <span>{{ menu.label }} </span>
        <span class="menu-small-text">{{
          (counters && counters[menu.position]) || 0
        }}</span>
      </div>
    </section>
  </div>
</ng-template>

<ng-template #minTemplate>
  <div class="min-container">
    <section *ngFor="let menuGroup of menuGroups" class="menu-group">
      <img
        alt="menu-title"
        class="menu"
        tooltipPosition="right"
        [src]="path + menuGroup.file"
        [pTooltip]="menuGroup.title"
        (click)="menu.toggle($event)"
      />
      <p-tieredMenu
        #menu
        [model]="menuGroup.menus"
        [popup]="true"
        appendTo="body"
        showTransitionOptions="0ms"
        hideTransitionOptions="0ms"
        styleClass="menu-container"
      ></p-tieredMenu>
    </section>
  </div>
</ng-template>
