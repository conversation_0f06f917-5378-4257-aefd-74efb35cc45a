<ng-container [formGroup]="form">
  <prc-form-label class="form-label" label="Create Date" *ngIf="!isPo">
    <jb-calendar
      inputStyleClass="jb-text readonly no-background"
      [showValue]="true"
      [content]="{ selectedValue: createDate | jbDate }"
    ></jb-calendar>
  </prc-form-label>

  <prc-form-label class="form-label" label="PO Date" *ngIf="isPo">
    <jb-calendar
      inputStyleClass="jb-text readonly no-background"
      [showValue]="true"
      [content]="{ selectedValue: poDate | jbDate }"
    ></jb-calendar>
  </prc-form-label>

  <prc-form-label class="form-label" label="Urgency">
    <jb-single-select-dropdown
      [isWithoutBackground]="!isEditMode"
      [showValue]="!isEditMode"
      [content]="urgencyTypeDropdown"
      [disabled]="
        isPo ||
        !((permission$ | async)?.EditGeneralInfo | hasPermissions) ||
        readonly
      "
    >
    </jb-single-select-dropdown>
  </prc-form-label>

  <prc-form-label class="form-label" label="PO Type">
    <jb-single-select-dropdown
      [isWithoutBackground]="!isEditMode"
      [showValue]="!isEditMode"
      [content]="poTypesDropdown"
      [disabled]="
        isPo ||
        !((permission$ | async)?.EditGeneralInfo | hasPermissions) ||
        disablePoType ||
        reviewMode ||
        readonly
      "
    >
    </jb-single-select-dropdown>
  </prc-form-label>
  <prc-form-label class="form-label" label="No. Of Items">
    <input
      class="jb-text readonly no-background"
      pInputText
      [ngModel]="numberOfItems"
      [ngModelOptions]="{ standalone: true }"
    />
  </prc-form-label>

  <prc-form-label class="form-label" label="Assigned To">
    <jb-single-select-dropdown
      [isWithoutBackground]="!isEditMode"
      [showValue]="!isEditMode"
      [content]="assignedToTypeDropdown"
      [virtualScroll]="true"
      [disabled]="!poEditGeneralInfo || readonly"
    >
    </jb-single-select-dropdown>
  </prc-form-label>

  <prc-form-label
    *ngIf="approvalMatrixDetails?.approvalFlowName"
    class="form-label"
    label="Flow Name"
  >
    <div
      class="text-ellipsis"
      [prcTooltip]="approvalMatrixDetails?.approvalFlowName"
    >
      {{ approvalMatrixDetails?.approvalFlowName }}
    </div>
  </prc-form-label>

  <prc-form-label
    *ngIf="approvalMatrixDetails?.nextApproverRoles?.length"
    class="form-label"
    label="Next Approver"
  >
    <div>
      {{ approvalMatrixDetails.nextApproverRoles | roles | async }}
    </div>
  </prc-form-label>

  <i
    *ngIf="ecStatus !== 'notEC'"
    class="icons8-private-3 export-control-icon header-ec-icon"
    pTooltip="EC"
    [ngClass]="ecStatus"
  ></i>

  <img
    *ngIf="headerDetails.criticality"
    alt="critical-icon"
    class="icons8-copyright critical-icon"
    pTooltip="Critical"
    src="assets/images/criticality.svg"
  />

  <img
    *ngIf="headerDetails.dangerousGoods"
    alt="warning-icon"
    class="icons8-falling-objects-hazard warning-icon"
    pTooltip="Dangerous Goods (DG)"
    src="assets/images/DG-icon.svg"
  />

  <ng-container *ngIf="headerDetails.ihm">
    <img
      *ngIf="isPo; else defaultIhmIcon"
      alt="info-icon"
      class="icons8-high-priority-3 info-icon"
      pTooltip="Inventory of Hazardous Material (IHM)"
      [src]="
        headerDetails.certificatePendingCount ||
        headerDetails.hmContentPendingCount
          ? 'assets/images/ihm.svg'
          : 'assets/procurement/images/ihm-green.svg'
      "
    />
  </ng-container>

  <ng-template #defaultIhmIcon>
    <img
      alt="info-icon"
      class="icons8-high-priority-3 info-icon"
      pTooltip="Inventory of Hazardous Material (IHM)"
      src="assets/images/ihm.svg"
    />
  </ng-template>
</ng-container>
