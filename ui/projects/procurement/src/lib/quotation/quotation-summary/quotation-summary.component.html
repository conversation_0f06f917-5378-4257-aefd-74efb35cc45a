<div class="table-header">
  <ng-container *ngIf="gridRef">
    <div grid-link-header class="radio-grouping">
      <div>Quotation Currency</div>
      <div *ngFor="let currency of currencyRadioContext.values">
        <p-radioButton
          [name]="currencyRadioContext.groupName"
          [value]="currency"
          [label]="currency"
          [(ngModel)]="selectedCurrency"
          (onClick)="onCurrencyChange(currency)"
        ></p-radioButton>
      </div>
    </div>
    <ng-container
      [ngTemplateOutlet]="gridRef?.getGridTemplates?.gridHeaderRef"
    ></ng-container>
  </ng-container>
</div>
<jb-grid
  #gridRef
  class="quotation-table"
  dataKey="uid"
  selectionMode="multiple"
  [advancedSettings]="gridInputs.advancedSettings"
  [colData]="gridInputs.columns"
  [extraHeaders]="gridInputs.extraHeaders"
  [getStyleByContainer]="true"
  [getTemplate]="{ gridHeaderRef: true }"
  [gridName]="gridInputs.gridName"
  [isDisplayAdvancedFilter]="false"
  [isDisplaySearchField]="false"
  [matrixSelectedValue]="selectedQuotations$ | async"
  [paginator]="false"
  [selectedCurrency]="selectedCurrency"
  [showSettings]="gridInputs.showSettings"
  [tableData]="data"
  (action)="gridAction($event)"
  (matrixSelection)="emitSelectedQuotations($event)"
  (sortByColumn)="sortByColumn($event)"
>
</jb-grid>
<ng-template #stateTemplate let-rowData>
  <span
    [ngClass]="stateToIcon[rowData.state]"
    [pTooltip]="stateTooltip(rowData)"
  ></span>
</ng-template>
<ng-template #supplierTemplate let-rowData>
  <span class="template-ellipsis" [prcTooltip]="rowData.supplierRegisteredName">
    <a
      *ngIf="rowData.supplierStatus === 'Approved'; else notApproved"
      class="supplier-link jb_grid_topCellValue jb-link-600-14 text-ellipsis"
      [prcNavigationLink]="getSupplierLink(rowData?.clientSupplierUid)"
      target="_blank"
      >{{ rowData.supplierRegisteredName }}
    </a>
    <ng-template #notApproved>{{ rowData.supplierRegisteredName }}</ng-template>
  </span>
</ng-template>

<ng-template #remarksTemplate let-rowData>
  <button
    class="icon-button"
    type="button"
    pButton
    icon="icons8-comments"
    pTooltip="remarks"
    (click)="showRemarkDialog(rowData)"
  ></button>
</ng-template>
<prc-remark-dialog
  *ngIf="remarkDialogVisible"
  [additionalDetails]="additionalDetails"
  [quotation]="selectedRow"
  (dialogClose)="closeRemarkDialog()"
>
</prc-remark-dialog>

<ng-template #itemsAmountTemplate let-rowData>
  {{ rowData.itemsAmount | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #texationTemplate let-rowData>
  {{ rowData.taxation }}%
</ng-template>
<ng-template #discountTemplate let-rowData>
  {{ rowData.discount }}%
</ng-template>
<ng-template #truckTemplate let-rowData>
  {{ rowData.truck | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #packagingTemplate let-rowData>
  {{ rowData.packaging | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #freightTemplate let-rowData>
  {{ rowData.freight | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #bargeTemplate let-rowData>
  {{ rowData.barge | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #otherTemplate let-rowData>
  {{ rowData.other | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<ng-template #currencyTemplate let-rowData>
  {{
    selectedCurrency === eCurrencyType.Org ? rowData.currency : selectedCurrency
  }}
</ng-template>
<ng-template #selectedItemsAmountTemplate let-rowData>
  {{
    rowData.amountSelectedItems
      | currency : rowData.currencyRate : selectedCurrency
  }}
</ng-template>
<ng-template #finalAmountTemplate let-rowData>
  {{ rowData.finalAmount | currency : rowData.currencyRate : selectedCurrency }}
</ng-template>
<prc-rework-dialog
  *ngIf="selectedAction?.type === eQoutationSummaryActionTypesType.Rework"
  [quotatonUid]="selectedAction?.rowData.uid"
  [reference]="selectedAction?.rowData.reference"
  [timestamp]="selectedAction?.rowData.timestamp"
  (close)="closeActionDialog($event)"
></prc-rework-dialog>
