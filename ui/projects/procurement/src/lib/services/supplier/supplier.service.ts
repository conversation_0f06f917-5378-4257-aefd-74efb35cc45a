import { Injectable } from '@angular/core';

import { supplant } from '@j3-procurement/dtos';
import { RequisitionSupplierDto } from '@j3-procurement/dtos/requisition';
import {
  RfqSupplierRequestDto,
  SendRfqRequestDto,
  SendRfqResponseDto,
} from '@j3-procurement/dtos/rfq';
import { RfqSupplierFilterOptionsDto } from '@j3-procurement/dtos/rfq';
import { AgentDto, SupplierAddressDto } from '@j3-procurement/dtos/supplier';
import {
  ApiRequestService,
  eCrud,
  OData,
  WebApiRequest,
} from 'jibe-components';
import { Observable } from 'rxjs';
import { finalize } from 'rxjs/operators';

import { map } from 'rxjs/operators';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';
import { SpinnerService } from '../spinner.service';

@Injectable({
  providedIn: 'root',
})
export class SupplierService {
  constructor(
    private apiRequestService: ApiRequestService,
    private readonly spinnerService: SpinnerService
  ) {}

  public getAllowedStatusFilters(): Promise<string[]> {
    const supplierStatus: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      action: 'filter-options',
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(supplierStatus).toPromise();
  }

  getSupplierGridDetails(
    requisitionUid,
    body?
  ): Observable<RequisitionSupplierDto[]> {
    const requisitionHeaderDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/rfqs-suppliers/list`,
      crud: eCrud.Post,
      body,
    };
    return this.apiRequestService.sendApiReq(requisitionHeaderDetails);
  }

  getSupplierDetailsReq(requisitionUid: string): WebApiRequest {
    const requisitionHeaderDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/rfqs-suppliers/list`,
      crud: eCrud.Post,
    };
    return requisitionHeaderDetails;
  }

  getSupplierDetails(
    requisitionUid: string
  ): Promise<RequisitionSupplierDto[]> {
    return this.apiRequestService
      .sendApiReq(this.getSupplierDetailsReq(requisitionUid))
      .pipe(map(({ records }) => records))
      .toPromise();
  }

  public getSupplierFilters(
    requisitionUid: string
  ): Observable<RfqSupplierFilterOptionsDto> {
    const supplierFilter: WebApiRequest = {
      action: `${requisitionUid}/rfqs-suppliers/suppliers-filter-options`,
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      crud: eCrud.Get,
      entity: ePrcRequestEntity.Requisition,
    };
    return this.apiRequestService.sendApiReq(supplierFilter);
  }

  public getSupplierListReq(odata?: OData): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      action: ePrcRequestAction.List,
      crud: eCrud.Post,
      odata,
    };
  }

  public getAgentListReq(odata?: OData): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Companies,
      action: ePrcRequestAction.AgentList,
      crud: eCrud.Post,
      odata,
    };
  }

  /**
   * @description Used to delete Item Record.
   */
  removeSupplierDetails(
    sendObj: RfqSupplierRequestDto,
    requisitionUid,
    rfqUid
  ): Observable<Object[]> {
    const removeSupplierDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      crud: eCrud.Put,
      action: `${requisitionUid}/rfqs/${rfqUid}`,
      body: sendObj,
    };
    return this.apiRequestService.sendApiReq(removeSupplierDetails);
  }

  sendRFQ(body: SendRfqRequestDto): Observable<SendRfqResponseDto> {
    const removeSupplierDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Rfq,
      crud: eCrud.Post,
      action: `list`,
      body,
    };

    this.spinnerService.show();
    return this.apiRequestService
      .sendApiReq(removeSupplierDetails)
      .pipe(finalize(() => this.spinnerService.hide()));
  }

  public getAddressesReq(supplierUid: string): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      crud: eCrud.Get,
      action: `${supplierUid}/addresses`,
    };
  }

  public getAddresses(supplierUid: string): Promise<SupplierAddressDto[]> {
    const request = this.getAddressesReq(supplierUid);
    return this.apiRequestService.sendApiReq(request).toPromise();
  }

  public getAgent(supplierUid: string): Promise<AgentDto> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      action: supplant(ePrcRequestAction.GetAgent, { supplierUid }),
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(request).toPromise();
  }

  /**
   * Use getAgent when it is possible
   */
  public getAgentBySupplierId(supplierId: string): Promise<AgentDto> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      action: 'agent',
      params: `supplierId=${supplierId}`,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(request).toPromise();
  }
}
