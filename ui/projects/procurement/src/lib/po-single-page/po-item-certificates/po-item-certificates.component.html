<jb-grid
  class="item-certificates-grid"
  *ngIf="gridInputs"
  [colData]="gridInputs.columns"
  [filterData]="gridInputs.filters"
  [filterListsSet]="gridInputs.filtersLists"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [searchFields]="gridInputs.searchFields"
  [setActions]="!readonly ? gridInputs.actions : null"
  [tableDataReq]="gridInputs.request"
  [tableData]="[]"
  (action)="onGridAction($event)"
></jb-grid>

<ng-template #certificateTemplate let-rowData>
  <div
    class="certificate"
    (click)="
      previewFile(
        rowData.uploadUid,
        rowData.uploadFileName,
        rowData.uploadFilePath,
        rowData.fileType
      )
    "
  >
    <i *ngIf="rowData.uploadUid" class="certificate-icon icons8-attach-2"></i>
    <span
      class="certificate-name text-ellipsis"
      [prcTooltip]="rowData.uploadFileName"
      >{{ rowData.uploadFileName }}</span
    >
  </div>
</ng-template>

<ng-template #ihmContentTemplate let-rowData>
  <div class="ihm-content" (click)="showDetails(rowData)">
    <span
      *ngIf="rowData.ihmContentText || 'Select' as ihmContentText"
      class="ihm-content-name text-ellipsis"
      [prcTooltip]="ihmContentText"
      >{{ ihmContentText }}</span
    >
    <i
      *ngIf="rowData.uploadUid"
      class="ihm-content-icon icons8-external-link"
    ></i>
  </div>
</ng-template>

<ng-template #itemTypeTemplate let-rowData>
  <span *ngIf="rowData.itemTypeUid as itemTypeUid">{{
    (itemTypeUid | jcdsItem : "ItemsCategory" | async)?.category_name || UNKNOWN
  }}</span>
</ng-template>

<ng-template #remarksTemplate let-rowData>
  <i
    *ngIf="rowData.remarks as remarks"
    [pTooltip]="remarks"
    class="remarks-icon icons8-chat-room"
    (click)="showDetails(rowData)"
  ></i>
</ng-template>

<ng-template #statusTemplate let-rowData>
  <prc-status
    *ngIf="rowData.taskStatus as status"
    [color]="statusColor[status | lowercase] || statusColor.default"
    [status]="status"
  ></prc-status>
</ng-template>

<jb-document-preview
  [documentContent]="documentContent"
  [(isDocumentPreview)]="isDocumentPreview"
  [modal]="true"
></jb-document-preview>
