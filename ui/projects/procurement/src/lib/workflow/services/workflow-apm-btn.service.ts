import { Injectable } from '@angular/core';
import { ApprovalMatrixRequestDto } from '@j3-approval-matrix/dtos/approval-matrix';
import { supplant } from '@j3-prc-shared/dtos';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import { NotificationService, RolesPipe } from 'j3-prc-components';
import { UserService } from 'jibe-components';
import { SpinnerService } from '../../services/spinner.service';

import { ActionResultDto } from '@j3-procurement/dtos/workflow';
import { MasterService } from '../../services/master/master.service';
import { PermissionService } from '../../services/permission/permission.service';
import { HeaderButton } from '../../shared/generic-header/header-button';
import { ApmDialogService } from './apm-dialog.service';
import { ApprovalMatrixService } from './approval-matrix.service';
import {
  apmPermissionTooltip,
  APM_BUTTON_NAME,
  noApprovalFlows,
} from './constants';
import {
  ApmActionResult,
  ApprovalMatrixDetails,
  RunActionOptions,
} from './types';
import { WorkflowBtnService } from './workflow-btn.service';
import { WorkflowService } from './workflow.service';

@Injectable()
export class WorkflowApmBtnService extends WorkflowBtnService<ApmActionResult> {
  private apmReq: ApprovalMatrixRequestDto;

  set apmContext(value: ApprovalMatrixRequestDto) {
    this.apmReq = value;
  }

  constructor(
    private readonly apmDialogService: ApmDialogService,
    private readonly approvalMatrixService: ApprovalMatrixService,
    private readonly rolePipe: RolesPipe,
    protected masterService: MasterService,
    protected notificationService: NotificationService,
    protected permissionService: PermissionService,
    protected spinnerService: SpinnerService,
    protected userService: UserService,
    protected workflowService: WorkflowService
  ) {
    super(
      masterService,
      notificationService,
      permissionService,
      spinnerService,
      userService,
      workflowService
    );
  }

  public async reworkAction(): Promise<ActionResultDto> {
    const curState = this.actionSubject.getValue()?.state;
    // If there's no previous state, exit early (nothing to revert to).
    if (!curState?.prevId) {
      return;
    }
    await this.runAction(curState.prevId, { forceRework: true });
    const { action, apm, state, timestamp } = this.actionSubject.getValue();
    const isApm = state.configDetails?.approval_matrix?.is_applicable;
    let newApm = apm;
    // If the APM is applicable, load the APM details.
    if (isApm) {
      newApm = await this.loadApm();
    } else if (apm) {
      newApm = null;
    }
    // Only update if APM has changed
    if (newApm !== apm) {
      this.actionSubject.next({ action, apm: newApm, state, timestamp });
    }
  }

  public async runAction(
    statusId: WorkflowType,
    options?: RunActionOptions<ApmActionResult>
  ): Promise<ActionResultDto | undefined> {
    const { forceRework } = options ?? {};
    let runOptions = options;
    if (!forceRework) {
      const { action, state } = this.actionSubject.getValue() ?? {};
      const isApm = state.configDetails?.approval_matrix?.is_applicable;
      const nextIsApm = action?.configDetails?.approval_matrix?.is_applicable;
      // if APM is applicable and the action is successfully executed, exit early (no state update needed)
      if (isApm && (await this.runApmAction())) {
        return;
      }
      // if the next action requires APM, initialize the approval flow
      if (nextIsApm) {
        runOptions = { ...options, beforeRun: () => this.initApmFlow() };
      }
    }
    return super.runAction(statusId, runOptions);
  }

  protected buildHeaderButton(
    actionRes: ApmActionResult,
    disabled?: boolean,
    tooltip?: string
  ): HeaderButton {
    const btn = super.buildHeaderButton(actionRes, disabled, tooltip);
    const { apm, state } = actionRes ?? {};
    const isApm = state?.configDetails?.approval_matrix?.is_applicable;
    return btn && isApm && apm?.currentStep
      ? { ...btn, label: APM_BUTTON_NAME }
      : btn;
  }

  protected async canRunAction(res: ApmActionResult): Promise<boolean> {
    const { apm, state } = res;
    const isApm = state.configDetails?.approval_matrix?.is_applicable;
    if (!(isApm && apm?.currentStep)) {
      return super.canRunAction(res);
    }

    try {
      return await this.approvalMatrixService
        .canApproveFlow(apm.approvalHistoryUid)
        .toPromise();
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
      return false;
    }
  }

  protected async getWfTooltip(res: ApmActionResult): Promise<string> {
    const { action, apm, state } = res;
    if (!action) {
      return '';
    }
    if (!state.configDetails?.approval_matrix?.is_applicable) {
      return super.getWfTooltip(res);
    }

    const roles = apm?.nextApproverRoles ?? [];
    if (roles.length === 0) {
      return '';
    }

    const roleNames = await this.rolePipe.transform(roles).toPromise();
    return supplant(apmPermissionTooltip, { roleNames });
  }

  protected async initActionResult(result: ApmActionResult): Promise<void> {
    const { state } = result ?? {};
    let apmResult = result;

    const isApm = state?.configDetails?.approval_matrix?.is_applicable;
    if (isApm) {
      apmResult = { ...result, apm: await this.loadApm() };
    }
    return super.initActionResult(apmResult);
  }

  private async initApmFlow(): Promise<ApmActionResult | false> {
    const flowList = await this.approvalMatrixService
      .getApprovalFlowList(this.apmReq)
      .toPromise();

    if (!flowList?.length) {
      throw new Error(noApprovalFlows);
    }
    // openDialog returns approvalFlowUid if modal is closed successfully, or undefined otherwise.
    const approvalFlowUid =
      flowList.length > 1
        ? await this.apmDialogService.openDialog({ flowList })
        : flowList[0].uid;

    if (!approvalFlowUid) {
      return false; // abort super.runAction
    }

    const { objectUid, vesselId } = this.context;
    const flow = await this.approvalMatrixService
      .initFlow({
        objectUid,
        approvalFlowUid,
        vesselId,
        value: this.apmReq.value,
      })
      .toPromise();

    const { action, state, timestamp } = this.actionSubject.getValue();
    const apm: ApprovalMatrixDetails = {
      approvalFlowName: flow.name,
      approvalHistoryUid: flow.uid,
      currentStep: flow.state?.currentStep,
      nextApproverRoles: flow.state?.currentStep?.info?.roles,
    };
    return { action, apm, state, timestamp };
  }

  private async loadApm(): Promise<ApprovalMatrixDetails> {
    try {
      const flow = await this.approvalMatrixService
        .getHistoryFlow(this.context.objectUid)
        .toPromise();

      return {
        approvalFlowName: flow.name,
        approvalHistoryUid: flow.uid,
        currentStep: flow.state?.currentStep,
        nextApproverRoles: flow.state?.currentStep?.info?.roles,
      };
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
    }
  }

  /**
   * Executes the APM action.
   * Returns `true` to indicate that the action was handled and no state update is needed,
   * regardless of success or failure. Returns `false` only if the action was not processed.
   */
  private async runApmAction(): Promise<boolean> {
    const { action, apm, state, timestamp } =
      this.actionSubject.getValue() ?? {};
    if (!apm?.currentStep) {
      return false;
    }

    this.spinnerService.show();
    try {
      const { state: apmState } = await this.approvalMatrixService
        .approveFlow(apm.approvalHistoryUid)
        .toPromise();
      const currentStep = apmState.currentStep;
      const nextApproverRoles = currentStep?.info?.roles;
      const newApm = { ...apm, currentStep, nextApproverRoles };
      this.actionSubject.next({ action, apm: newApm, state, timestamp });
      return true;
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
      return true;
    } finally {
      this.spinnerService.hide();
    }
  }
}
