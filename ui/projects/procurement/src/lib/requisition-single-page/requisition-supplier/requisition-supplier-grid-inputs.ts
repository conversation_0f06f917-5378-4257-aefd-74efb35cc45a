import { createColumns, getJCDSDataReq } from 'j3-prc-components';
import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eGridEvents,
  eIconNames,
  Filter,
  FilterListSet,
  GridRowActions,
  IJbDialog,
  SearchField,
} from 'jibe-components';
import {
  eGridActionName,
  eLabels,
  eSwitchCaseParams,
  eValues,
} from '../../models/enums/prc-main.enum';
import { eDisplayText, eFieldNames } from './requisition-supplier.enum';
import { ColumnKey, FilterKey } from './types';

import { JbDropdownOption } from '../../models/interfaces/jb-dropdown-option';

export const gridName = 'supplierGrid';

export const filters: Filter[] = [
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.Supplier,
    Active_Status: true,
    FieldName: eFieldNames.Supplier,
    DisplayCode: 'name',
    ValueCode: 'uid',
    FieldID: 1,
    default: false,
  },
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.DeliveryPort,
    Active_Status: true,
    FieldName: eFieldNames.DeliveryPort,
    DisplayCode: 'name',
    ValueCode: 'id',
    FieldID: 2,
    default: false,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'from_Delivery_Date',
    DisplayCode: null,
    DisplayText: 'Delivery Date',
    FieldID: 3,
    FieldName: 'from_Delivery_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 2,
    CoupleLabel: 'Delivery Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'to_Delivery_Date',
    DisplayCode: null,
    DisplayText: '',
    FieldID: 4,
    FieldName: 'to_Delivery_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 2,
    CoupleLabel: 'Delivery Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'from_Send_Date',
    DisplayCode: null,
    DisplayText: 'Send Date',
    FieldID: 5,
    FieldName: 'from_Send_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 3,
    CoupleLabel: 'Send Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'to_Send_Date',
    DisplayCode: null,
    DisplayText: '',
    FieldID: 6,
    FieldName: 'to_Send_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 3,
    CoupleLabel: 'Send Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'from_Due_Date',
    DisplayCode: null,
    DisplayText: 'Due Date',
    FieldID: 7,
    FieldName: 'from_Due_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 4,
    CoupleLabel: 'Due Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'to_Due_Date',
    DisplayCode: null,
    DisplayText: '',
    FieldID: 8,
    FieldName: 'to_Due_Date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 4,
    CoupleLabel: 'Due Date',
    gridName,
  },
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.QuotationStatus,
    Active_Status: true,
    FieldName: 'rfqStatus',
    DisplayCode: 'display_name',
    FieldID: 9,
    default: false,
  },
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.Contract,
    Active_Status: true,
    FieldName: eFieldNames.Contract,
    DisplayCode: 'contracted',
    ValueCode: 'uid',
    FieldID: 10,
    default: false,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'from_last_quotation_date',
    DisplayCode: null,
    DisplayText: 'Last Quotation Date',
    FieldID: 11,
    FieldName: 'from_last_quotation_date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 5,
    CoupleLabel: 'Last Quotation Date',
    gridName,
  },
  {
    Active_Status: true,
    Active_Status_Config_Filter: true,
    ControlType: eGridCellType.Date,
    DataType: eGridCellType.DateTime,
    Details: 'to_last_quotation_date',
    DisplayCode: null,
    DisplayText: '',
    FieldID: 12,
    FieldName: 'to_last_quotation_date',
    FieldType: null,
    ValueCode: null,
    CoupleID: 5,
    CoupleLabel: 'Last Quotation Date',
    gridName,
  },
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.Rating,
    Active_Status: true,
    FieldName: eFieldNames.Rating,
    DisplayCode: 'name',
    ValueCode: 'uid',
    FieldID: 13,
    default: false,
  },
  {
    Active_Status_Config_Filter: true,
    type: eGridCellType.Multiselect,
    gridName,
    DisplayText: eDisplayText.Country,
    Active_Status: true,
    FieldName: eFieldNames.Country,
    DisplayCode: 'name',
    ValueCode: 'uid',
    FieldID: 14,
    default: false,
  },
];

export function getFiltersLists(
  suppliers: JbDropdownOption[],
  deliveryPorts: JbDropdownOption[],
  countries: JbDropdownOption[]
): Record<FilterKey, FilterListSet[string]> {
  return {
    supplier: {
      list: suppliers,
      type: eGridCellType.Multiselect,
      odataKey: 'supplierUid',
    },
    deliveryPortName: {
      list: deliveryPorts,
      type: eGridCellType.Multiselect,
      odataKey: 'deliveryPortId',
    },
    from_Delivery_Date: {
      type: eGridCellType.Date,
      odataKey: 'deliveryDate',
      dateMethod: 'ge',
    },
    to_Delivery_Date: {
      type: eGridCellType.Date,
      odataKey: 'deliveryDate',
      dateMethod: 'le',
    },
    from_Send_Date: {
      type: eGridCellType.Date,
      odataKey: 'sendDate',
      dateMethod: 'ge',
    },
    to_Send_Date: {
      type: eGridCellType.Date,
      odataKey: 'sendDate',
      dateMethod: 'le',
    },
    from_Due_Date: {
      type: eGridCellType.Date,
      odataKey: 'quotationDueDate',
      dateMethod: 'ge',
    },
    to_Due_Date: {
      type: eGridCellType.Date,
      odataKey: 'quotationDueDate',
      dateMethod: 'le',
    },
    rfqStatus: {
      webApiRequest: getJCDSDataReq('rfqStatus', {
        orderby: 'display_name',
      }),
      type: eGridCellType.Multiselect,
      odataKey: 'rfqStatusUid',
      listValueKey: 'uid',
    },

    contracted: {
      list: [
        {
          label: eLabels.Yes,
          value: eValues.Yes,
        },
        {
          label: eLabels.No,
          value: eValues.No,
        },
      ],
      type: eGridCellType.Multiselect,
      odataKey: 'contracted',
      listValueKey: 'value',
    },
    from_last_quotation_date: {
      type: eGridCellType.Date,
      odataKey: 'lastQuotation',
      dateMethod: 'ge',
    },
    to_last_quotation_date: {
      type: eGridCellType.Date,
      odataKey: 'lastQuotation',
      dateMethod: 'le',
    },
    rating: {
      list: [],
      type: eGridCellType.Multiselect,
      odataKey: 'rating',
    },

    country: {
      list: countries,
      type: eGridCellType.Multiselect,
      odataKey: 'countryId',
    },
  };
}

export const searchFields: SearchField[] = [
  { field: 'supplierName', pattern: 'contains' },
];

export const actions: GridRowActions[] = [
  {
    name: eGridEvents.Duplicate,
    icon: eIconNames.Duplicate,
    color: eColor.JbBlack,
  },
];

export const moreActionsArr = [
  {
    value: eGridActionName.DeliveryRemarks,
    key: eSwitchCaseParams.SupplierDeliveryKey,
  },
  {
    value: eGridActionName.ExportRFQ,
    key: eSwitchCaseParams.SupplierExportKey,
  },
];

export const dialogContentConfirmDetails: IJbDialog = {
  dialogHeader: 'Confirm',
  closableIcon: true,
  resizableDialog: true,
  modal: true,
  responsive: true,
  dialogHeight: 150,
  dialogWidth: 400,
};

export const dialogContentCommentDetails: IJbDialog = {
  dialogHeader: 'Comments',
  closableIcon: true,
  resizableDialog: true,
  modal: true,
  responsive: true,
  dialogHeight: 150,
  dialogWidth: 400,
};

export const getColumns = (readonly = false) =>
  createColumns<string, ColumnKey>([
    [
      eDisplayText.Name,
      'supplierName',
      {
        width: eGridColumnsWidth.ShortDescription,
        IsMandatory: true,
      },
    ],
    [
      eDisplayText.DeliveryPort,
      'deliveryPortName',
      {
        width: eGridColumnsWidth.LongDescription,
        Editable: !readonly,
        ReadOnly: true,
        ChangeEditableState: false,
        DisableSort: true,
        ControlType: eFieldControlType.Input,
        IsMandatory: true,
      },
    ],
    [
      eDisplayText.DeliveryDate,
      'deliveryDate',
      {
        width: eGridColumnsWidth.ShortDescription,
        ControlType: eGridCellType.Date,
        FieldType: eGridCellType.Date,
        Editable: !readonly,
        ChangeEditableState: false,
        DisableSort: true,
        DataType: 'datetime',
        IsMandatory: true,
      },
    ],
    [
      eDisplayText.DeliveryRemarks,
      'deliveryRemarks',
      {
        width: eGridColumnsWidth.ShortDescription,
        ControlType: eGridCellType.Input,
        Editable: !readonly,
        ReadOnly: readonly,
        ChangeEditableState: false,
        DisableSort: true,
        IsMandatory: true,
        MaxLength: 500,
      },
    ],
    [
      eDisplayText.SelectedItems,
      'items',
      { width: '100px', hyperlink: true, DisableSort: true, IsMandatory: true },
    ],
    [
      eDisplayText.SendDate,
      'rfqSendDate',
      {
        width: '80px',
        DataType: 'datetime',
        FieldType: eGridCellType.Date,
        IsMandatory: true,
      },
    ],
    [
      eDisplayText.DueDate,
      'quotationDueDate',
      {
        width: '80px',
        DataType: 'datetime',
        FieldType: eGridCellType.Date,
        IsMandatory: true,
      },
    ],
    [
      eDisplayText.QuotationStatus,
      'rfqStatusUid',
      { width: eGridColumnsWidth.LongDescription, IsMandatory: true },
    ],
    [
      eDisplayText.City,
      'city',
      {
        width: eGridColumnsWidth.ShortDescription,
        IsVisible: false,
      },
    ],
    [
      eDisplayText.Country,
      'country',
      {
        width: eGridColumnsWidth.ShortDescription,
        IsVisible: false,
      },
    ],
    [
      eDisplayText.LastQuotation,
      'lastQuotation',
      {
        width: eGridColumnsWidth.Date,
        IsVisible: false,
        DataType: 'datetime',
        FieldType: eGridCellType.Date,
      },
    ],
    [
      eDisplayText.Contract,
      'contracted',
      { width: eGridColumnsWidth.LongNumber, IsVisible: false },
    ],
    [
      eDisplayText.Rating,
      'rating',
      {
        width: eGridColumnsWidth.ShortDescription,
        IsVisible: false,
      },
    ],
    [
      '',
      'rfqRemark',
      {
        width: eGridColumnsWidth.ShortNumber,
        hyperlink: true,
        DisableSort: true,
        IsMandatory: true,
      },
    ],
    [
      '',
      'ccEmails',
      {
        width: eGridColumnsWidth.ShortNumber,
        IsMandatory: false,
      },
    ],
  ]);
