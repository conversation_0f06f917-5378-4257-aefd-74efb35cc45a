import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { AccountDto } from '@j3-prc-catalog/dtos/account-mapping';
import { Maybe } from '@j3-prc-shared/dtos';
import { BudgetDto, GLAccountBudgetsDto } from '@j3-procurement/dtos/budgets';
import { FinanceAdditionalChargeDto } from '@j3-procurement/dtos/finance';
import { POAdditionalChargeDto } from '@j3-procurement/dtos/po';
import { NotificationService, UnsubscribeComponent } from 'j3-prc-components';
import {
  Column,
  eGridCellType,
  eGridEvents,
  GridAction,
  GridService,
  WebApiRequest,
} from 'jibe-components';
import { from, of } from 'rxjs';
import { catchError, filter, map, switchMap, takeUntil } from 'rxjs/operators';
import {
  ePrcSinglePageGridNames,
  REQUISITION_PROCUREMENT_TYPE,
} from '../../../models/enums/prc-single-page.enum';
import { RequisitionPermission } from '../../../models/enums/requisition-permission.enum';
import { AnalysisCode } from '../../../models/interfaces/accounting/finance';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../../models/interfaces/grid-inputs';
import { JbDropdownOption } from '../../../models/interfaces/jb-dropdown-option';
import { DirectPOService } from '../../../services/direct-po/direct-po.service';
import { FinanceService } from '../../../services/finance.service';
import { PermissionService } from '../../../services/permission/permission.service';
import { PurchaseRequestType } from '../finance-item-configuration/types';
import { generateGLAccountKey } from '../finance-item-configuration/utils';
import {
  AdditionalChargeAction,
  advancedSettings,
  getActions,
  getColumns,
  gridName,
  searchFields,
  showSettings,
} from './grid-inputs';
import { AdditionalChargesUpdate, ColumnKey } from './types';
// TODO: refactor to reuse bindListToDropdown
@Component({
  selector: 'prc-finance-additional-charges',
  templateUrl: './finance-additional-charges.component.html',
  styleUrls: ['./finance-additional-charges.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FinanceAdditionalChargesComponent
  extends UnsubscribeComponent
  implements OnInit, OnDestroy
{
  @Input() bulkPurchase: boolean;
  @Input() deliveryDate: Date;
  @Input() isBudgetReadOnly: boolean;
  @Input() isPOIssued: boolean;
  @Input() objectUid: string;
  @Input() procurementType: PurchaseRequestType;
  @Input() vesselUid: string;

  @Output() getAdditionalChargesData = new EventEmitter();

  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, POAdditionalChargeDto>;

  private data: POAdditionalChargeDto[] = [];
  private hasFinancialConfigurationAccess = false;
  private readonlyMode: boolean;
  private saveAdditionalChargesData: Map<string, AdditionalChargesUpdate> =
    new Map();
  public splittedAdditonalChargeUid: string;

  @Input() set readonly(value: boolean) {
    this.readonlyMode = value;
    this.setColumnsEditable();
  }
  @Input() additionalChargesRequest: WebApiRequest;
  @Input() additionalChargeAccountsMap: Map<string, AccountDto[]>;
  public selectedAccountsMap: Map<string, AccountDto[]>;
  public glAccountBudgets: GLAccountBudgetsDto[];
  constructor(
    private readonly financeService: FinanceService,
    private readonly gridService: GridService,
    private readonly permissionService: PermissionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly directPOService: DirectPOService,
    private readonly notificationService: NotificationService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const isRequisition = this.procurementType === REQUISITION_PROCUREMENT_TYPE;
    this.gridInputs = {
      advancedSettings,
      columns: getColumns(this.bulkPurchase),
      gridName,
      request: this.additionalChargesRequest,
      searchFields,
      showSettings,
      actions: getActions(this.bulkPurchase && isRequisition),
    };

    this.setEditTableDropDownValues();

    await this.setHasFinancialConfigurationAccess();
    this.setReadOrEditableColumns();
    this.getAdditionalChargeDetails();
  }

  private setEditTableDropDownValues(): void {
    const projectColumn = this.getColumn('projectUid');
    this.gridService.setEditTableDropDownValue(projectColumn);
  }

  private async setHasFinancialConfigurationAccess(): Promise<void> {
    this.hasFinancialConfigurationAccess =
      await this.permissionService.hasPermissions(
        RequisitionPermission.FinancialConfiguration
      );
  }

  setReadOrEditableColumns(): void {
    const isDirectPODisabled = this.directPOService.isReadonly;

    this.gridInputs.columns.forEach((column) => {
      if (!column.hasOwnProperty(eGridCellType.Editable)) {
        return;
      }

      const isBudgetColumn = column.FieldName === 'budgetUid';
      const isBudgetEditable =
        isBudgetColumn && !(this.isBudgetReadOnly || this.isPOIssued);

      column.Editable =
        !this.readonlyMode &&
        (this.procurementType === REQUISITION_PROCUREMENT_TYPE ||
          isBudgetEditable) &&
        !isDirectPODisabled &&
        this.hasFinancialConfigurationAccess;
    });
  }

  getEditableData(rowData: AdditionalChargesUpdate): void {
    this.saveAdditionalChargesData.set(rowData.uid, rowData);
    this.getAdditionalChargesData.emit([
      ...this.saveAdditionalChargesData.values(),
    ]);

    const newMatrixValues = this.data.map((ac) =>
      ac.uid === rowData.uid ? { ...ac, ...rowData } : ac
    );

    const { poPageState$ } = this.financeService;
    poPageState$.next({
      ...poPageState$.value,
      additionalCharges: newMatrixValues,
    });
  }

  private getSelectedAccounts(gridData: POAdditionalChargeDto[]): void {
    if (!this.additionalChargeAccountsMap?.size) {
      return;
    }

    this.selectedAccountsMap = new Map(
      gridData?.map((ac) => [
        ac.uid,
        this.additionalChargeAccountsMap?.get(ac.additionalChargeUid),
      ])
    );
  }

  private getAdditionalChargeDetails(): void {
    this.gridService.matrixDataChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === 'financeAdditionalChargesGrid'),
        map(({ matrixValues }) => {
          this.data = matrixValues as POAdditionalChargeDto[];
          this.getSelectedAccounts(matrixValues);
          const { poPageState$ } = this.financeService;
          poPageState$.next({
            ...poPageState$.value,
            additionalCharges: this.data,
          });
          return matrixValues;
        }),
        switchMap((data) => {
          if (this.isPOIssued) {
            return of([]);
          }
          const glAccountUids = this.getUniqueGLAccounts();
          if (!glAccountUids?.length) {
            return of([]);
          }
          return this.bulkPurchase
            ? this.financeService.loadBulkPOBudgets(
                this.deliveryDate,
                glAccountUids,
                data,
                this.bulkPurchase
              )
            : from(
                this.financeService.loadBudgetsAndCodes({
                  vessel_uid: this.vesselUid,
                  delivery_date: this.deliveryDate,
                  gl_accounts: glAccountUids,
                })
              );
        }),
        catchError(() => of([]))
      )
      .subscribe(() =>
        !this.isBudgetReadOnly ? this.bindBudgetsForSelectedGLAccounts() : null
      );
  }

  getUniqueGLAccounts(): string[] {
    const glAccounts: string[] = [
      ...Object.values(this.additionalChargeAccountsMap ?? {}),
    ].reduce<string[]>(
      (acc, val) => acc.concat(val.map(({ accountUid }) => accountUid)),
      []
    );
    return [...new Set([...glAccounts, ...this.getGLAccountsFromGrid()])];
  }

  getGLAccountsFromGrid(): string[] {
    const glAccountUids = this.data
      ?.map(({ glAccount }) => glAccount?.uid)
      .filter(Boolean);
    return glAccountUids?.length ? [...new Set(glAccountUids)] : [];
  }

  bindBudgetsForSelectedGLAccounts(): void {
    const { poPageState$ } = this.financeService;
    poPageState$.next({
      ...poPageState$.value,
      additionalCharges: this.data as POAdditionalChargeDto[],
    });
    this.bindBudgetAndCodes();
  }

  bindBudgetAndCodes(): void {
    const budgetMap: Map<string, BudgetDto[]> =
      this.financeService.accountBudgets;

    const codeMap: Map<string, AnalysisCode[]> =
      this.financeService.analisysCodes;

    this.data?.forEach((item, i) => {
      const { glAccountUid, vesselUid } = item;
      const glAccountKey = generateGLAccountKey(
        glAccountUid,
        vesselUid,
        this.bulkPurchase
      );
      const budgets = budgetMap.get(glAccountKey);
      const codes = codeMap.get(item?.glAccountUid);
      const editBudgetList = budgets?.map(({ budget_name, uid }) => ({
        label: budget_name,
        value: uid,
      }));
      const editCodeList = codes?.map(({ analysisCode, uid }) => ({
        label: analysisCode,
        value: uid,
      }));
      this.bindListToDropdown('analysisCodeUid', editCodeList, i);
      this.bindListToDropdown('budgetUid', editBudgetList, i);
    });
  }

  bindListToDropdown(
    columnName: keyof POAdditionalChargeDto,
    list: JbDropdownOption[],
    rowIndex: number
  ): void {
    const listColumn = this.getColumn(columnName);
    if (!listColumn) {
      return;
    }
    this.gridService.storeCellData[listColumn.DisplayText] = null;
    this.gridService.gridCellListChangeReq.next({
      gridName: this.gridInputs.gridName,
      columnFieldName: columnName,
      rowIndex,
      dataSet: list,
      dontChangeAll: true,
      setListEmpty: !list?.length,
      detectChanges: true,
    });
  }

  private getColumn(columnName: string): Maybe<Column> {
    return this.gridInputs.columns.find((col) => col.FieldName === columnName);
  }

  private setColumnsEditable(): void {
    if (!this.gridInputs) {
      return;
    }
    this.gridInputs.columns = this.gridInputs?.columns?.map((column) =>
      column.hasOwnProperty(eGridCellType.Editable)
        ? { ...column, Editable: !this.readonlyMode }
        : column
    );
    this.gridInputs = { ...this.gridInputs };
    setTimeout(() => {
      this.gridService.refreshGrid(
        eGridEvents.StaticColumns,
        this.gridInputs.gridName
      );
      this.cdr.markForCheck();
    });
  }

  public onAction(
    actions: GridAction<AdditionalChargeAction, FinanceAdditionalChargeDto>
  ): void {
    if (actions.type !== 'Split Between Vessel') {
      return;
    }
    this.splittedAdditonalChargeUid = actions.payload.additionalChargeUid;
    this.cdr.markForCheck();
  }

  public async onSplit(vesselUids: string[]): Promise<void> {
    try {
      await this.financeService.splitAdditionalCharges(
        this.objectUid,
        this.splittedAdditonalChargeUid,
        { vesselUids }
      );
      this.closeSplitDialog(true);
    } catch ({ error, message }) {
      this.notificationService.error(error?.message ?? message);
    }
  }
  public closeSplitDialog(refreshGrid?: boolean): void {
    this.splittedAdditonalChargeUid = undefined;
    if (refreshGrid) {
      this.gridService.refreshGrid(
        eGridEvents.Table,
        ePrcSinglePageGridNames.FinanceAdditionalChargesGrid
      );
    }
  }
}
