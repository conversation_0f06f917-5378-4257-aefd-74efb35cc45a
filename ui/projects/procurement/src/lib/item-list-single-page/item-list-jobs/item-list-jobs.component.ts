import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  Optional,
  Self,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NgControl } from '@angular/forms';
import { ItemListJobDto, UpdateJobDto } from '@j3-procurement/dtos/item-list';
import {
  eGridEvents,
  GridAction,
  GridService,
  GridShareDataService,
} from 'jibe-components';
import { Observable } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';

import { ExtNavigationService } from 'j3-prc-components';
import { ePrcPermission } from '../../models/enums/prc-permission.enum';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import { ItemListService } from '../../services/item-list/item-list.service';
import { PermissionService } from '../../services/permission/permission.service';
import {
  GridControlValueAccessor,
  <PERSON><PERSON><PERSON><PERSON>,
} from '../../shared/grid-control-value-accessor';
import { BaseSectionService } from '../../shared/single-page-sections/base-section/base-section.service';
import { RowChangesService } from '../../shared/single-page-sections/base-section/row-changes.service';
import { DescriptionPopupData } from '../../shared/single-page-sections/job-section/description-popup/types';
import { eGridRowActions } from '../../shared/single-page-sections/job-section/grid-constants';
import { generateNavigationUrl } from '../../utils/generate-url';
import {
  additionalColumns,
  ColumnKey,
  columns,
  getActions,
  gridName,
  searchFields,
} from './grid-inputs';
import { JobEvent } from './types';

@Component({
  selector: 'prc-item-list-jobs',
  templateUrl: './item-list-jobs.component.html',
  styleUrls: ['./item-list-jobs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridShareDataService, BaseSectionService, RowChangesService],
})
export class ItemListJobsComponent
  extends GridControlValueAccessor
  implements OnInit
{
  @Input() event$: Observable<JobEvent>;
  @Input() itemListUid: string;
  @Input() isEditMode: boolean;

  @ViewChild('descriptionTemplate', { static: true })
  descriptionTemplate: TemplateRef<HTMLElement>;
  @ViewChild('codeTemplate', { static: true })
  codeTemplate: TemplateRef<HTMLElement>;

  public descriptionPopupData: DescriptionPopupData;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, ItemListJobDto>;
  public permission = ePrcPermission;

  private removedRows = new Set<string>();

  constructor(
    @Optional() @Self() public ngControl: NgControl,
    private readonly baseSectionService: BaseSectionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly gridService: GridService,
    private readonly itemListService: ItemListService,
    private readonly permissionService: PermissionService,
    private readonly rowChangesService: RowChangesService,
    private extNavigationService: ExtNavigationService
  ) {
    super(ngControl);
  }

  ngOnInit(): void {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      description: this.descriptionTemplate,
      code: this.codeTemplate,
    };

    this.gridInputs = {
      gridName,
      actions: getActions(),
      columns: [...columns, ...additionalColumns].map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      request: this.itemListService.getJobsRequest(this.itemListUid),
    };

    this.ngControl.control.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((v) => !v)
      )
      .subscribe(() => this.rowChangesService.resetChanges());

    this.toggleGridActions();

    if (this.event$) {
      this.event$
        .pipe(
          takeUntil(this.componentDestroyed$),
          filter(({ type }) => type === 'refreshGrid')
        )
        .subscribe(() => {
          this.gridInputs.request.body = undefined;
          this.gridService.refreshGrid(eGridEvents.Table, gridName);
        });
    }
  }

  public onCloseDescriptionPopup(description: string): void {
    if (description !== undefined) {
      const { catalogUid, jobUid, uid } = this.descriptionPopupData;

      const { count, records } =
        this.baseSectionService.getMatrixData<ItemListJobDto>();

      this.gridInputs = {
        ...this.gridInputs,
        data: {
          count,
          records: records.map((record) =>
            record.jobUid === jobUid && record.catalogUid === catalogUid
              ? { ...record, description }
              : record
          ),
        },
      };

      this.updateRowData(uid, {
        catalogUid,
        description,
        jobUid,
      });
    }
    this.descriptionPopupData = undefined;
  }

  public onGridAction({
    payload,
    type,
  }: GridAction<eGridRowActions, ItemListJobDto>): void {
    const { catalogUid, jobUid, uid } = payload;
    const changes: Partial<UpdateJobDto> = {};

    if (type === eGridRowActions.RemoveService) {
      this.removedRows.add(uid);
      changes.activeStatus = false;
      this.hideRemovedItems();
    }

    this.updateRowData(uid, {
      ...changes,
      catalogUid,
      jobUid,
    });
  }

  public openDescriptionPopup(rowData: ItemListJobDto): void {
    const { jobUid, catalogUid, uid } = rowData;

    this.descriptionPopupData = {
      catalogUid,
      description: rowData.description,
      jobUid,
      uid,
    };
  }

  private hideRemovedItems(): void {
    this.gridInputs = {
      ...this.gridInputs,
      data: this.baseSectionService.filterRemovedRows(this.removedRows),
      request: {
        ...this.gridInputs.request,
        body: { uidsToExclude: [...this.removedRows.values()] },
      },
    };
  }

  private async toggleGridActions(): Promise<void> {
    const canDeleteJobs = await this.permissionService.hasPermissions(
      ePrcPermission.ListDeleteServices
    );
    this.gridInputs.actions = getActions(!canDeleteJobs);

    this.cdr.markForCheck();
  }

  private updateRowData(uid: string, rowChanges: RowChanges): void {
    const mergedRowChanges = this.baseSectionService.mergeRowChanges(
      uid,
      rowChanges
    );
    this.rowChangesService.setRowChanges(uid, mergedRowChanges);
    this.onChange(Object.values(this.rowChangesService.getChanges()));
  }

  public getNavigationUrl(rowData: ItemListJobDto): string {
    return this.extNavigationService.transformUrl(
      generateNavigationUrl(rowData)
    );
  }
}
