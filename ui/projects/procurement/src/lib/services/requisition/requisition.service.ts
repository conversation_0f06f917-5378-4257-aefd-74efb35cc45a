import { Injectable } from '@angular/core';

import { SearchResponse } from '@j3-prc-shared/dtos/odata';
import { supplant } from '@j3-procurement/dtos';
import { FinanceAdditionalChargeDto } from '@j3-procurement/dtos/finance';
import {
  QuotationChargesDto,
  QuotationItemDetails,
  QuotationItemStateDto,
  QuotationTimestamp,
} from '@j3-procurement/dtos/quotation';
import {
  CloseRequisitionDto,
  CloseRequisitionRequestDto,
  CreateRequisitionDto,
  CreateRequisitionResponseDto,
  DuplicateRequisitionDto,
  DuplicateRequisitionResponseDto,
  DuplicateRequisitionValidationResponseDto,
  RequisitionDetailsDto,
  RequisitionItemDto,
  RequisitionItemFinanceDto,
  RequisitionPendingWithMeDto,
  RequisitionSummaryDto,
  RequisitiontItemFilterOptionsDto,
  UpdateRequisitionDto,
  UpdateRequisitionResponseDto,
} from '@j3-procurement/dtos/requisition';
import {
  CreateRfqsRequestDto,
  SupplierRfqsDto,
} from '@j3-procurement/dtos/rfq';
import { ApiRequestService, eCrud, WebApiRequest } from 'jibe-components';
import { BehaviorSubject, Observable } from 'rxjs';

import { StatusLabel } from '@j3-procurement/dtos/label';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';

interface PageState {
  additionalCharges: FinanceAdditionalChargeDto[];
  financialItems: RequisitionItemFinanceDto[];
  mandatoryQuotationCharges?: QuotationChargesDto;
  quotationItemState: QuotationItemStateDto[];
  itemDetails: Record<string, QuotationItemDetails>;
  quotationTimestamps: QuotationTimestamp[];
  suppliers: StatusLabel[];
}

export function getRequisitionItemsReq(requisitionUid: string): WebApiRequest {
  return {
    apiBase: ePrcRequestApiBase.j3ProcurementAPI,
    entity: ePrcRequestEntity.Requisition,
    action: supplant(ePrcRequestAction.RequisitionItemsList, {
      requisitionUid,
    }),
    crud: eCrud.Post,
  };
}

@Injectable({
  providedIn: 'root',
})
export class RequisitionService {
  constructor(private apiRequestService: ApiRequestService) {}

  public pageState$ = new BehaviorSubject<PageState>({
    additionalCharges: [],
    financialItems: [],
    quotationItemState: [],
    itemDetails: {},
    suppliers: [],
    mandatoryQuotationCharges: null,
    quotationTimestamps: [],
  });

  public closeRequisition(
    requisitionUid: string,
    body?: CloseRequisitionRequestDto
  ): Promise<CloseRequisitionDto> {
    return this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.Requisition,
        action: supplant(ePrcRequestAction.RequisitionClosing, {
          requisitionUid,
        }),
        crud: eCrud.Post,
        body,
      })
      .toPromise();
  }

  public createRfqs(
    body: CreateRfqsRequestDto,
    requisitionUid: string
  ): Promise<SupplierRfqsDto[]> {
    const updateSupplier: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionRfqs, { requisitionUid }),
      body,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(updateSupplier).toPromise();
  }

  public getDuplicateRequisitionValidation(
    requisitionUid: string,
    body: DuplicateRequisitionDto
  ): Promise<DuplicateRequisitionValidationResponseDto> {
    const createRequisitionRequest: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionDuplicateValidations, {
        requisitionUid,
      }),
      body,
      crud: eCrud.Post,
    };
    return this.apiRequestService
      .sendApiReq(createRequisitionRequest)
      .toPromise();
  }

  public duplicateRequisition(
    requisitionUid: string,
    body: DuplicateRequisitionDto
  ): Observable<DuplicateRequisitionResponseDto> {
    const createRequisitionRequest: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/duplicate`,
      body,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(createRequisitionRequest);
  }

  createRequisition(
    body: CreateRequisitionDto
  ): Observable<CreateRequisitionResponseDto> {
    const createRequisitionRequest: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      body,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(createRequisitionRequest);
  }

  public countPendingItemsAndJobsRequest(
    requisitionUid: string
  ): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionPendingItemsJobsCount, {
        requisitionUid,
      }),
      crud: eCrud.Post,
    };
  }

  public countPendingItemsAndJobs(requisitionUid: string): Promise<number> {
    const request = this.countPendingItemsAndJobsRequest(requisitionUid);
    return this.apiRequestService.sendApiReq(request).toPromise();
  }

  public getJobsRequest(requisitionUid: string): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/jobs/list`,
      crud: eCrud.Post,
    };
  }

  getRequisition(requisitionUid: string): Observable<RequisitionDetailsDto> {
    const requisitionHeaderDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: requisitionUid,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(requisitionHeaderDetails);
  }

  getRequisitionSummary(
    requisitionUid: string
  ): Observable<RequisitionSummaryDto> {
    const requisitionHeaderDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/summary`,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(requisitionHeaderDetails);
  }

  getRequisitionSegments(requisitionUid: string): Observable<string[]> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionSegments, {
        requisitionUid,
      }),
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(request);
  }

  public getRequisitionSuppliersReq(
    requisitionUid: string,
    deliveryPortId: number
  ): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionSupplierList, {
        requisitionUid,
      }),
      crud: eCrud.Post,
      body: { deliveryPortId },
    };
  }

  public getRequisitionItemFiltersRequest(
    requisitionUid: string
  ): WebApiRequest {
    return {
      action: supplant(ePrcRequestAction.RequisitionItemsFilterOptions, {
        requisitionUid,
      }),
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      crud: eCrud.Get,
      entity: ePrcRequestEntity.Requisition,
    };
  }

  updateRequisition(
    uid: string,
    dto: UpdateRequisitionDto
  ): Observable<UpdateRequisitionResponseDto> {
    const requisitionHeaderDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: uid,
      crud: eCrud.Put,
      body: dto,
    };
    return this.apiRequestService.sendApiReq(requisitionHeaderDetails);
  }

  public getRequisitionItems(
    requisitionUid: string
  ): Promise<SearchResponse<RequisitionItemDto>> {
    const request = getRequisitionItemsReq(requisitionUid);
    return this.apiRequestService
      .sendApiReq({
        ...request,
        odata: {
          orderby: 'runningNumber asc',
          select: 'itemUid,catalogUid',
          count: 'false',
        },
      })
      .toPromise();
  }

  getPendingWithMeCount(body: RequisitionPendingWithMeDto): Observable<number> {
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: ePrcRequestAction.RequisitionPendingWithMeCount,
      crud: eCrud.Post,
      body,
    });
  }
  getRequisitionParentCatalogs(requisitionUid: string): Observable<string[]> {
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionParentCatalogs, {
        requisitionUid,
      }),
      crud: eCrud.Get,
    });
  }

  getRequisitionItemFilters(
    filtersRequest: WebApiRequest
  ): Promise<RequisitiontItemFilterOptionsDto> {
    return this.apiRequestService.sendApiReq(filtersRequest).toPromise();
  }

  getRequisitionDirect(requisitionUid: string): Promise<boolean> {
    return this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.Requisition,
        action: supplant(ePrcRequestAction.RequisitionDirect, {
          requisitionUid,
        }),
        crud: eCrud.Get,
      })
      .toPromise();
  }
}
