<jb-layout-widget titleText="Suppliers" titleIconClass="icons8-supplier-2">
  <div awidget-header-buttons class="p-grid-supgrid">
    <div class="iconalign">
      <span
        class="icons8-menu-2-filled"
        (click)="settingsPanel.toggle($event)"
      ></span>
      <p-overlayPanel #settingsPanel appendTo="body" dismissable="true">
        <div class="p-grid moreActions">
          <ng-container *ngFor="let moreAction of supplierMoreActionsArr">
            <span
              class="p-col-12 invoice-panel"
              (click)="onMoreActions(moreAction.key)"
              >{{ moreAction.value }}</span
            >
          </ng-container>
        </div>
      </p-overlayPanel>
    </div>
    <div class="left">
      <jb-button
        [type]="buttonTypeNoButton"
        label="Select Supplier"
        class="action-button"
        name="addSupplier"
        [disabled]="readonlyMode || !hasAddEditSupplierAccess"
        (click)="addSupplierPopup()"
      ></jb-button>
    </div>
    <div class="right">
      <jb-button
        type="NoButton"
        label="Send RFQ"
        class="action-button"
        [disabled]="
          isSendingRfq ||
          readonlyMode ||
          disableSendRFQBtn ||
          !(permission.SendRfq | hasPermissions)
        "
        name="sendRFQ"
        (click)="sendRfq(rfqUids)"
      ></jb-button>
    </div>
  </div>
  <div class="supplier-grid" *ngIf="gridInputs" awidget-content>
    <jb-grid
      selectionMode="multiple"
      [colData]="gridInputs.columns"
      [filterData]="gridInputs.filters"
      [filterListsSet]="gridInputs.filtersLists"
      [getStyleByContainer]="true"
      [gridName]="gridInputs.gridName"
      [isDisplayAdvancedSettings]="false"
      [searchFields]="gridInputs.searchFields"
      [setActions]="mainGridAction"
      [tableData]="gridInputs.data"
      [tableDataReq]="gridInputs.request"
      (action)="onRfqActionSelected($event)"
      (cellChange)="onCellChange($event)"
      (matrixSelection)="emitSelectedRfq($event)"
    ></jb-grid>
    <ng-template #rfqSendDateTemplate let-rowData>
      <div class="template-column">
        <span class="text-ellipsis" [prcTooltip]="rowData.rfqSendDate | jbDate">
          {{ rowData.rfqSendDate | jbDate }}
        </span>
      </div>
    </ng-template>
    <ng-template #dueDateTemplate let-rowData>
      <div class="template-column">
        <span
          class="text-ellipsis"
          [prcTooltip]="rowData.quotationDueDate | jbDate"
        >
          {{ rowData.quotationDueDate | jbDate }}
        </span>
      </div>
    </ng-template>
    <ng-template #supplierIconTemplate let-rowData>
      <span>
        <i *ngIf="rowData.contracted" class="icons8-prize"></i>
      </span>
    </ng-template>
  </div>
</jb-layout-widget>

<ng-template #selectedItemTemplate let-rowData>
  <span>
    <a
      class="supplieritem_link"
      (click)="openSupplierItemPopup()"
      *ngIf="rowData.totalItems && !readonlyMode"
      >{{ rowData.itemCount }}/{{ rowData.totalItems }}
    </a>
    <div *ngIf="rowData.totalItems && readonlyMode">
      {{ rowData.itemCount }}/{{ rowData.totalItems }}
    </div>
  </span>
</ng-template>

<ng-template #rfqStatusTemplate let-rowData>
  <div
    *ngIf="rowData.rfqStatusUid | prcGetRfqDisplayName | async as displayName"
    [prcOverlay]="canShowDeclineReasonOverlay(rowData) ? overlay : null"
  >
    {{ displayName }}
  </div>

  <prc-column-overlay #overlay>
    <prc-decline-object
      [declineRemark]="rowData.declineRemark"
      [declineReasons]="rowData.declineReasons"
      [isOverlay]="true"
    ></prc-decline-object>
  </prc-column-overlay>
</ng-template>

<ng-template #commentIconTemplate let-rowData>
  <span class="commentIcon" (click)="addCommentPopup(rowData?.rfqRemark)">
    <i class="icons8-chat-room"></i>
  </span>
</ng-template>

<ng-template #ccEmailsTemplate let-rowData>
  <span (click)="openCCEmails(rowData)">
    <i class="icons8-message" pTooltip="CC Emails"></i>
  </span>
</ng-template>

<ng-template #supplierNameTemplate let-rowData>
  <span class="template-ellipsis" [prcTooltip]="rowData.supplierName">
    <a
      *ngIf="rowData.currentStatus === 'Approved'; else notApproved"
      class="jb_grid_topCellValue jb-link-600-14 text-ellipsis"
      [prcNavigationLink]="getSupplierUrl(rowData)"
      target="_blank"
    >
      {{ rowData.supplierName }}
    </a>
    <ng-template #notApproved>{{ rowData.supplierName }}</ng-template>
  </span>
</ng-template>

<div *ngIf="isDeliveryPortVisible && !readonlyMode">
  <prc-delivery-port-country
    [deliveryPortDialog]="isDeliveryPortVisible"
    (selectedPortDetails)="deliveryPortSelected($event)"
    (closePortsPopup)="isDeliveryPortVisible = false"
  >
  </prc-delivery-port-country>
</div>

<div *ngIf="isAddSupplierVisible">
  <lib-supplier-popup
    [requisitionUid]="requisition?.uid"
    [deliveryPort]="deliveryPort"
    [deliveryDate]="deliveryDate"
    [supplierDialog]="isAddSupplierVisible"
    [supplierType]="supplierType"
    [vesselId]="requisition?.vesselId"
    (closeSupplierPopup)="closeAddSupplierPopup()"
    (saveSupplierDetails)="supplierDetailsSaved()"
  ></lib-supplier-popup>
</div>
<div *ngIf="isSupplierItemVisible">
  <lib-supplier-items
    [requisitionUid]="requisition?.uid"
    [supplierItemDialog]="isSupplierItemVisible"
    [suppliersInfo]="supplierGridData"
    (closeSupplierItemPopup)="closeSupplierItemPopup()"
    (supplierItemsSaved)="onItemsSaved($event)"
  ></lib-supplier-items>
</div>

<div>
  <jb-dialog
    [dialogContent]="dialogContentConfirmDetails"
    [(dialogVisible)]="isDialogConfirmVisible"
    (closeDialog)="closeDeletePopup()"
    *ngIf="isDialogConfirmVisible"
  >
    <div jb-dialog-body>
      <div class="delete-pop-up-message">
        <p>Are you sure,you want to delete?</p>
        <div>
          <jb-button
            label="Cancel"
            type="NoButton"
            (click)="closeDeletePopup()"
          ></jb-button>
          <jb-button
            class="confirm-button"
            label="Confirm"
            [type]="buttonTypeStandard"
            (click)="removeItemRecord()"
          ></jb-button>
        </div>
      </div>
    </div>
  </jb-dialog>
</div>

<div>
  <jb-dialog
    [dialogContent]="dialogContentCommentDetails"
    [(dialogVisible)]="isDialogCommentVisible"
    (closeDialog)="closeCommentPopup()"
    *ngIf="isDialogCommentVisible"
  >
    <div jb-dialog-body>
      <div class="delete-pop-up-message">
        <p>{{ rfqRemark }}</p>
      </div>
    </div>
  </jb-dialog>
</div>

<prc-cc-emails-popup
  *ngIf="selectedRfqUid"
  [rfqUid]="selectedRfqUid"
  [ccEmails]="ccEmails"
  (close)="closeCCEmailsPopup($event)"
>
</prc-cc-emails-popup>
