import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  QuotationAdditionalDetailsDto,
  QuotationDto,
  QuotationState,
} from '@j3-procurement/dtos/quotation';
import {
  GridAction,
  GridComponent,
  GridRowActions,
  GridService,
  GridShareDataService,
  JbDatePipe,
  MatrixDataChanged,
} from 'jibe-components';
import f from 'odata-filter-builder';
import { combineLatest, Observable, Subject } from 'rxjs';
import { filter, startWith, take, takeUntil } from 'rxjs/operators';

import {
  ColumnKey,
  eQoutationSummaryActionTypes,
  getActions,
  getColumns,
  gridName,
} from './table-context';
import { currencyRadioContext, stateToIconMapper } from './utils';

import { calculateFinalAmount } from '@j3-procurement/dtos';
import { UnsubscribeComponent } from 'j3-prc-components';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { RequisitionPermission } from '../../models/enums/requisition-permission.enum';
import { GridInputsWithRequest } from '../../models/interfaces/grid-inputs';
import { MasterService } from '../../services/master/master.service';
import { MasterPortCountryDto } from '../../services/master/types';
import { PermissionService } from '../../services/permission/permission.service';
import { FormatPortPipe } from '../../shared/pipes/format-port.pipe';
import { CurrencyPipe } from '../currency-pipe/currency.pipe';
import { QuotationEvaluationActionsService } from '../quotation-evaluation-actions/quotation-evaluation-actions.service';
import { QuotationService } from '../quotation.service';
import { QuotationItemsSummary } from '../types';
import { eCurrency } from '../utils';
import { compareNumbers, compareStrings } from './compare-primitives';
import { contextTable as quotationContext } from './table-context';
import { QuotationRecord, SortColumn } from './types';

@Component({
  selector: 'prc-quotation-summary',
  templateUrl: './quotation-summary.component.html',
  styleUrls: ['./quotation-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridShareDataService],
})
export class QuotationSummaryComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() set initialQuotationUids(initialQuotationUids: string[]) {
    this.initialQuotationUids$.next(initialQuotationUids);
  }
  @Input() quotationList: QuotationDto[] = [];
  @Input() requisitionPortId: number;
  @Input() requisitionUid: string;
  @Input() set selectedItemsSummary(value: QuotationItemsSummary) {
    this.quotationItemsSummary = value;
    this.updateMatrixData();
  }

  @Output() selectedQuotationsChange = new EventEmitter<QuotationRecord[]>();

  @ViewChild('gridRef')
  gridRef: GridComponent;
  @ViewChild('supplierTemplate', { static: true })
  supplierTemplate: TemplateRef<HTMLElement>;
  @ViewChild('remarksTemplate', { static: true })
  remarksTemplate: TemplateRef<HTMLElement>;
  @ViewChild('texationTemplate', { static: true })
  texationTemplate: TemplateRef<HTMLElement>;
  @ViewChild('itemsAmountTemplate', { static: true })
  itemsAmountTemplate: TemplateRef<HTMLElement>;
  @ViewChild('discountTemplate', { static: true })
  discountTemplate: TemplateRef<HTMLElement>;
  @ViewChild('stateTemplate', { static: true })
  stateTemplate: TemplateRef<HTMLElement>;
  @ViewChild('truckTemplate', { static: true })
  truckTemplate: TemplateRef<HTMLElement>;
  @ViewChild('packagingTemplate', { static: true })
  packagingTemplate: TemplateRef<HTMLElement>;
  @ViewChild('freightTemplate', { static: true })
  freightTemplate: TemplateRef<HTMLElement>;
  @ViewChild('bargeTemplate', { static: true })
  bargeTemplate: TemplateRef<HTMLElement>;
  @ViewChild('otherTemplate', { static: true })
  otherTemplate: TemplateRef<HTMLElement>;
  @ViewChild('currencyTemplate', { static: true })
  currencyTemplate: TemplateRef<HTMLElement>;
  @ViewChild('itemsTemplate', { static: true })
  itemsTemplate: TemplateRef<HTMLElement>;
  @ViewChild('selectedItemsAmountTemplate', { static: true })
  selectedItemsAmountTemplate: TemplateRef<HTMLElement>;
  @ViewChild('finalAmountTemplate', { static: true })
  finalAmountTemplate: TemplateRef<HTMLElement>;

  public selectedQuotations$: Observable<QuotationRecord[]>;
  private initialQuotationUids$ = new Subject<string[]>();
  private matrixValuesMapped$ = new Subject<QuotationRecord[]>();
  private portNameMap: Record<number, string>;
  private quotationItemsSummary: QuotationItemsSummary;

  data: QuotationDto[];
  gridInputs: GridInputsWithRequest<ColumnKey> = quotationContext;
  additionalDetails: QuotationAdditionalDetailsDto;
  quotationActions: GridRowActions[];
  currencyRadioContext = currencyRadioContext;
  eCurrencyType = eCurrency;
  selectedCurrency: eCurrency;
  selectedRow: QuotationRecord;
  columnFieldToCellTemplateMapper: Partial<
    Record<ColumnKey, TemplateRef<HTMLElement>>
  >;
  stateToIcon = stateToIconMapper;
  eQoutationSummaryActionTypesType = eQoutationSummaryActionTypes;
  selectedAction: {
    type: eQoutationSummaryActionTypes;
    rowData: QuotationRecord;
  };
  remarkDialogVisible = false;

  constructor(
    private readonly actionsService: QuotationEvaluationActionsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly currencyPipe: CurrencyPipe,
    private readonly datePipe: JbDatePipe,
    private readonly gridService: GridService,
    private readonly gridShareDataService: GridShareDataService,
    private readonly masterService: MasterService,
    private readonly permissionService: PermissionService,
    private readonly quotationService: QuotationService
  ) {
    super();
    this.selectedQuotations$ = this.selectedQuotationsChange.asObservable();
  }

  ngOnInit(): void {
    this.selectedCurrency = currencyRadioContext.defalut;
    this.columnFieldToCellTemplateMapper = {
      state: this.stateTemplate,
      supplierRegisteredName: this.supplierTemplate,
      remarks: this.remarksTemplate,
      taxation: this.texationTemplate,
      itemsAmount: this.itemsAmountTemplate,
      discount: this.discountTemplate,
      truck: this.truckTemplate,
      packaging: this.packagingTemplate,
      freight: this.freightTemplate,
      barge: this.bargeTemplate,
      other: this.otherTemplate,
      currency: this.currencyTemplate,
      selectedItems: this.itemsTemplate,
      amountSelectedItems: this.selectedItemsAmountTemplate,
      finalAmount: this.finalAmountTemplate,
    };
    this.setSummaryRowDisplay();
    this.setQuotationActions();
    this.loadGridInitiators();
    this.loadQuotationSummaryData();
    this.initSelectedQuotations();

    this.gridService.matrixDataChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === gridName)
      )
      .subscribe(async ({ matrixValues }: MatrixDataChanged<QuotationDto>) => {
        if (!matrixValues?.length) {
          this.matrixValuesMapped$.next([]);
          return;
        }

        const portNameMap = await this.getPortNameMap(matrixValues);
        const newMatrixValues = matrixValues.map((quotation) => ({
          ...quotation,
          portName:
            portNameMap[quotation?.portId ?? this.requisitionPortId] ?? '',
          taxation: quotation.taxation ?? 0,
          discount: quotation.discount ?? 0,
        }));

        this.matrixValuesMapped$.next(newMatrixValues);
        this.gridService.storeData$.next({
          gridName,
          data: newMatrixValues,
        });
      });
  }

  private getMatrixData(): QuotationRecord[] {
    const { matrixApi } = this.gridShareDataService;
    return Array.isArray(matrixApi) ? matrixApi : [];
  }

  async loadQuotationSummaryData(): Promise<void> {
    const responseData = await this.quotationService
      .getQuotationList(this.requisitionUid)
      .toPromise();
    this.data = responseData?.records;
    this.cdr.markForCheck();
  }

  gridAction(
    action: GridAction<eQoutationSummaryActionTypes, QuotationRecord>
  ): void {
    if (action.type === eQoutationSummaryActionTypes.Rework) {
      this.showActionDialog(action.type, action.payload);
    }
  }

  sortByColumn({ field, order }: SortColumn): void {
    const matrixValues = [...this.gridService.getMatrixValue()];
    const percentValues = ['taxation', 'discount'];
    const firstElement = matrixValues[0];
    let sortFn;
    if (percentValues.includes(field)) {
      sortFn = (a, b): number => compareNumbers(a[field], b[field]);
    } else {
      if (typeof firstElement[field] === 'string') {
        sortFn = (a, b): number => compareStrings(a[field], b[field]);
      } else {
        sortFn = (a, b): number => {
          const currencyAmountA = this.currencyPipe.transform(
            a[field] as number,
            a.currencyRate,
            this.selectedCurrency
          );

          const currencyAmountB = this.currencyPipe.transform(
            b[field] as number,
            b.currencyRate,
            this.selectedCurrency
          );

          return compareNumbers(currencyAmountA, currencyAmountB);
        };
      }
    }
    this.gridService.storeData$.next({
      gridName,
      data: matrixValues.sort((a, b) => (order ?? 1) * sortFn(a, b)),
    });
  }

  public emitSelectedQuotations(
    selectedQuotionsList: QuotationRecord[] | QuotationRecord
  ): void {
    const list = [].concat(selectedQuotionsList ?? []);
    this.selectedQuotationsChange.emit(list);
  }

  loadGridInitiators(): void {
    this.gridInputs.columns.forEach((col) => {
      if (this.columnFieldToCellTemplateMapper?.[col.FieldName]) {
        col.cellTemplate = this.columnFieldToCellTemplateMapper[col.FieldName];
      }
    });
  }
  showActionDialog(
    action: eQoutationSummaryActionTypes,
    rowData: QuotationRecord
  ): void {
    this.selectedAction = { type: action, rowData: rowData };
  }

  closeActionDialog(event: QuotationState): void {
    const matrixData = this.getMatrixData();
    const data = matrixData.map((row: QuotationRecord) =>
      row.uid === this.selectedAction.rowData.uid
        ? { ...row, state: event }
        : row
    );

    this.gridService.matrixDataChangeRequest.next({ data, gridName });
    this.gridService.refreshGrid();
    this.selectedAction = null;
  }

  async showRemarkDialog(rowData: QuotationRecord): Promise<void> {
    this.additionalDetails = await this.quotationService
      .getQuotationAdditionalDetails(rowData.uid)
      .toPromise();
    this.selectedRow = rowData;
    this.remarkDialogVisible = true;
    this.cdr.markForCheck();
  }

  closeRemarkDialog(): void {
    this.remarkDialogVisible = false;
  }

  private async initSelectedQuotations(): Promise<void> {
    const [initialQuotationUids, matrixValues] = await combineLatest([
      this.initialQuotationUids$,
      this.matrixValuesMapped$,
    ])
      .pipe(filter(([initUids, values]) => Boolean(initUids && values.length)))
      .pipe(take(1))
      .toPromise();

    const quotations: QuotationRecord[] = matrixValues ?? [];

    if (initialQuotationUids) {
      const selectedQuotations = initialQuotationUids.length
        ? quotations.filter(({ uid }) => initialQuotationUids.includes(uid))
        : quotations;

      this.emitSelectedQuotations(selectedQuotations);
      // calling matrixApiBSLoad is workaround for update selectedMatrixValue in jb-grid
      this.gridShareDataService.matrixApiBSLoad([...quotations]);
    }
  }

  private async getPortNameMap(
    quotations: QuotationDto[]
  ): Promise<Record<number, string>> {
    if (!quotations?.length) {
      return {};
    }

    if (this.portNameMap) {
      return this.portNameMap;
    }

    const portIds = quotations.reduce(
      (accum, { portId }) => (portId ? [...accum, portId] : accum),
      [this.requisitionPortId]
    );
    const portFilter = f().in('PORT_ID', portIds, false);
    const ports = await this.masterService.getPortCountryList<
      MasterPortCountryDto[]
    >({
      filter: portFilter,
      count: 'false',
    });

    const formatPipe = new FormatPortPipe();
    this.portNameMap = ports.reduce(
      (accum, { PORT_COUNTRY: portCountry, PORT_NAME: portName, PORT_ID }) => {
        const name = formatPipe.transform({ portName, portCountry });
        return { ...accum, [PORT_ID]: name };
      },
      {}
    );

    return this.portNameMap;
  }

  stateTooltip(rowData: QuotationRecord): string {
    const date =
      rowData.state === 'reworked' ? rowData.receivedDate : rowData.timestamp;
    return `${rowData.state} ${this.datePipe.transform(date)}`;
  }

  public getSupplierLink(clientSupplierUid: string): string {
    return (
      clientSupplierUid ??
      ePageRoutes.ContactCardRoute +
        btoa(JSON.stringify({ Supplier_code: clientSupplierUid }))
    );
  }

  onCurrencyChange(payload: eCurrency): void {
    this.actionsService.dispatchAction({
      type: 'currencyConverter',
      payload,
    });
  }

  private async setQuotationActions(): Promise<void> {
    const hasPermission = !(await this.permissionService.hasPermissions(
      RequisitionPermission.SendQuotationReworkRequest
    ));
    this.quotationActions = getActions(hasPermission);
    this.cdr.markForCheck();
  }

  private updateMatrixData(): void {
    const matrixData = this.getMatrixData();
    const data = matrixData.map((row: QuotationRecord) => {
      const { amount = 0, items = 0 } =
        this.quotationItemsSummary?.[row.uid] ?? {};
      return {
        ...row,
        amountSelectedItems: amount,
        selectedItems: items,
        finalAmount: row ? calculateFinalAmount(row, amount, 1) : 0,
      };
    });

    this.gridService.matrixDataChangeRequest.next({ data, gridName });
    this.gridService.refreshGrid();
  }

  public setSummaryRowDisplay(): void {
    combineLatest([
      this.selectedQuotations$,
      this.actionsService
        .listenActions(['currencyConverter'])[0]
        .action.pipe(startWith(this.selectedCurrency)),
    ])
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(([selectedQuotaitonList, selectedCurrency]) => {
        const validCurrencyRate = selectedQuotaitonList?.every((quote) =>
          selectedCurrency === eCurrency.Org
            ? quote.currency === selectedQuotaitonList[0].currency
            : quote.currencyRate
        );
        this.gridInputs = {
          ...this.gridInputs,
          columns: getColumns(validCurrencyRate),
        };
      });
  }
}
