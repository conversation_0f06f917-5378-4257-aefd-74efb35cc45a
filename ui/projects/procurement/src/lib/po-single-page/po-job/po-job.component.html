<jb-grid
  [colData]="gridInputs.columns"
  [filterData]="gridInputs.filters"
  [filterListsSet]="gridInputs.filtersLists"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [searchFields]="gridInputs.searchFields"
  [setActions]="isEditMode && !readonly ? gridInputs.actions : null"
  [tableData]="gridInputs.data"
  [tableDataReq]="gridInputs.request"
  [exportExtraColumn]="exportExtraColumn"
  (action)="onGridAction($event)"
></jb-grid>

<ng-template #servicePerformanceTemplate let-rowData>
  {{ servicePerformances[rowData.servicePerformanceUid] }}
</ng-template>

<ng-template #deliveryPortTemplate let-rowData>
  {{ ports[rowData.deliveryPortId] }}
</ng-template>

<ng-template #statusTemplate let-rowData>
  <span class="status-column" [ngClass]="rowData.status | lowercase">{{
    rowData.status
  }}</span>
</ng-template>
<ng-template #descriptionTemplate let-rowData>
  <div class="description-column">
    <ng-container *ngIf="rowData.description || ' ' | stripHtml as description">
      <span class="text-ellipsis" [prcTooltip]="description">
        {{ description }}
      </span>
    </ng-container>

    <img
      class="external-link-icon"
      src="assets/procurement/images/external-link.svg"
      (click)="openDescriptionPopup(rowData)"
    />
  </div>
</ng-template>
<ng-template #componentTemplate let-rowData>
  <div class="description-column">
    <span class="text-ellipsis" [prcTooltip]="rowData.catalogPath">
      {{ rowData.catalogName }}
    </span>
  </div>
</ng-template>

<ng-template #attachmentTemplate let-rowData>
  <i
    *ngIf="rowData.hasAttachments"
    class="icons8-attach"
    (click)="openAttachmentsPopup(rowData)"
  ></i>
</ng-template>

<jb-dialog
  [dialogContent]="content"
  [dialogVisible]="popupDescription"
  (dialogVisibleChange)="onCloseDescriptionPopup()"
>
  <ng-container jb-dialog-body>
    {{ popupDescription }}
  </ng-container>
  <ng-container jb-dialog-footer>
    <jb-dialog-footer
      okBtnLabel="OK"
      cancelBtnLabel="Cancel"
      (ok)="onCloseDescriptionPopup()"
      (cancel)="onCloseDescriptionPopup()"
    ></jb-dialog-footer>
  </ng-container>
</jb-dialog>

<prc-po-job-delivery-popup
  *ngIf="isDeliveryPopupVisible"
  [data]="deliveryDetails"
  [poUid]="poUid"
  [vesselId]="vesselId"
  [action]="deliveryAction"
  (close)="onCloseDeliveryDetailsPopup()"
  (save)="onSaveDeliveryDetailsPopup()"
  (actionChange)="onActionChange($event)"
></prc-po-job-delivery-popup>

<prc-po-job-attachments-popup
  *ngIf="isPoJobAttachmentsPopupVisible"
  [poJobUid]="currentPoJobUid"
  [poUid]="poUid"
  [vesselId]="vesselId"
  (close)="onClosePoJobAttachmentsPopup()"
></prc-po-job-attachments-popup>

<jb-integration-log
  *ngIf="viewIntegrationLogObject"
  [dialogVisible$]="showIntegrationLogPopup$"
  [functionCode]="viewIntegrationLogObject.functionCode"
  [moduleCode]="viewIntegrationLogObject.moduleCode"
  [entityUid]="viewIntegrationLogObject.entityUid"
></jb-integration-log>

<ng-template #codeTemplate let-rowData>
  <a
  class="jb_grid_topCellValue jb-link-600-14"
  target="_blank"
  [href]="getNavigationUrl(rowData)"
  >{{ rowData.code }}
</a>
 </ng-template>
