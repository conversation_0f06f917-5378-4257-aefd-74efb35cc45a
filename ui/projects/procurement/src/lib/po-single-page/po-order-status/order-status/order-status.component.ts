import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  PoStatusBranchDto,
  PoStatusBranchName,
} from '@j3-procurement/dtos/po-status-branch';
import { JbDatePipe } from 'jibe-components';
import { PoPermission } from '../../../models/enums/po-permission.enum';
import { prcOrderStatusIcons } from '../../../models/enums/prc-order-status-icons.enum';
import { readonlyBranches } from './constant';

@Component({
  selector: 'prc-order-status',
  templateUrl: './order-status.component.html',
  styleUrls: ['./order-status.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderStatusComponent implements OnInit {
  @Input() item: PoStatusBranchDto;
  @Input() hideLink: boolean;
  public orderStatusIcons: Map<PoStatusBranchName, string> =
    prcOrderStatusIcons;

  @Input() slfPermission: 'edit' | 'view' | undefined;

  public viewUpdateStatus = false;
  public viewMoreDetails = false;

  @Output() updateStatusClick = new EventEmitter<PoStatusBranchDto>();
  @Input() poUid: string;
  public permission = PoPermission;
  showUpdateStatus: boolean;
  showExpandArrow: boolean;
  constructor(private jbDate: JbDatePipe) {}

  ngOnInit(): void {
    const canView =
      this.slfPermission === 'view' || this.slfPermission === 'edit';
    this.showUpdateStatus = !(this.item.isDecline || this.item.isComplete);
    this.showExpandArrow = !readonlyBranches.includes(this.item.status);
    this.viewUpdateStatus =
      canView && this.showExpandArrow && this.showUpdateStatus;
    this.viewMoreDetails =
      canView && this.showExpandArrow && !this.showUpdateStatus;
  }

  formatDate(date: Date): string {
    return this.jbDate.transform(date);
  }

  openStatus(): void {
    this.updateStatusClick.emit(this.item);
  }
}
