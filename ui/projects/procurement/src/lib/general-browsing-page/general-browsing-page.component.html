<div class="general-browsing-page-container">
  <div class="header">
    <prc-search-filter-bar
      [nodes]="selectedNodes"
      [showAddItem]="showAddItem"
      (navigationChange)="loadItems($event)"
      (searchTextClear)="loadItems()"
      (segmentChange)="onSegmentChanged($event)"
      (vesselChange)="onVesselChanged($event)"
    ></prc-search-filter-bar>
  </div>

  <div class="main" *ngIf="isFiltersSelected; else segmentNotSelected">
    <prc-sidebar>
      <prc-general-browsing-sidebar
        slot="sidebar-content"
        [codes]="sidebarCodes"
        [visibleFilters]="visibleFilters"
        (selectionChange)="onFilterChanged($event)"
      ></prc-general-browsing-sidebar>

      <prc-tree-filter
        (selectionChange)="onNodeSelected($event)"
        [visibleNodeUids]="visibleNodeUids"
        [segment]="segment"
        [vesselUid]="vesselUid"
      ></prc-tree-filter>

      <prc-general-browsing-results
        class="items-view"
        [itemConfig]="request"
        [pageData]="pageData$ | async"
        [segmentType]="segment?.type"
        (getResults)="getResults($event)"
      ></prc-general-browsing-results>
    </prc-sidebar>
  </div>
</div>

<ng-template #segmentNotSelected>
  <prc-start-search></prc-start-search>
</ng-template>
