<jb-dialog
  [dialogContent]="dialogContent"
  [dialogVisible]="true"
  (dialogVisibleChange)="close.emit()"
>
  <div jb-dialog-body class="requisition-dialog-body">
    <form [formGroup]="form" *ngIf="data.vesselUid as vesselUid">
      <div class="two-columns">
        <div class="column">
          <prc-form-label label="Vessel">
            <input
              [value]="vesselUid | vesselName | async"
              pInputText
              class="jb-text disabled"
              disabled
            />
          </prc-form-label>
          <prc-form-label label="Purchase Type" [required]="true">
            <jb-single-select-dropdown
              appendTo="body"
              class="margin-top"
              [content]="poTypeDropdown"
            ></jb-single-select-dropdown>
          </prc-form-label>
          <prc-form-label label="Urgency" [required]="true">
            <jb-single-select-dropdown
              appendTo="body"
              class="margin-top"
              [content]="urgencyDropdown"
            ></jb-single-select-dropdown>
          </prc-form-label>
          <prc-form-label
            *ngIf="data.itemListUid"
            label="Include Items From Other Lists"
          >
            <jb-multi-select-dropdown
              appendTo="body"
              class="margin-top"
              [content]="itemListDropdown"
            ></jb-multi-select-dropdown>
          </prc-form-label>
        </div>
        <div class="column">
          <prc-form-label label="Port Call">
            <prc-upcoming-port-select
              formControlName="deliveryUpcomingPort"
              (selectedPortChange)="onUpcomingPortChange($event)"
              [vesselId]="vesselId"
            ></prc-upcoming-port-select>
          </prc-form-label>
          <prc-form-label label="Delivery Port" [required]="true">
            <prc-generic-port-select
              formControlName="deliveryPort"
              (selectedPortChange)="onDeliveryPortChange($event)"
            ></prc-generic-port-select>
          </prc-form-label>
          <prc-form-label label="Delivery Date" [required]="true">
            <p-calendar
              appendTo="body"
              formControlName="deliveryDate"
              inputStyleClass="jb-text"
              yearRange="2000:2030"
              [dateFormat]="userDateControl"
              [monthNavigator]="true"
              [yearNavigator]="true"
            ></p-calendar>
          </prc-form-label>
        </div>
      </div>
      <prc-form-label
        class="form-label-details"
        label="Subject"
        [required]="true"
      >
        <jb-textarea [content]="subjectTextArea"></jb-textarea>
      </prc-form-label>
    </form>
  </div>
  <jb-dialog-footer
    jb-dialog-footer
    cancelBtnLabel="Cancel"
    okBtnLabel="Save"
    [isOkBtnDisabled]="form.invalid"
    (cancel)="close.emit()"
    (ok)="onSave()"
  >
  </jb-dialog-footer>
</jb-dialog>
