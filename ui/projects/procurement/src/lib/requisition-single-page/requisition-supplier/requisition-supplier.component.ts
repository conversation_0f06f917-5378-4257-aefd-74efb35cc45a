import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { PrcFunctionCodes, PrcModuleCodes } from '@j3-procurement/dtos';
import {
  RequisitionDetailsDto,
  RequisitionSupplierDto,
} from '@j3-procurement/dtos/requisition';
import {
  CreateRfqsRequestDto,
  RfqActionType,
  RfqSupplierDto,
  RfqSupplierRequestDto,
} from '@j3-procurement/dtos/rfq';
import {
  ExtNavigationService,
  ModalDialogService,
  NotificationService,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  eColor,
  eGridCellType,
  eGridEvents,
  eIconNames,
  GridAction,
  GridRowActions,
  GridService,
  GridShareDataService,
  IJbDialog,
  MatrixDataChanged,
} from 'jibe-components';
import { combineLatest, from } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import {
  ePrcConfirmLabel,
  ePrcWarnMessages,
} from '../../models/enums/prc-messages.enum';
import {
  GridInputsWithData,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import {
  actions,
  dialogContentCommentDetails,
  dialogContentConfirmDetails,
  filters,
  getColumns,
  getFiltersLists,
  gridName,
  moreActionsArr,
  searchFields,
} from './requisition-supplier-grid-inputs';
import { ColumnKey, rfqActionLabels, RfqActions } from './types';

import { SearchResponse } from '@j3-prc-shared/dtos/odata';
import { IdLabel } from '@j3-procurement/dtos/label';
import { saveAs } from 'file-saver';
import { UnsubscribeComponent } from 'j3-prc-components';
import { eSwitchCaseParams } from '../../models/enums/prc-main.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { ePrcSuccessMessages } from '../../models/enums/prc-single-page.enum';
import { RequisitionPermission } from '../../models/enums/requisition-permission.enum';
import { JbCellChangeEvent } from '../../models/interfaces/jb-cell-change-event';
import { DeliveryInstructionService } from '../../services/delivery-instruction/delivery-instruction.service';
import { JCDSService } from '../../services/jcds.service';
import { PermissionService } from '../../services/permission/permission.service';
import { PrcSharedService } from '../../services/prc-shared.service';
import { RequisitionService } from '../../services/requisition/requisition.service';
import { RfqService } from '../../services/rfq/rfq.service';
import { SupplierService } from '../../services/supplier/supplier.service';
import { RowChangesService } from '../../shared/single-page-sections/base-section/row-changes.service';
import { WorkflowApmBtnService } from '../../workflow/services/workflow-apm-btn.service';
import { SendRfqEvent } from '../types';
import { eFieldNames } from './requisition-supplier.enum';
import { DeliveryPort } from './supplier-popup/types';

@Component({
  selector: 'lib-requisition-supplier',
  templateUrl: './requisition-supplier.component.html',
  styleUrls: ['./requisition-supplier.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridShareDataService, RowChangesService],
})
export class RequisitionSupplierComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public disableSendRFQBtn = true;
  public supplierGridData: RequisitionSupplierDto[] = [];
  public selectedRfqUid: string;

  buttonTypeStandard = 'Standard';
  buttonTypeNoButton = 'NoButton';

  clickedRowIndex: number;

  dialogContentConfirmDetails: IJbDialog = dialogContentConfirmDetails;
  dialogContentCommentDetails: IJbDialog = dialogContentCommentDetails;

  gridAction: GridAction<RfqActions, RequisitionSupplierDto>;
  hasAddEditSupplierAccess: boolean;
  public refSentUid: string;
  public rfqDeclinedUid: string;

  isAddSupplierVisible = false;
  isAddEditQuotation: boolean;
  isActiveComponent = true;
  isSupplierItemVisible = false;
  isDeliveryPortVisible = false;
  isDialogConfirmVisible = false;
  isDialogCommentVisible = false;
  isModified: boolean;
  mainGridAction: GridRowActions[] = [];
  readonlyMode: boolean;
  selectedSuppliers: RequisitionSupplierDto[] = [];
  supplierMoreActionsArr = moreActionsArr;

  rfqRemark: string;
  rfqUids: string[] = [];
  rfqSupplierActions: Record<RfqActions, GridRowActions>;
  uid: string;

  public deliveryPort: DeliveryPort;
  public deliveryDate: Date;
  public ccEmails: string[] = [];

  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithData<ColumnKey, RequisitionSupplierDto>;

  @Input() isSendingRfq = false;
  @Input() requisition: RequisitionDetailsDto;
  @Input() set readonly(value: boolean) {
    this.readonlyMode = value ?? false;
    this.setColumnsEditable();
  }
  @Input() supplierType: string;
  @Output() getSupplierData = new EventEmitter();
  @Output() saveRequisitionSupplierData = new EventEmitter();
  @Output() requisitionTimestamp = new EventEmitter();
  @Output() rfqSent = new EventEmitter<SendRfqEvent>();

  @ViewChild('supplierNameTemplate', { static: true })
  supplierNameTemplate: TemplateRef<HTMLElement>;
  @ViewChild('selectedItemTemplate', { static: true })
  selectedItemTemplate: TemplateRef<HTMLElement>;
  @ViewChild('commentIconTemplate', { static: true })
  commentIconTemplate: TemplateRef<HTMLElement>;
  @ViewChild('supplierIconTemplate', { static: true })
  supplierIconTemplate: TemplateRef<HTMLElement>;
  @ViewChild('dueDateTemplate', { static: true })
  dueDateTemplate: TemplateRef<HTMLElement>;
  @ViewChild('rfqSendDateTemplate', { static: true })
  rfqSendDateTemplate: TemplateRef<HTMLElement>;
  @ViewChild('rfqStatusTemplate', { static: true })
  rfqStatusTemplate: TemplateRef<HTMLElement>;
  @ViewChild('ccEmailsTemplate', { static: true })
  ccEmailsTemplate: TemplateRef<HTMLElement>;

  public permission = RequisitionPermission;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly cdService: CentralizedDataService,
    private readonly deliveryInstructionService: DeliveryInstructionService,
    private readonly extNavigationService: ExtNavigationService,
    private readonly gridService: GridService,
    private readonly gridShareDataService: GridShareDataService,
    private readonly jcdsService: JCDSService,
    private readonly modalDialogService: ModalDialogService,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly prcSharedService: PrcSharedService,
    private readonly requisitionService: RequisitionService,
    private readonly rfqService: RfqService,
    private readonly rowChangesService: RowChangesService,
    private readonly supplierService: SupplierService,
    private readonly workflowApmBtnService: WorkflowApmBtnService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    this.rfqDeclinedUid = await this.rfqService.getUidByRfqStatus(
      'RFQ Declined'
    );
    this.refSentUid = await this.rfqService.getUidByRfqStatus('RFQ Sent');
    await this.getDeliveryInstructions(this.requisition.uid);
    await this.setHasAddEditSupplierAccess();
    this.setRfqSupplierActions();

    this.isAddEditQuotation = await this.permissionService.hasPermissions(
      RequisitionPermission.EditAddQuotation
    );
    this.mainGridAction = [
      {
        name: eGridEvents.Duplicate,
        icon: eIconNames.Duplicate,
        color: eColor.JbBlack,
        actionFunction: (arg) => this.setRfqActions(arg),
      },
    ];
    const columnTemplateMap: Map<ColumnKey, TemplateRef<HTMLElement>> = new Map(
      [
        ['supplierName', this.supplierNameTemplate],
        ['items', this.selectedItemTemplate],
        ['rfqRemark', this.commentIconTemplate],
        ['contracted', this.supplierIconTemplate],
        ['quotationDueDate', this.dueDateTemplate],
        ['rfqSendDate', this.rfqSendDateTemplate],
        ['rfqStatusUid', this.rfqStatusTemplate],
        ['ccEmails', this.ccEmailsTemplate],
      ]
    );
    this.gridInputs = {
      gridName,
      columns: getColumns(this.readonly).map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap.get(column.FieldName),
      })),
      searchFields,
      filters,
      filtersLists: getFiltersLists([], [], []),
      request: this.supplierService.getSupplierDetailsReq(this.requisition.uid),
      actions,
    };

    this.setFilterLists();
    combineLatest([
      this.gridService.matrixDataChanged,
      from(this.jcdsService.getData('supplierDecline')).pipe(
        map(
          (jcdsSupplierDecline) =>
            new Map<string, string>(
              jcdsSupplierDecline.map(({ _id, reasons }) => [_id, reasons])
            )
        )
      ),
    ])
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter(
          ([event]) => event.gridName === gridName && event.matrixCount > 0
        )
      )
      .subscribe(
        ([{ matrixValues }, jcdsSupplierDeclineMapped]: [
          MatrixDataChanged<RequisitionSupplierDto>,
          Map<string, string>
        ]) => {
          const newMatrixValues = matrixValues.map(
            (matrixValue): RequisitionSupplierDto => {
              const record = {
                ...matrixValue,
                ...this.rowChangesService.getRowChanges(matrixValue.uid),
              };

              record.declineReasons = record.declineReasons?.map((id) =>
                jcdsSupplierDeclineMapped.get(id)
              );
              return record;
            }
          );

          this.gridService.storeData$.next({ gridName, data: newMatrixValues });
        }
      );

    this.gridInputs.columns.forEach((column) => {
      if (column.hasOwnProperty(eGridCellType.Editable)) {
        column.Editable = this.hasAddEditSupplierAccess;
      }
    });
  }

  private async getDeliveryInstructions(requisitionUid: string): Promise<void> {
    const { deliveryPortDetails } = await this.deliveryInstructionService
      .getDeliveryInstruction(requisitionUid)
      .toPromise();
    if (!deliveryPortDetails) {
      return;
    }
    this.deliveryPort = {
      name: deliveryPortDetails.portName,
      id: deliveryPortDetails.portId,
      country: deliveryPortDetails.portCountry,
      code: deliveryPortDetails.portCode,
    };
    this.deliveryDate = deliveryPortDetails.requestedDeliveryDate;
  }

  private async setFilterLists(): Promise<void> {
    if (!this.requisition?.uid) {
      return;
    }
    const filterOptions = await this.supplierService
      .getSupplierFilters(this.requisition.uid)
      .toPromise();

    if (!filterOptions) {
      return;
    }

    const { suppliers, countries, deliveryPorts } = filterOptions;

    const suppliersFilter = suppliers?.map(({ name, uid }) => ({
      label: name,
      value: uid,
    }));
    const deliveryPortsFilter = deliveryPorts?.map(({ name, uid }) => ({
      label: name,
      value: uid,
    }));
    const countriesFilter = countries?.map(({ name, uid }) => ({
      label: name,
      value: uid,
    }));

    this.gridInputs.filtersLists = getFiltersLists(
      suppliersFilter,
      deliveryPortsFilter,
      countriesFilter
    );

    this.gridShareDataService.setFilters(
      null,
      true,
      this.gridInputs.filtersLists,
      this.gridInputs.filters
    );
  }

  private getMatrixData(): RequisitionSupplierDto[] {
    const matrixApi = this.gridShareDataService
      .matrixApi as unknown as SearchResponse<RequisitionSupplierDto>;
    return matrixApi?.records ?? [];
  }

  private getNewMatrixData(): RequisitionSupplierDto[] {
    const matrixValues = this.getMatrixData();
    return matrixValues.map((matrixValue): RequisitionSupplierDto => {
      return {
        ...matrixValue,
        ...this.rowChangesService.getRowChanges(matrixValue.uid),
      };
    });
  }

  private async setHasAddEditSupplierAccess(): Promise<void> {
    this.hasAddEditSupplierAccess = await this.permissionService.hasPermissions(
      RequisitionPermission.AddSupplier
    );

    this.cdr.markForCheck();
  }

  private setRfqActions(rfq: RequisitionSupplierDto): GridRowActions[] {
    const editQuotationAction = this.rfqSupplierActions['Edit Quotation'];
    const duplicateAction = this.rfqSupplierActions['Duplicate'];
    const statusId = this.workflowApmBtnService.getState()?.id;
    const disabled = statusId === 'CLOSE';

    const defaultActions = [
      this.rfqSupplierActions['Export RFQ (PDF)'],
      {
        ...editQuotationAction,
        disabled: !(
          disabled ||
          (rfq?.rfqStatusUid === this.refSentUid && this.isAddEditQuotation)
        ),
      },
      { ...duplicateAction, disabled },
    ];

    const rfqActions = rfqActionLabels[rfq?.state ?? 'Created'].map(
      (actionLabel) => {
        const rfqAction = this.rfqSupplierActions[actionLabel];
        rfqAction.disabled = disabled;
        return rfqAction;
      }
    );
    return [...defaultActions, ...rfqActions];
  }

  private setRfqSupplierActions(): void {
    this.rfqSupplierActions = {
      Duplicate: {
        name: 'Duplicate',
        icon: eIconNames.Duplicate,
        color: eColor.JbBlack,
        actionFunction: (payload) => this.duplicateSupplier(payload),
        disabled: !this.hasAddEditSupplierAccess,
      },
      'Export RFQ (PDF)': {
        name: 'Export RFQ (PDF)',
        icon: eIconNames.Document,
        color: eColor.JbBlack,
        actionFunction: (payload) => this.exportRfqAsPdf(payload),
        disabled: false,
      },
      'Edit Quotation': {
        name: 'Edit Quotation',
        icon: eIconNames.Edit,
        color: eColor.JbLightGreen,
        actionFunction: (payload) => this.navigateToSupplierPortal(payload),
      },
      'Remove from the list': {
        name: 'Remove from the list',
        icon: eIconNames.Cancel,
        color: eColor.JbRed,
        actionFunction: () => (this.isDialogConfirmVisible = true),
        disabled: !this.hasAddEditSupplierAccess,
      },
      'Send Reminder': {
        name: 'Send Reminder',
        icon: eIconNames.Sent,
        color: eColor.JbRed,
        actionFunction: (payload) =>
          this.sendRfq([payload.uid], 'sendReminder'),
      },
      'Resend RFQ': {
        name: 'Resend RFQ',
        icon: eIconNames.Resend,
        color: eColor.JbRed,
        actionFunction: (payload) => this.sendRfq([payload.uid], 'resendRfq'),
      },
      'Send RFQ': {
        name: 'Send RFQ',
        icon: eIconNames.Resend,
        color: eColor.JbRed,
        actionFunction: (payload) => this.sendRfq([payload.uid]),
      },
    };
  }

  addSupplierPopup(): void {
    this.isAddSupplierVisible = true;
  }
  closeAddSupplierPopup(): void {
    this.isAddSupplierVisible = false;
  }

  public addCommentPopup(rfqRemark: string): void {
    this.isDialogCommentVisible = true;
    this.rfqRemark = rfqRemark;
  }
  closeSupplierItemPopup(): void {
    this.isSupplierItemVisible = false;
    this.rowChangesService.resetChanges();
  }

  openSupplierItemPopup(): void {
    this.isSupplierItemVisible = true;
    this.supplierGridData = this.getNewMatrixData();
  }
  onMoreActions(supplierActionParam): void {
    if (supplierActionParam === eSwitchCaseParams.SupplierExportKey) {
      this.handleExportRfqAsPdf();
    }
  }

  async handleExportRfqAsPdf(): Promise<void> {
    await Promise.all(
      this.rfqUids.map((rfqUid) => this.exportRfqAsPdf({ uid: rfqUid }))
    );
  }

  supplierDetailsSaved(): void {
    this.closeAddSupplierPopup();
    this.gridService.refreshGrid(eGridEvents.Table, 'supplierGrid');
  }

  onItemsSaved(suppliers: RequisitionSupplierDto[]): void {
    this.closeSupplierItemPopup();
    const matrixValues = this.getMatrixData();
    const updatedSuppliers = suppliers.map((supplier) => {
      const existingSupplier = matrixValues.find(
        (supplierRfq) => supplierRfq.uid === supplier.uid
      );
      existingSupplier.itemCount = supplier.itemCount;
      existingSupplier.selectedRfqItemCount = supplier.selectedRfqItemCount;
      existingSupplier.selectedRfqJobCount = supplier.selectedRfqJobCount;
      existingSupplier.selectedAttachmentsCount =
        supplier.selectedAttachmentsCount;
      existingSupplier.state = supplier.state;
      this.supplierGridData = { ...this.supplierGridData, ...existingSupplier };
      this.setRfqActions(existingSupplier);
      return existingSupplier;
    });

    this.gridService.storeData$.next({
      gridName: 'supplierGrid',
      data: updatedSuppliers,
    });

    this.cdr.markForCheck();
  }

  public emitSelectedRfq(matrixValues: RequisitionSupplierDto[]): void {
    const uids = matrixValues?.map((r) => r.uid).filter(Boolean) ?? [];
    this.rfqUids = [...new Set(uids)];
    this.disableSendRFQBtn = this.rfqUids.length === 0;
  }

  onCellChange(cellData: JbCellChangeEvent<RequisitionSupplierDto>): void {
    const { rowData, cellvalue } = cellData;
    const supplierFieldName = cellData.cellName.FieldName;
    const selectedRfqUid = rowData.uid;
    this.rowChangesService.setRowChanges(selectedRfqUid, rowData);

    switch (supplierFieldName) {
      case eFieldNames.DeliveryPort:
        this.isDeliveryPortVisible = true;
        this.clickedRowIndex = cellData.rowIndex;
        break;
    }
    this.isModified = cellvalue !== null && cellvalue !== '';
    this.getSupplierData.emit(
      Object.values(this.rowChangesService.getChanges())
    );
  }

  deliveryPortDetails = (port: IdLabel) => {
    return `${port.name} | ${port.country}`;
  };

  public deliveryPortSelected(selectedDeliveryPort: IdLabel): void {
    this.isDeliveryPortVisible = false;
    const deliveryPort = this.deliveryPortDetails(selectedDeliveryPort);

    const matrixValues = this.getNewMatrixData();
    const row = matrixValues[this.clickedRowIndex];
    row.deliveryPortName = deliveryPort;
    row.deliveryPort = selectedDeliveryPort;

    this.rowChangesService.setRowChanges(row.uid, row);

    this.gridService.storeData$.next({
      gridName,
      data: matrixValues,
    });

    this.getSupplierData.emit(
      Object.values(this.rowChangesService.getChanges())
    );
  }

  async sendRfq(
    uids: string[],
    actionType: RfqActionType = 'sendRfq'
  ): Promise<void> {
    this.disableSendRFQBtn = true;
    if (this.isModified) {
      const confirmedModalResult = await this.modalDialogService.openDialog({
        jbDialog: {
          dialogHeader: ePrcConfirmLabel.Save,
        },
        text: ePrcWarnMessages.SaveListConfirmation,
        confirmButtonLabel: ePrcConfirmLabel.Save,
        rejectButtonLabel: ePrcConfirmLabel.Cancel,
      });
      if (!confirmedModalResult) {
        return;
      }
      const matrixValues = this.getNewMatrixData();
      this.saveRequisitionSupplierData.emit(matrixValues);
      this.isModified = false;
    }
    this.rowChangesService.resetChanges();
    this.rfqSent.emit({ uids, actionType });
    this.rfqUids = [];
  }
  /**
   * @description This method will use for grid action like edit, delete and remove item
   */
  onRfqActionSelected(actionEvent): void {
    this.gridAction = actionEvent.payload;
    this.uid = actionEvent?.payload?.uid;
    this.rfqSupplierActions[actionEvent.type].actionFunction(
      actionEvent.payload
    );
  }

  async navigateToSupplierPortal(payload): Promise<void> {
    try {
      const { clientUid } = await this.prcSharedService
        .getClientUid()
        .toPromise();
      const newClientUid = clientUid.toLocaleUpperCase();

      const userInfo = this.cdService.userDetails;
      const body = {
        payload: {
          UserID: userInfo.UserID,
          clientUid: newClientUid,
          module_code: PrcModuleCodes.Procurement,
          function_code: PrcFunctionCodes.RequisitionAdministration,
        },
      };

      const { authtoken } = await this.prcSharedService
        .generateJ3AuthToken(body)
        .toPromise();

      const searchParams = new URLSearchParams({
        token: localStorage.globalToken,
        j3Token: authtoken,
        id: payload.uid,
        spFlag: '1',
        supplierUid: payload.supplierUid,
        action: 'edit',
        client_uid: newClientUid,
      });
      const url = `${window['environment'].jcdsUrl}?`;
      this.extNavigationService.openBlank(url + searchParams.toString());
    } catch (err) {
      throw new Error(
        'Error in navigateToSupplierPortal method: ' + err.message
      );
    }
  }

  /**
   * @description This method will create duplicate for selected record
   */
  async duplicateSupplier(payload): Promise<void> {
    const requisitionRecord: RfqSupplierDto[] = [];
    const supplierObj: RfqSupplierDto = {
      uid: payload?.supplierUid,
      supplierUid: payload?.supplierUid,
      supplier: {
        uid: payload.supplierUid,
        name: payload.supplierName,
      },
      isSelected: true,
      activeStatus: true,
    };
    requisitionRecord.push(supplierObj);
    const dto: CreateRfqsRequestDto = {
      description: payload.rfqRemark,
      quotationDueDate: new Date(payload.quotationDueDate),
      suppliers: requisitionRecord,
    };

    const response = await this.requisitionService.createRfqs(
      dto,
      this.requisition.uid
    );
    if (response) {
      this.closeDeletePopup();
      this.notificationService.success(ePrcSuccessMessages.RecordAddedSuccess);
      this.gridService.refreshGrid(eGridEvents.Table, 'supplierGrid');
    }
  }
  /**
   * @description This method will export rfq as pdf and prompt to download
   */
  async exportRfqAsPdf(payload: RfqSupplierDto): Promise<void> {
    const rfqUid = payload?.uid;
    const file = await this.rfqService.getRfqPdf(rfqUid).toPromise();
    saveAs(file, file.name);
  }
  /**
   * @description This method will delete selected record
   */
  async removeItemRecord(): Promise<void> {
    const sendObj: RfqSupplierRequestDto = {
      activeStatus: false,
    };
    const response = await this.supplierService
      .removeSupplierDetails(sendObj, this.requisition.uid, this.uid)
      .toPromise();
    if (response) {
      this.closeDeletePopup();
      this.notificationService.success(ePrcSuccessMessages.RecordDeleteSuccess);
      this.gridService.refreshGrid(eGridEvents.Table, 'supplierGrid');
    }
  }
  /**
   * @description This method will close delete popup dialog
   */
  closeDeletePopup(): void {
    this.isDialogConfirmVisible = false;
  }

  /**
   * @description This method will close comment popup dialog
   */
  closeCommentPopup(): void {
    this.isDialogCommentVisible = false;
  }

  public getSupplierUrl(rowData: RequisitionSupplierDto): string {
    return (
      ePageRoutes.ContactCardRoute +
      btoa(JSON.stringify({ Supplier_code: rowData.clientSupplierUid }))
    );
  }

  canShowDeclineReasonOverlay(rowData: RequisitionSupplierDto): boolean {
    return (
      rowData.rfqStatusUid === this.rfqDeclinedUid &&
      rowData.declineReasons.length > 0
    );
  }

  private setColumnsEditable(): void {
    if (!this.gridInputs) {
      return;
    }
    this.gridInputs.columns = this.gridInputs?.columns.map((column) =>
      column.hasOwnProperty(eGridCellType.Editable)
        ? {
            ...column,
            Editable: this.readonlyMode,
          }
        : column
    );
    this.gridInputs = { ...this.gridInputs };
  }

  public openCCEmails(rowData: RequisitionSupplierDto): void {
    this.selectedRfqUid = rowData.uid;
    this.ccEmails = rowData.ccEmails ?? [];
    this.cdr.markForCheck();
  }

  public closeCCEmailsPopup(refreshGrid: boolean): void {
    this.selectedRfqUid = null;
    if (refreshGrid) {
      this.gridService.refreshGrid(eGridEvents.Table, 'supplierGrid');
    }
  }
}
