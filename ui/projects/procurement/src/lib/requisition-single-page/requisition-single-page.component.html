<jb-details-layout-figma [formGroup]="requisitionForm">
  <prc-generic-header
    alayout-header
    formControlName="titleNumber"
    [buttons]="headerButtons$ | async"
    [editMode]="sectionEditMode.headerDetails"
    [sections]="headerSections"
    [settingsOptions]="settingsOptions$ | async"
    [status]="headerStatus$ | async"
    (click)="setSectionEditMode('headerDetails')"
  >
    <prc-single-page-sub-header
      *ngIf="requisition"
      [isEditMode]="sectionEditMode.headerDetails"
      [dropdownDetails]="requisition"
      [readonly]="readonly"
      [costValue]="selecetedItemsCost"
      [parentCatalogUids]="parentCatalogUids$ | async"
      [permission]="permission"
      [glAccountUids]="glAccountUids$ | async"
      [approvalMatrixDetails]="approvalMatrixDetails$ | async"
      [reviewMode]="reviewMode"
    ></prc-single-page-sub-header>
  </prc-generic-header>

  <div class="section main" alayout-content>
    <ng-container *ngIf="currentView === 'requisition-details'">
      <jb-layout-widget
        id="general-information"
        titleText="General Information"
        titleIconClass="icons8-resume-template"
        (click)="setSectionEditMode('generalInformation')"
      >
        <prc-requisition-information
          awidget-content
          class="general-information"
          [isEditMode]="sectionEditMode.generalInformation"
          [readonly]="readonly"
          [reviewMode]="reviewMode"
          [segmentUids]="segmentUids"
          [vesselUid]="requisition?.vesselUid"
          [bulkPurchase]="requisition?.bulkPurchase"
        ></prc-requisition-information>
      </jb-layout-widget>

      <jb-layout-widget
        id="items"
        titleText="Items"
        titleIconClass="icons8-shopping-cart-2"
        (click)="setSectionEditMode('items')"
      >
        <jb-button
          awidget-header-buttons
          type="NoButton"
          (click)="navigateToGeneralBrowsing(['Stores', 'Spares'])"
          label="Add Item"
          [disabled]="
            readonly || reviewMode || !(permission.AddEditItem | hasPermissions)
          "
        ></jb-button>

        <prc-requisition-items
          awidget-content
          class="requisition-items"
          formControlName="items"
          [isEditMode]="
            sectionEditMode.items && (permission.AddEditItem | hasPermissions)
          "
          [requisitionUid]="requisitionUid"
          [status]="requisition?.statusId"
          [vesselUid]="requisition?.vesselUid"
          (ecStatusChanged)="updateEcIconHeader($event)"
          [readonly]="readonly || reviewMode"
        ></prc-requisition-items>
      </jb-layout-widget>

      <jb-layout-widget
        *ngIf="!requisition?.bulkPurchase"
        id="jobs"
        titleText="Services"
        titleIconClass="icons8-user-2"
        (click)="setSectionEditMode('jobs')"
      >
        <jb-button
          awidget-header-buttons
          type="NoButton"
          (click)="navigateToGeneralBrowsing('Services')"
          label="Add Service"
          [disabled]="
            readonly ||
            reviewMode ||
            !(permission.AddEditServices | hasPermissions)
          "
        ></jb-button>
        <prc-job-section
          awidget-content
          class="requisition-services"
          formControlName="jobs"
          [jobPermissions]="jobPermissions"
          [getJobsRequest]="getJobsRequest"
        ></prc-job-section>
      </jb-layout-widget>

      <jb-layout-widget
        id="machinery"
        titleText="Machinery"
        titleIconClass="sm-machinery"
      >
        <prc-machinery
          awidget-content
          [refreshSubject$]="refreshMachinery$"
          [objectUid]="requisitionUid"
          class="requisition-machinery"
        >
        </prc-machinery>
      </jb-layout-widget>

      <jb-layout-widget
        titleText="Purchase Orders"
        titleIconClass="icons8-buy-with-card"
        id="purchase-order"
      >
        <lib-requisition-purchase-order
          awidget-content
          [requisitionUid]="requisitionUid"
        ></lib-requisition-purchase-order>
      </jb-layout-widget>

      <jb-layout-widget
        *ngIf="attachmentConfig"
        id="attachments"
        titleText="Attachments"
        titleIconClass="icons8-resume-template"
      >
        <jb-button
          awidget-header-buttons
          [disabled]="readonly || !(permission.AddAttachments | hasPermissions)"
          type="NoButton"
          (click)="attachments.dialogOnDemand()"
          label="Attach file"
        ></jb-button>

        <jb-attachments
          awidget-content
          class="requisition-attachments"
          #attachments
          [attachConfig]="attachmentConfig"
          [actionRow]="attachmentConfig.actions"
          [syncTo]="requisition?.vesselId?.toString()"
        ></jb-attachments>
      </jb-layout-widget>
    </ng-container>

    <lib-requisition-supplier
      *ngIf="currentView === 'suppliers'"
      id="suppliers"
      (getSupplierData)="getSupplierData($event)"
      (saveRequisitionSupplierData)="saveRequisitionSupplierData()"
      (requisitionTimestamp)="updateTimestamp($event)"
      (rfqSent)="this.onSendRfq($event)"
      [readonly]="readonly"
      [requisition]="requisition"
      [isSendingRfq]="isSendingRfq"
    ></lib-requisition-supplier>

    <prc-quotation
      *ngIf="currentView === 'quotation-evaluation'"
      id="quotation-evaluation"
      [requisitionUid]="requisitionUid"
      [requisitionPortId]="requisition?.deliveryPort?.id"
      [bulkPurchase]="requisition?.bulkPurchase"
      [readonly]="readonly || reviewMode"
      (costChange)="onSelectedItemsCostChanged($event)"
      (hasChanges)="quotationEvaluationHasChanged($event)"
      [vesselUid]="requisition?.vesselUid"
      [status]="requisition?.statusId"
    ></prc-quotation>

    <prc-requisition-finance
      *ngIf="currentView === 'finance'"
      id="finance"
      prcType="requisition"
      [additionalChargeAccountsMap]="additionalChargeAccountsMap$ | async"
      [additionalChargesRequest]="additionalChargesRequest"
      [bulkPurchase]="requisition?.bulkPurchase"
      [catalogGLAccounts]="catalogGLAccounts$ | async"
      [deliveryDate]="requisition?.deliveryDate"
      [financeItemsRequest]="financeItemsRequest"
      [objectUid]="requisitionUid"
      [readonly]="readonly || reviewMode"
      [vesselUid]="requisition?.vesselUid"
      (sendAdditionalChargesData)="sendAdditionalChargesData($event)"
      (sendItemConfigurationData)="sendItemConfigurationData($event)"
    ></prc-requisition-finance>

    <prc-delivery-instruction
      *ngIf="currentView == 'delivery-instruction'"
      slfPermission="edit"
      [objectUid]="requisitionUid"
      [readonly]="
        requisition.statusId === 'APPROVE' || requisition.statusId === 'CLOSE'
      "
      [vesselUid]="requisition?.vesselUid"
      (deliveryInstructionData)="deliveryInstructionHasChanged($event)"
    >
    </prc-delivery-instruction>

    <jb-layout-widget *ngIf="currentView === 'audit-trail'">
      <jb-event-history
        awidget-content
        [Key1]="requisitionUid"
        [ModuleCode]="moduleCode"
        [FunctionCode]="functionCode"
        [statusGraphKeys]="statusGraphKeys"
      ></jb-event-history>
    </jb-layout-widget>

    <jb-layout-widget
      *ngIf="currentView === 'linked-records'"
      id="linked-records"
      titleText="Linked Records"
      titleIconClass="icons8-clipboard"
    >
      <jb-tm-linked-records
        *ngIf="linkedRecordsDetails"
        awidget-content
        [hideHeader]="true"
        [taskManagerDetails]="linkedRecordsDetails"
        taskType="supplier_invoice"
      ></jb-tm-linked-records>
    </jb-layout-widget>
  </div>

  <jb-discussion-feed
    *ngIf="currentView !== 'quotation-evaluation' && feedDetail"
    alayout-aside
    class="feed-discussion"
    [detail]="feedDetail"
    [canAdd]="true"
    [function_code]="functionCode"
    [module_code]="moduleCode"
    [vessel_uid]="requisition?.vesselUid"
  ></jb-discussion-feed>
</jb-details-layout-figma>

<prc-duplicate-requisition-popup
  *ngIf="openDuplicatePopup && requisitionUid"
  [requisition]="requisition"
  [(popupOpen)]="openDuplicatePopup"
></prc-duplicate-requisition-popup>

<prc-select-item-list-popup
  *ngIf="openSelectItemListPopup"
  [vesselUid]="requisition?.vesselUid"
  (close)="onCloseSelectItemListPopup($event)"
></prc-select-item-list-popup>

<prc-create-item-list-popup
  *ngIf="createItemListPopupData"
  dialogHeader="Create New List"
  [confirmationDialogParams]="confirmationDialogParams"
  [data]="createItemListPopupData"
  (close)="onCloseCreateItemListPopup($event)"
></prc-create-item-list-popup>

<prc-create-requisition-popup
  *ngIf="createRequisitionPopupData"
  dialogHeader="Create Requisition from Pending Items"
  [data]="createRequisitionPopupData"
  (close)="onCloseCreateRequisitionPopup($event)"
></prc-create-requisition-popup>

<prc-requisition-created-popup
  *ngIf="requisitionCreatedPopupData"
  [data]="requisitionCreatedPopupData"
  (close)="onCloseRequisitionCreatedPopup()"
></prc-requisition-created-popup>

<prc-approval-flow-popup></prc-approval-flow-popup>
