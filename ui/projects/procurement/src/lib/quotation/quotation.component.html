<div class="single-page">
  <jb-layout-widget
    titleText="Quotation Summary"
    titleIconClass="icons8-resume-template"
  >
    <prc-quotation-summary
      awidget-content
      [requisitionUid]="requisitionUid"
      [requisitionPortId]="requisitionPortId"
      [selectedItemsSummary]="quotationsSelectedItemsSummary"
      [initialQuotationUids]="initialQuotationUids"
      (selectedQuotationsChange)="selectQuotations($event)"
    ></prc-quotation-summary>
  </jb-layout-widget>

  <jb-layout-widget
    id="quotation-evaluation-section"
    titleText="Quotation Evaluation"
    titleIconClass="icons8-resume-template"
  >
    <jb-button
      awidget-header-buttons
      type="NoButton"
      label="Save Evaluation"
      (click)="onSaveEvaluation()"
      [disabled]="
        saveEvaluationDisable ||
        readonly ||
        !(permission.PerformQuotationEvaluation | hasPermissions)
      "
    ></jb-button>
    <prc-quotation-evaluation-actions awidget-header-buttons>
    </prc-quotation-evaluation-actions>
    <prc-quotation-evaluation
      awidget-content
      *ngIf="selectedQuotations"
      [bulkPurchase]="bulkPurchase"
      [readonly]="readonly"
      [quotationItemStateMap]="quotationItemStateMap"
      [requisitionUid]="requisitionUid"
      [selectedQuotations]="selectedQuotations"
      [vesselUid]="vesselUid"
      [status]="status"
      (quotationEvaluationSummaryChange)="
        onQuotationEvaluationSummaryChange($event)
      "
      (quotationItemStateMapChange)="onQuotationItemStateMapChange($event)"
    ></prc-quotation-evaluation>
  </jb-layout-widget>
</div>
