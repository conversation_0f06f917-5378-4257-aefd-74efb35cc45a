import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate } from '@angular/router';
import { AuthService } from 'jibe-components';
import { Observable, throwError } from 'rxjs';
import { catchError, mapTo } from 'rxjs/operators';
import { getUrl } from '../../utils/get-url';
import { SlfGuardConfig } from './types';

/**
 * Route guard that delegates self-access permission check to a service defined per route.
 *
 * The guard expects route `data.slf` to contain a configuration object:
 * - `paramKey` (optional): The route param name containing the UID to check. Defaults to `'uid'`.
 * - `requestEntity`: An url segment (e.g., 'po') when constructing the access check endpoint.
 */
@Injectable({ providedIn: 'root' })
export class SlfGuard implements CanActivate {
  name = this.constructor.name;
  constructor(private httpClient: HttpClient) {}
  canActivate(route: ActivatedRouteSnapshot): boolean | Observable<boolean> {
    const { paramKey, requestEntity }: SlfGuardConfig = route.data?.slf ?? {};
    const uid = route.paramMap.get(paramKey);
    if (!uid || !requestEntity) {
      console.warn(`${this.name}: Missing requestEntity or uid`);
      return false;
    }
    const headers = { Authorization: AuthService.getTokenId() };
    const url = getUrl({ entity: requestEntity, action: uid });
    return this.httpClient.head<boolean>(url, { headers }).pipe(
      catchError((e) => {
        console.warn(`${this.name}: Access denied to vessel`);
        return throwError(() => e);
      }),
      mapTo(true)
    );
  }
}
