<div class="machinery-grid">
  <jb-grid
    *ngIf="tableData$ | async as tableData"
    selectionMode="single"
    [advancedSettings]="customAdvancedSettings"
    [colData]="gridInputs.columns"
    [filterData]="gridInputs.filters"
    [filterListsSet]="gridInputs.filtersLists"
    [getStyleByContainer]="true"
    [gridName]="gridInputs.gridName"
    [paginator]="false"
    [showSettings]="advanceSettings"
    [showTreeGrid]="true"
    [tableData]="tableData"
  ></jb-grid>
</div>
<ng-template #machineryIconTemplate let-rowData>
  <i *ngIf="rowData.machinery" [class]="rowData.icon"></i>
  {{ rowData?.machinery }}
</ng-template>
