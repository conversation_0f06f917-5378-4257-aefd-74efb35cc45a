import { Injectable, Optional } from '@angular/core';
import { NgControl } from '@angular/forms';

import { SearchResponse } from '@j3-prc-shared/dtos/odata';
import { GridShareDataService } from 'jibe-components';

import { GridInputs } from '../../../models/interfaces/grid-inputs';
import { RowChangesService } from './row-changes.service';
import { RowChanges, RowData } from './types';

@Injectable()
export class BaseSectionService {
  /**
   * @deprecated
   * use GridControlValueAccessor instead
   */
  public ngControl: NgControl;
  /**
   * @deprecated
   * use GridControlValueAccessor.onChange() instead
   */
  public onChange: (rowData: RowData[]) => void;

  constructor(
    private readonly gridShareDataService: GridShareDataService,
    @Optional() private readonly rowChangesService: RowChangesService
  ) {}

  public filterRemovedRows<T extends { uid: string }>(
    removedRows: Set<string>
  ): SearchResponse<T> {
    const matrixData = this.getMatrixData<T>();
    const { count, records } = matrixData;

    return {
      records: records.filter(({ uid }) => !removedRows.has(uid)),
      count: count - 1,
    };
  }

  public getMatrixData<T = RowData>(): SearchResponse<T> {
    return this.gridShareDataService.matrixApi as unknown as SearchResponse<T>;
  }

  public async setFilters({
    filtersLists,
    filters,
  }: GridInputs<string>): Promise<void> {
    this.gridShareDataService.setFilters(null, true, filtersLists, filters);
  }

  public mergeRowChanges(uid: string, rowChanges: RowChanges): RowChanges {
    if (!this.rowChangesService) {
      throw new Error(`${RowChangesService.name} is not provided`);
    }

    const oldRowChanges = this.rowChangesService.getRowChanges(uid);
    return {
      ...(rowChanges.activeStatus === false ? {} : oldRowChanges),
      ...rowChanges,
    };
  }

  public updateRowData(uid: string, rowChanges: RowChanges): void | never {
    const mergedRowChanges = this.mergeRowChanges(uid, rowChanges);
    this.rowChangesService.setRowChanges(uid, mergedRowChanges);
    this.onChange(Object.values(this.rowChangesService.getChanges()));
  }
}
