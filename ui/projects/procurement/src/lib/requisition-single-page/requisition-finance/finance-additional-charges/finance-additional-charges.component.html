<div class="aditional-charges-grid">
  <prc-finance-table
    [bulkPurchase]="bulkPurchase"
    [deliveryDate]="deliveryDate"
    [financeGridInputs]="gridInputs"
    [isDisplaySearchField]="false"
    [selectedAccountsMap]="selectedAccountsMap"
    [vesselUid]="vesselUid"
    (getEditableData)="getEditableData($event)"
    (action)="onAction($event)"
  ></prc-finance-table>
</div>

<prc-split-popup
  *ngIf="splittedAdditonalChargeUid"
  [selectedUid]="splittedAdditonalChargeUid"
  (confirm)="onSplit($event)"
  (close)="closeSplitDialog()"
></prc-split-popup>
