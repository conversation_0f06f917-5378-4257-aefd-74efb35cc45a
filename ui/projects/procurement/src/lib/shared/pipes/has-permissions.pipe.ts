import {
  ChangeDetector<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  PipeTransform,
} from '@angular/core';
import { PermissionService } from '../../services/permission/permission.service';

@Pipe({ name: 'hasPermissions', pure: false })
export class HasPermissionsPipe implements OnDestroy, PipeTransform {
  private latestValue = false;

  private promise: Promise<boolean>;
  private rightCode: string | string[];

  constructor(
    private readonly ref: ChangeDetectorRef,
    private readonly permissionService: PermissionService
  ) {}

  ngOnDestroy(): void {
    if (this.promise) {
      this.dispose();
    }
  }

  transform(rightCode: string | string[]): boolean {
    if (!this.rightCode) {
      if (rightCode) {
        this.waitForPermissions(rightCode);
      }
      return this.latestValue;
    }

    if (rightCode !== this.rightCode) {
      this.dispose();
      return this.transform(rightCode);
    }

    return this.latestValue;
  }

  private async waitForPermissions(
    rightCode: string | string[]
  ): Promise<void> {
    this.rightCode = rightCode;
    this.promise = this.permissionService.hasPermissions(rightCode);
    this.updateLatestValue(rightCode, await this.promise);
  }

  private dispose(): void {
    this.latestValue = undefined;
    this.rightCode = undefined;
    this.promise = undefined;
  }

  private updateLatestValue(
    rightCode: string | string[],
    value: boolean
  ): void {
    if (rightCode === this.rightCode) {
      this.latestValue = value;
      this.ref.markForCheck();
    }
  }
}
