<div class="item-grid">
  <jb-grid
    [advancedSettings]="customAdvancedSettings"
    [colData]="gridInputs.columns"
    [filterData]="gridInputs.filters"
    [filterListsSet]="gridInputs.filtersLists"
    [getStyleByContainer]="true"
    [gridName]="gridInputs.gridName"
    [searchFields]="gridInputs.searchFields"
    [setActions]="!readonly ? gridInputs.actions : null"
    [showSettings]="advanceSettings"
    [tableDataReq]="gridInputs.request"
    [toolTipFields]="poItemToolTipFields"
    (action)="onGridAction($event)"
  ></jb-grid>
</div>

<ng-template #iconTemplate let-rowData>
  <div class="po-item-icon">
    <i
      class="icons8-private-3 export-control-icon"
      *ngIf="rowData.ecStatus !== 'notEC'"
      pTooltip="EC"
      [ngClass]="rowData.ecStatus"
    ></i>
    <img
      *ngIf="rowData.criticality"
      alt="critical-icon"
      class="icons8-copyright critical-icon"
      pTooltip="Critical"
      src="assets/images/criticality.svg"
    />
    <img
      *ngIf="rowData.dg"
      alt="dg-icon"
      class="icons8-falling-objects-hazard warning-icon"
      pTooltip="Dangerous Goods (DG)"
      src="assets/images/DG-icon.svg"
    />
    <img
      *ngIf="rowData.ihm"
      alt="ihm-icon"
      class="icons8-high-priority-3 info-icon"
      pTooltip="Inventory of Hazardous Material (IHM)"
      src="assets/images/ihm.svg"
    />
  </div>
</ng-template>

<ng-template #robTemplate let-rowData>
  <span *ngIf="showDbRob(rowData); else showUpdatedRob">
    {{ rowData.rob }}
  </span>
  <ng-template #showUpdatedRob>
    <span *ngIf="(robs$ | async)?.length && rowData.itemUid as itemUid">{{  itemUid | rob | async }}</span>
  </ng-template>
</ng-template>

<ng-template #pendingQuantityTemplate let-rowData>
    <span>{{rowData.pendingQuantity}}</span>
</ng-template>

<ng-template #undeliveredQuantityTemplate let-rowData>
  <span>{{rowData.undeliveredQuantity}}</span>
</ng-template>

<ng-template #itemNameTemplate let-rowData>
  <div class="item-name">
    <span
      class="text-ellipsis name"
      [prcTooltip]="rowData.itemName"
      appendTo="body"
    >
    <a class="jb_grid_topCellValue jb-link-600-14"
      target="_blank"
      [prcNavigationLink]="[itemSinglePageRoute, rowData.itemUid]"
      > {{rowData.itemName}}</a>
    </span>
</div>
</ng-template>  