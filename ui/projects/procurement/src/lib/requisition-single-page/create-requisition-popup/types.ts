import { ItemListBaseDto } from '@j3-procurement/dtos/item-list';
import { CreateRequisitionDto } from '@j3-procurement/dtos/requisition';

export type CreateRequisitionForm = Pick<
  CreateRequisitionDto,
  'deliveryDate' | 'description' | 'itemListUids' | 'poType' | 'urgency'
> & { deliveryPort: number; deliveryUpcomingPort?: number };

export interface CreateRequisitionPopupData {
  deliveryDate?: Date;
  deliveryPortId?: number;
  description?: string;
  itemListUid?: string;
  urgencyUid?: string;
  poTypeUid?: string;
  vesselUid: string;
}

/** Added to avoid chokidar issue */
export type ItemListBaseDtoType = ItemListBaseDto;
