{"rulesDirectory": ["codelyzer", "tslint-plugin-prettier"], "extends": ["rxjs-tslint-rules", "tslint-config-prettier", "tslint-plugin-prettier"], "rules": {"arrow-return-shorthand": true, "callable-types": true, "class-name": true, "component-class-suffix": true, "comment-format": [true, "check-space"], "curly": true, "deprecation": {"severity": "warning"}, "directive-class-suffix": true, "eofline": true, "forin": true, "import-blacklist": [true, "rxjs/Rx"], "import-spacing": true, "indent": [true, "spaces"], "interface-over-type-literal": true, "label-position": true, "max-line-length": [true, 200], "member-access": false, "member-ordering": [true, {"order": ["static-field", "instance-field", "static-method", "instance-method"]}], "no-arg": true, "no-bitwise": true, "no-console": [true, "log", "info", "time", "timeEnd", "trace"], "no-construct": true, "no-debugger": true, "no-duplicate-super": true, "no-empty-interface": true, "no-empty": false, "no-eval": true, "no-host-metadata-property": true, "no-inferrable-types": [true, "ignore-params"], "no-misused-new": true, "no-non-null-assertion": true, "no-redundant-jsdoc": true, "no-input-rename": true, "no-inputs-metadata-property": true, "no-output-on-prefix": true, "no-output-rename": true, "no-outputs-metadata-property": true, "no-shadowed-variable": true, "no-string-literal": false, "no-string-throw": true, "no-switch-case-fall-through": true, "no-trailing-whitespace": true, "no-unnecessary-initializer": true, "no-unused-expression": true, "no-var-keyword": true, "object-literal-sort-keys": false, "one-line": [true, "check-catch", "check-else", "check-whitespace"], "ordered-imports": true, "prefer-const": true, "prettier": true, "quotemark": [true, "single"], "radix": true, "rxjs-finnish": {"severity": "error", "options": [{"functions": false, "methods": false, "parameters": false, "properties": true, "variables": true, "names": {"valueChanges": false}, "types": {"^Subject$": false}}]}, "rxjs-prefer-angular-takeuntil": {"severity": "error", "options": [{"alias": ["take", "<PERSON><PERSON><PERSON><PERSON>"], "checkComplete": true, "checkDecorators": ["Component"], "checkDestroy": false}]}, "typedef": [true, "call-signature", "property-declaration"], "unified-signatures": true, "use-lifecycle-interface": true, "use-pipe-transform-interface": true, "variable-name": false, "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"]}}