<jb-dialog
  [dialogContent]="dialogContent"
  [(dialogVisible)]="selectedUid"
  (closeDialog)="close.emit()"
>
  <div jb-dialog-body>
    <p>To how many vessel should the line be split to?</p>
    <div>
      <form [formGroup]="splitForm">
        <prc-form-label label="Select Vessels" class="form-label">
          <jb-multi-select-dropdown
            appendTo="body"
            [content]="vesselContent"
            [virtualScroll]="true"
          >
          </jb-multi-select-dropdown>
        </prc-form-label>
      </form>
    </div>
  </div>
  <ng-container jb-dialog-footer>
    <jb-dialog-footer
      [cancelBtnLabel]="'Cancel'"
      [okBtnLabel]="'Confirm'"
      [isOkBtnDisabled]="vesselContent.selectedValues.length === 0"
      (cancel)="close.emit()"
      (ok)="onConfirm()"
    ></jb-dialog-footer>
  </ng-container>
</jb-dialog>
