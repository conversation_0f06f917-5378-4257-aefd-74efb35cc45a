import { createColumns, createFilters } from 'j3-prc-components';
import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  FilterListSet,
  GridRowActions,
} from 'jibe-components';

import { JbDropdownOption } from '../../models/interfaces/jb-dropdown-option';
import { eGridRowActions } from './constants';
import { ColumnKey } from './types';

export const gridName = 'itemSection';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  ['Part No.', 'itemNumber', { width: eGridColumnsWidth.LongDescription }],
  ['Item Name', 'itemName', { width: '180px', hyperlink: true }],
  ['Brand', 'brandName', { width: eGridColumnsWidth.ShortDescription }],
  ['Model', 'modelNumber', { width: eGridColumnsWidth.ShortDescription }],
  ['UOM', 'uomName', { width: eGridColumnsWidth.ShortDescription }],
  [
    'QTY',
    'qty',
    {
      ControlType: eFieldControlType.Number,
      Precision: '1.2-2',
      width: eGridColumnsWidth.LongNumber,
    },
  ],
  [
    'Remarks',
    'remarks',
    {
      ControlType: eFieldControlType.Input,
      MaxLength: 500,
      width: eGridColumnsWidth.LongDescription,
    },
  ],
  ['ROB', 'rob', { DisableSort: true, width: eGridColumnsWidth.ShortNumber }],
]);

export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['DG', 'dangerousGoodsText', { width: eGridColumnsWidth.YesNo }],
    ['IHM', 'ihmText', { width: eGridColumnsWidth.YesNo }],
    ['Tags', 'tagsLabels', { width: eGridColumnsWidth.LongDescription }],
    ['Item Type', 'itemTypeUid', { width: eGridColumnsWidth.ShortDescription }],
    ['Type Attributes', 'typeAttributes', { width: '150px' }],
  ],
  { IsVisible: false }
);

type FilterKey = 'brand' | 'model' | 'machinery' | 'catalog';

export const filters = createFilters<string, FilterKey>(
  [
    ['Brand', 'brand'],
    ['Model', 'model'],
  ],
  { gridName }
);

export const additionalFilters = createFilters<string, FilterKey>(
  [
    ['Machinery', 'machinery'],
    ['Catalogue', 'catalog'],
  ],
  { gridName, default: false }
);

export function getFiltersLists(
  brands: JbDropdownOption[],
  models: JbDropdownOption[],
  machineries: JbDropdownOption[],
  catalogs: JbDropdownOption[]
): Record<FilterKey, FilterListSet[string]> {
  return {
    brand: {
      list: brands,
      type: eGridCellType.Multiselect,
      odataKey: 'brandUid',
    },
    model: {
      list: models,
      type: eGridCellType.Multiselect,
      odataKey: 'modelNumber',
    },
    machinery: {
      list: machineries,
      type: eGridCellType.Multiselect,
      odataKey: 'machineryUid',
    },
    catalog: {
      list: catalogs,
      type: eGridCellType.Multiselect,
      odataKey: 'catalogUid',
    },
  };
}

export const searchFields: ColumnKey[] = [
  'catalogName',
  'itemName',
  'itemNumber',
  'remarks',
];

export function getActions(isRemoveItemDisabled = true): GridRowActions[] {
  return [
    {
      name: eGridRowActions.DeleteRemark,
      icon: eIconNames.Delete,
      color: eColor.JbBlack,
    },
    {
      name: eGridRowActions.RemoveItem,
      icon: eIconNames.Cancel,
      color: eColor.JbRed,
      disabled: isRemoveItemDisabled,
    },
  ];
}
