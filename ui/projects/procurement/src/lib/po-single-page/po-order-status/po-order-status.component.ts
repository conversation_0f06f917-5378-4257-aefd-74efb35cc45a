import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import {
  PoAdditionalInfoDto,
  UpdatePoAdditionalInfoDto,
} from '@j3-procurement/dtos/po';
import {
  PoStatusBranchDto,
  PoStatusBranchResponseDto,
} from '@j3-procurement/dtos/po-status-branch';
import {
  localToUTC,
  NotificationService,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { CentralizedDataService, eAppLocation } from 'jibe-components';
import {
  ePrcErrorMessages,
  ePrcSuccessMessages,
} from '../../models/enums/prc-messages.enum';

import { PrcFunctionCodes } from '@j3-procurement/dtos';
import { PoPermission } from '../../models/enums/po-permission.enum';
import { Vessel } from '../../models/interfaces/vessel';
import { InfraService } from '../../services/infra.service';
import { POService } from '../../services/po/po.service';
import { VesselService } from '../../services/vessel/vessel.service';
import { PoSupplierConfirmation } from '../types';
import { SupplierConfirmationDialogComponent } from './supplier-confirmation/supplier-confirmation-dialog.component';

@Component({
  selector: 'prc-po-order-status',
  templateUrl: './po-order-status.component.html',
  styleUrls: ['./po-order-status.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PoOrderStatusComponent extends UnsubscribeComponent {
  @Input() hideLink: boolean;
  @Input() poIssueUid: string;
  @Input() poUid: string;
  @Input() readonly: boolean;
  @Input() statusBranch: PoStatusBranchResponseDto;
  @Input() vesselUid: string;

  @Input() canUpdateOrderStatus: boolean;

  @Input() slfPermission: 'edit' | 'view' | undefined;

  @Output() save = new EventEmitter<boolean>();

  @ViewChild(SupplierConfirmationDialogComponent)
  supplierConfirmationDialogComponent: SupplierConfirmationDialogComponent;

  private additionalInformationData: UpdatePoAdditionalInfoDto;
  private vessel: Vessel;
  private isOffice: boolean;

  public isUpdateStatus = false;
  public supplierConfirmationData: PoSupplierConfirmation;
  public selectedItem: PoStatusBranchDto;
  public permission = PoPermission;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly notificationService: NotificationService,
    private readonly poService: POService,
    private readonly infraService: InfraService,
    private readonly cds: CentralizedDataService,
    private readonly vesselService: VesselService
  ) {
    super();
  }

  public selectItem(item: PoStatusBranchDto): void {
    this.selectedItem = item;
  }

  public updateStatusVisibility(data: boolean): void {
    this.isUpdateStatus = data;
  }

  public getSupplierConfirmationData(data: PoSupplierConfirmation): void {
    this.supplierConfirmationData = data;
  }

  public getAdditionalDeliveryMethodData(data: PoAdditionalInfoDto): void {
    this.additionalInformationData = data;
  }

  public async saveStatus(): Promise<void> {
    if (this.selectedItem.status === 'Supplier Confirmation') {
      await this.savePoConfirmation();
    } else {
      await this.saveAdditionalInfo();
    }
    this.save.emit(true);
    this.cdr.markForCheck();
  }

  public async savePoConfirmation(): Promise<void> {
    const AppLocation = this.cds.userDetails ?? {};
    this.isOffice = AppLocation === eAppLocation.Office;

    await this.vesselService.getVessels().toPromise();
    this.vessel = this.vesselService.getVesselByUid(this.vesselUid);

    const data = {
      ...this.selectedItem,
      ...this.supplierConfirmationDialogComponent?.saveSupplierConfirmation(),
    };

    if (data.attachments?.length > 0) {
      const fileUploadPromisses = data.attachments.map((attachment) =>
        this.infraService.uploadFile(
          this.poUid,
          this.isOffice,
          this.vessel.vesselId,
          attachment,
          PrcFunctionCodes.PoAttachments,
          PrcFunctionCodes.SupplierConfirmationAttachments
        )
      );
      await Promise.all(fileUploadPromisses);
    }
    const response = await this.poService
      .saveSupplierConfirmation(this.poUid, {
        confirmDate: localToUTC(data.confirmDate),
        orderConfirmationNumber: data.orderConfirmationNumber,
        supplierStatus: data.supplierStatus,
        remark: data.remark,
        readyDate: localToUTC(data.readyDate),
      })
      .toPromise();
    if (response) {
      this.notificationService.success(ePrcSuccessMessages.RecordSavedSuccess);
    } else {
      this.notificationService.error(ePrcErrorMessages.RecordNotSavedSuccess);
    }
    this.statusBranch.list = this.statusBranch?.list?.map((item) =>
      item.uid === data.uid
        ? { ...data, date: data.confirmDate ?? data.date }
        : item
    );
    this.cdr.markForCheck();
  }

  public async saveAdditionalInfo(): Promise<void> {
    const additionalInfo = {
      ...this.selectedItem,
      ...this.additionalInformationData,
    };
    const result = await this.poService
      .saveAdditionalInfo(this.poUid, {
        ...this.additionalInformationData,
        confirmDate: localToUTC(this.additionalInformationData.confirmDate),
        branchName: additionalInfo.status,
      })
      .toPromise();
    if (result) {
      this.notificationService.success(ePrcSuccessMessages.RecordSavedSuccess);
    } else {
      this.notificationService.error(ePrcErrorMessages.RecordNotSavedSuccess);
    }

    this.statusBranch.list = this.statusBranch.list?.map((item) =>
      item.uid === additionalInfo.uid
        ? {
            ...additionalInfo,
            isComplete: true,
            date: additionalInfo.confirmDate ?? additionalInfo.date,
          }
        : item
    );
    this.cdr.markForCheck();
  }
}
