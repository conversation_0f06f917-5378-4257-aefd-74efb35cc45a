import { createColumns, createFilters } from 'j3-prc-components';
import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  FilterListSet,
  GridRowActions,
} from 'jibe-components';
import { <PERSON>umn<PERSON><PERSON>, Filter<PERSON><PERSON> } from './types';

import { EcStatus } from '@j3-procurement/dtos/export-control';
import { RequisitionItemDto } from '@j3-procurement/dtos/requisition';
import { JbDropdownOption } from '../../models/interfaces/jb-dropdown-option';
import { eGridRowActions } from './grid-constants';

export const gridName = 'itemSection';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  ['Part No.', 'partNumber', { width: eGridColumnsWidth.LongDescription }],
  ['Item Name', 'itemName', { width: eGridColumnsWidth.LongDescription }],
  ['', 'icon', { width: eGridColumnsWidth.JobCode, DisableSort: true }],
  ['Brand', 'brandName', { width: eGridColumnsWidth.ShortDescription }],
  ['Model', 'modelNumber', { width: eGridColumnsWidth.ShortDescription }],
  ['Unit', 'unitMeasurement', { width: eGridColumnsWidth.ShortDescription }],
  [
    'QTY',
    'itemQuantity',
    {
      ControlType: eFieldControlType.Number,
      Precision: '1.2-2',
      width: eGridColumnsWidth.LongNumber,
    },
  ],
  [
    'Remarks',
    'itemRemark',
    {
      ControlType: eFieldControlType.Input,
      MaxLength: 500,
      width: eGridColumnsWidth.LongDescription,
    },
  ],
  ['ROB', 'rob', { DisableSort: true, width: eGridColumnsWidth.ShortNumber }],
]);

export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['Tags', 'tags', { width: eGridColumnsWidth.LongDescription }],
    ['Item Type', 'itemType', { width: eGridColumnsWidth.ShortDescription }],
    ['Type Attributes', 'typeAttributes', { width: '150px' }],
    [
      'Item Requisition Status',
      'itemRequisitionStatus',
      { width: eGridColumnsWidth.ShortDescription },
    ],
  ],
  { IsVisible: false }
);

export const filters = createFilters<string, FilterKey>(
  [
    ['Brand', 'brand'],
    ['Model', 'model'],
    ['Item Requisition Status', 'itemRequisitionStatus'],
  ],
  { gridName }
);

export const additionalFilters = createFilters<string, FilterKey>(
  [
    ['Machinery', 'machinery'],
    ['Catalogue', 'catalog'],
  ],
  { gridName, default: false }
);

export function getFiltersLists(
  brands: JbDropdownOption[],
  catalogs: JbDropdownOption[],
  models: JbDropdownOption[],
  machineries: JbDropdownOption[],
  statuses: JbDropdownOption[]
): Record<FilterKey, FilterListSet[string]> {
  return {
    brand: {
      list: brands,
      type: eGridCellType.Multiselect,
      odataKey: 'brandUid',
    },
    catalog: {
      list: catalogs,
      type: eGridCellType.Multiselect,
      odataKey: 'catalogUid',
    },
    itemRequisitionStatus: {
      list: statuses,
      type: eGridCellType.Multiselect,
      odataKey: 'itemRequisitionStatus',
    },
    machinery: {
      list: machineries,
      type: eGridCellType.Multiselect,
      odataKey: 'machineryUid',
    },
    model: {
      list: models,
      type: eGridCellType.Multiselect,
      odataKey: 'modelNumber',
    },
  };
}

export const searchFields: ColumnKey[] = [
  'itemName',
  'itemRemark',
  'catalogName',
];

const getMarkEcGridAction = (isBlockEcDisabled: boolean): GridRowActions => ({
  name: eGridRowActions.MarkEc,
  color: eColor.JbBlack,
  disabled: isBlockEcDisabled,
});

const getReleaseEcGridAction = (
  isReleaseEcDisabled: boolean
): GridRowActions => ({
  name: eGridRowActions.ReleaseEc,
  color: eColor.JbBlack,
  disabled: isReleaseEcDisabled,
});

const getBlockEcGridAction = (isBlockEcDisabled: boolean): GridRowActions => ({
  name: eGridRowActions.BlockEc,
  color: eColor.JbBlack,
  disabled: isBlockEcDisabled,
});

interface ActionAccess {
  itemSection: boolean;
  bolckEC: boolean;
  releaseEc: boolean;
}

const buildEcActionMap = (access: ActionAccess) =>
  new Map<EcStatus, any>([
    ['blocked', getReleaseEcGridAction(access.releaseEc)],
    ['released', getBlockEcGridAction(access.bolckEC)],
    ['notEC', getMarkEcGridAction(access.bolckEC)],
  ]);

export const getActions = (access: ActionAccess): GridRowActions[] => {
  const ecActionMap = buildEcActionMap(access);

  const buildActions = ({ ecStatus }: RequisitionItemDto) => {
    const baseActions = getBaseGridActions(access.itemSection);
    const ecAction = ecActionMap.get(ecStatus);
    return ecAction ? [...baseActions, ecAction] : baseActions;
  };

  return [
    {
      name: eGridRowActions.MarkEc,
      color: eColor.JbBlack,
      actionFunction: buildActions,
    },
  ];
};

const getBaseGridActions = (
  isItemsSectionDisabled: boolean
): GridRowActions[] => [
  {
    name: eGridRowActions.HideRemark,
    icon: eIconNames.Archive,
    color: eColor.JbBlack,
  },
  {
    name: eGridRowActions.DeleteRemark,
    icon: eIconNames.Delete,
    color: eColor.JbBlack,
    disabled: isItemsSectionDisabled,
  },
  {
    name: eGridRowActions.RemoveItem,
    icon: eIconNames.Cancel,
    color: eColor.JbRed,
    disabled: isItemsSectionDisabled,
  },
];
