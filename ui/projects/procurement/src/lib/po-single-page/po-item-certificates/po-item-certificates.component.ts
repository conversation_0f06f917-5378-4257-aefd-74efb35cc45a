import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { CertificateDto } from '@j3-procurement/dtos/certificate';
import { IDocumentPreview } from 'jibe-components/lib/components/jb-document-preview/model/jb-document-preview.model';

import {
  localToUTC,
  ModalDialogService,
  NotificationService,
  UnsubscribeComponent,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  eAppLocation,
  eDateFormats,
  eGridEvents,
  eGridRowActions,
  FileService,
  GridAction,
  GridService,
  GridShareDataService,
  JbDatePipe,
  MatrixDataChanged,
} from 'jibe-components';
import { Observable } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { DEFAULT_SEPARATOR, UNKNOWN } from '../../models/constants';
import { ItemCertificatePermission } from '../../models/enums/item-certificate-permission.enum';
import {
  ePrcConfirmLabel,
  ePrcModalTitle,
  ePrcSuccessMessages,
  ePrcWarnMessages,
} from '../../models/enums/prc-messages.enum';
import { ePrcWorklistType } from '../../models/enums/prc-worklist-type.enum';
import { GridInputsWithRequest } from '../../models/interfaces/grid-inputs';
import { CertificateService } from '../../services/certificate.service';
import { JCDSService } from '../../services/jcds.service';
import { PermissionService } from '../../services/permission/permission.service';
import { TaskManagerService } from '../../services/task-manager.service';
import { StatusColor } from '../../shared/status/types';
import {
  ColumnKey,
  columns,
  filters,
  getActions,
  getFiltersLists,
  gridName,
  searchFields,
} from './grid-inputs';
import { CertificateData, CertificateEvent } from './types';

@Component({
  selector: 'prc-po-item-certificates',
  templateUrl: './po-item-certificates.component.html',
  styleUrls: ['./po-item-certificates.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PoItemCertificatesComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() event$: Observable<CertificateEvent>;
  @Input() poUid: string;
  @Input() readonly: boolean;

  @Output() detailsIconClick = new EventEmitter<CertificateData>();

  @ViewChild('certificateTemplate', { static: true })
  certificateTemplate: TemplateRef<HTMLElement>;
  @ViewChild('ihmContentTemplate', { static: true })
  ihmContentTemplate: TemplateRef<HTMLElement>;
  @ViewChild('itemTypeTemplate', { static: true })
  itemTypeTemplate: TemplateRef<HTMLElement>;
  @ViewChild('remarksTemplate', { static: true })
  remarksTemplate: TemplateRef<HTMLElement>;
  @ViewChild('statusTemplate', { static: true })
  statusTemplate: TemplateRef<HTMLElement>;

  public documentContent: IDocumentPreview;
  public gridInputs: GridInputsWithRequest<ColumnKey>;
  public isDocumentPreview = false;
  public statusColor: Record<string, StatusColor> = {
    complete: 'green',
    approve: 'purple',
    verify: 'voilet',
    raise: 'blue-dark',
    inprogress: 'blue',
    review: 'purple-dark',
    default: 'grey-blue',
  };
  public UNKNOWN = UNKNOWN;

  public isDialogDeleteVisible = false;

  private isOfficeApp: boolean;
  public deleteCertificateData: CertificateDto;

  constructor(
    private readonly cds: CentralizedDataService,
    private readonly certificateService: CertificateService,
    private readonly fileService: FileService,
    private readonly gridService: GridService,
    private readonly gridShareDataService: GridShareDataService,
    private readonly jbDatePipe: JbDatePipe,
    private readonly jcdsService: JCDSService,
    private readonly taskManagerService: TaskManagerService,
    private readonly permissionService: PermissionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly notificationService: NotificationService,
    private readonly modalDialogService: ModalDialogService
  ) {
    super();
  }

  ngOnInit(): void {
    this.isOfficeApp = this.cds.userDetails.AppLocation === eAppLocation.Office;

    this.processIhmCertificateData();
  }

  private async processIhmCertificateData(): Promise<void> {
    await this.setGridInputs();
    this.setFilterLists();
    this.gridService.matrixDataChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === gridName)
      )
      .subscribe(
        async ({ matrixValues }: MatrixDataChanged<CertificateDto>) => {
          if (!matrixValues?.length) {
            return;
          }

          const [certificateTypesMap, hazardousMaterialsMap] =
            await Promise.all([
              this.certificateService.getIhmCertificateTypesMap(),
              this.jcdsService.getDataMap('hazardousMaterials'),
            ]);

          this.gridService.storeData$.next({
            gridName,
            data: matrixValues.map((rowData) => {
              const ihmContent = rowData.ihmContent?.filter(({ materialUid }) =>
                hazardousMaterialsMap.has(materialUid)
              );

              const ihmContentText = ihmContent
                ?.map((content) => {
                  const { material_name: materialName, UOM_unit: uom } =
                    hazardousMaterialsMap.get(content.materialUid) ?? {};
                  return `${materialName} - ${content.quantity} ${uom}`;
                })
                .join(DEFAULT_SEPARATOR);

              return {
                ...rowData,
                certificateTypeName: certificateTypesMap.get(
                  rowData.certificateTypeId
                )?.name,
                expiryDate: this.jbDatePipe.transform(
                  rowData.expiryDate,
                  undefined,
                  eDateFormats.DefaultFormat.toUpperCase()
                ),
                ihmContent,
                ihmContentText,
                issueDate: this.jbDatePipe.transform(
                  localToUTC(new Date(rowData.issueDate)),
                  undefined,
                  eDateFormats.DefaultFormat.toUpperCase()
                ),
              };
            }),
          });
        }
      );

    if (this.event$) {
      this.event$
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((event) => {
          if (event.refreshGrid) {
            this.gridService.refreshGrid(eGridEvents.Table, gridName);
            this.setFilterLists();
          }
        });
    }
  }

  private async setGridInputs(): Promise<void> {
    const [hasEditItemPermission, hasOtherPermission] = await Promise.all([
      this.permissionService.hasPermissions(
        ItemCertificatePermission.EditCertificate
      ),
      this.permissionService.hasAtLeastOnePermission([
        ItemCertificatePermission.ViewCertificate,
        ItemCertificatePermission.AddCertificate,
      ]),
    ]);
    const canLoadData = hasEditItemPermission || hasOtherPermission;
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      uploadUid: this.certificateTemplate,
      ihmContent: this.ihmContentTemplate,
      itemTypeUid: this.itemTypeTemplate,
      remarks: this.remarksTemplate,
      taskStatus: this.statusTemplate,
    };
    this.gridInputs = {
      columns: columns.map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      filters,
      filtersLists: getFiltersLists([], [], undefined),
      gridName,
      searchFields,
      ...(canLoadData && {
        actions: getActions(!hasEditItemPermission),
        request: this.certificateService.getCertificateListRequest(this.poUid),
      }),
    };
    this.cdr.markForCheck();
  }

  public async previewFile(
    uploadUid: string,
    uploadFileName: string,
    uploadFilePath: string,
    fileType: string
  ): Promise<void> {
    const file = await this.fileService
      .downloadFileByUid(uploadUid)
      .toPromise();
    const objectURL = URL.createObjectURL(file);

    this.documentContent = {
      FileName: uploadFileName,
      FileType: fileType,
      FilePath: uploadFilePath,
      FileUrl: objectURL,
      Width: '100%',
      Height: '900px',
    };

    this.isDocumentPreview = true;
  }

  public showDetails({ itemName, itemUid, ...details }: CertificateDto): void {
    this.detailsIconClick.emit({
      isViewMode: true,
      details,
      items: [{ itemName, itemUid }],
    });
  }

  public onGridAction(
    gridAction: GridAction<eGridRowActions, CertificateDto>
  ): void {
    if (gridAction.type === eGridRowActions.Edit) {
      const { itemName, itemUid, ...details } = gridAction.payload;
      this.detailsIconClick.emit({
        isViewMode: false,
        details,
        items: [{ itemName, itemUid }],
      });
    }
    if (gridAction.type === eGridRowActions.Delete) {
      this.openDialogDelete(gridAction.payload);
    }
  }

  private async setFilterLists(): Promise<void> {
    const [
      { itemTypeUids, certificateTypeIds },
      categoryMap,
      ihmCertificateTypesMap,
      taskStatuses,
    ] = await Promise.all([
      this.certificateService.getCertificateListFilterOptions({
        objectUid: this.poUid,
      }),
      this.jcdsService.getDataMap('ItemsCategory'),
      this.certificateService.getIhmCertificateTypesMap(),
      this.taskManagerService
        .getWorklist(ePrcWorklistType.Certificate)
        .toPromise(),
    ]);

    this.gridInputs.filtersLists = getFiltersLists(
      itemTypeUids.map((uid) => ({
        value: uid,
        label: categoryMap.get(uid)?.category_name ?? UNKNOWN,
      })),
      certificateTypeIds.map((id) => ({
        value: id.toString(),
        label: ihmCertificateTypesMap.get(id)?.name ?? UNKNOWN,
      })),
      taskStatuses
        .filter(({ Is_Office }) => Boolean(Is_Office) === this.isOfficeApp)
        .map(({ WorkflowType_ID }) => ({
          value: WorkflowType_ID,
          label: WorkflowType_ID,
        }))
    );

    this.gridShareDataService.setFilters(
      null,
      true,
      this.gridInputs.filtersLists,
      this.gridInputs.filters
    );
  }

  public async deleteCertificate(): Promise<void> {
    await this.certificateService.deleteCertificate(this.deleteCertificateData);
    this.closeDialogDelete();
    this.notificationService.success(
      ePrcSuccessMessages.CertificateDeleteSuccess
    );
    this.gridService.refreshGrid(eGridEvents.Table, gridName);
    this.setFilterLists();
  }

  public closeDialogDelete(): void {
    this.deleteCertificateData = null;
  }

  private async openDialogDelete(details: CertificateDto): Promise<void> {
    this.deleteCertificateData = details;
    const isConfirmed = await this.modalDialogService.openDialog({
      confirmButtonLabel: ePrcConfirmLabel.Confirm,
      text: ePrcWarnMessages.DeleteIHMItem,
      jbDialog: {
        dialogHeader: ePrcModalTitle.ConfirmationHeader,
        closableIcon: true,
      },
    });

    if (!isConfirmed) {
      this.closeDialogDelete();
      return;
    }
    this.deleteCertificate();
  }
}
