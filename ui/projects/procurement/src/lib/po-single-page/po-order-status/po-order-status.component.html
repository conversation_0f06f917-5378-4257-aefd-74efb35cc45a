<jb-layout-widget
  id="order-status"
  titleText="Order Status"
  titleIconClass="icons8-shopping-cart"
>
  <jb-button
    awidget-header-buttons
    type="NoButton"
    label="Save"
    (click)="saveStatus()"
    [disabled]="readonly || !isUpdateStatus || !canUpdateOrderStatus"
  >
  </jb-button>
  <section class="content" awidget-content>
    <div>
      <prc-order-status
        *ngFor="let item of statusBranch.list"
        [class.next-step]="
          item.branchSequenceNo === statusBranch.lastActiveSeq + 1
        "
        [item]="item"
        [hideLink]="hideLink"
        (updateStatusClick)="selectItem(item)"
        [slfPermission]="slfPermission"
        [poUid]="poUid"
      >
      </prc-order-status>
    </div>
    <div class="status-form">
      <div *ngIf="selectedItem && !readonly" [ngSwitch]="selectedItem.status">
        <div *ngSwitchCase="'Supplier Confirmation'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            PO Confirmation
          </div>
          <supplier-confirmation-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            (updateStatusVisibility)="updateStatusVisibility($event)"
            (getSupplierConfirmationData)="getSupplierConfirmationData($event)"
          ></supplier-confirmation-dialog>
        </div>

        <div *ngSwitchCase="'Supplier Consolidation'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Supplier Consolidation Confirmation
          </div>
          <supplier-consolidation-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
            (updateStatusVisibility)="updateStatusVisibility($event)"
            (getAdditionalDeliveryMethodData)="
              getAdditionalDeliveryMethodData($event)
            "
          ></supplier-consolidation-dialog>
        </div>

        <div *ngSwitchCase="'Management Company'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Management Company Confirmation
          </div>
          <management-company-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
            (updateStatusVisibility)="updateStatusVisibility($event)"
            (getAdditionalDeliveryMethodData)="
              getAdditionalDeliveryMethodData($event)
            "
          ></management-company-dialog>
        </div>

        <div *ngSwitchCase="'Additional Delivery Details'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Additional Delivery Details
          </div>
          <additional-delivery-details-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
            (updateStatusVisibility)="updateStatusVisibility($event)"
            (getAdditionalDeliveryMethodData)="
              getAdditionalDeliveryMethodData($event)
            "
          ></additional-delivery-details-dialog>
        </div>

        <div *ngSwitchCase="'Warehouse'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Warehouse
          </div>
          <warehouse-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
          ></warehouse-dialog>
        </div>

        <div *ngSwitchCase="'Direct to Vessel'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Direct To Vessel
          </div>
          <direct-to-vessel-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
          ></direct-to-vessel-dialog>
        </div>

        <div *ngSwitchCase="'Agent'">
          <div
            class="status-status"
            [ngClass]="{ active: selectedItem.isComplete }"
          >
            Agent
          </div>
          <direct-to-vessel-dialog
            [item]="selectedItem"
            [poUid]="poUid"
            [poIssueUid]="poIssueUid"
          ></direct-to-vessel-dialog>
        </div>
      </div>
    </div>
  </section>
</jb-layout-widget>
