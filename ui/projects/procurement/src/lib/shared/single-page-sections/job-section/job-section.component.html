<prc-base-section
  [gridInputs]="gridInputs"
  [isEditMode]="isEditMode"
  (gridAction)="onGridAction($event)"
></prc-base-section>

<ng-template #descriptionTemplate let-rowData>
  <div class="description-column">
    <ng-container *ngIf="rowData.description || ' ' | stripHtml as description">
      <span class="text-ellipsis" [prcTooltip]="description">
        {{ description }}
      </span>
    </ng-container>

    <img
      class= 'external-link-icon'
      [class.disabled]="!(permission.AddEditServices | hasPermissions)"
      src="assets/procurement/images/external-link.svg"
      (click)="openDescriptionPopup(rowData)"
    />
  </div>
</ng-template>

<prc-description-popup
  *ngIf="descriptionPopupData"
  [data]="descriptionPopupData"
  (close)="onCloseDescriptionPopup($event)"
></prc-description-popup>

<ng-template #codeTemplate let-rowData>
  <a
  class="jb_grid_topCellValue jb-link-600-14"
  target="_blank"
  [href]="getNavigationUrl(rowData)"
  >{{ rowData.code }}
</a>
</ng-template>  