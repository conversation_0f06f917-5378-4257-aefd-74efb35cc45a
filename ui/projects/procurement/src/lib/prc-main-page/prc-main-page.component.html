<div class="main-grid">
  <jb-grid
    selectionMode="selectionMode"
    [advancedSettings]="customAdvancedSettings"
    [colData]="gridInputs.columns"
    [filterData]="gridInputs.filters"
    [filterListsSet]="gridInputs.filtersLists"
    [getStyleByContainer]="true"
    [gridName]="gridInputs.gridName"
    [searchFields]="gridInputs.searchFields"
    [setActions]="prcGridAction"
    [showSettings]="advanceSettings"
    [sidebarFloat]="sidebarFloatConfig"
    [tableDataReq]="gridInputs.request"
    [toolTipFields]="prcIndexToolTipFields"
    (action)="gridAction($event)"
  >
    <ng-template #sidebarFloat>
      <ng-container [ngSwitch]="selectedRow?.type">
        <prc-requisition-right-pane
          *ngSwitchCase="'Requisition'"
          [requisitionUid]="selectedRow.uid"
          (close)="onCloseSlider()"
        ></prc-requisition-right-pane>
        <prc-po-right-pane
          *ngSwitchCase="'PO'"
          [poUid]="selectedRow?.uid"
          (close)="onCloseSlider()"
        ></prc-po-right-pane>
      </ng-container>
    </ng-template>
  </jb-grid>
</div>

<ng-template #accountsTemplate let-rowData>
  <div
    *ngIf="rowData.glAccountNames | prcJoin as account"
    class="text-ellipsis"
    [prcTooltip]="account"
  >
    {{ account }}
  </div>
</ng-template>

<ng-template #canceledDateTemplate let-rowData>
  <div class="template-column">
    <span class="text-ellipsis" [prcTooltip]="rowData.cancelDate | jbDate">
      {{ rowData.cancelDate | jbDate }}</span
    >
  </div>
</ng-template>

<ng-template #deliveryDetailsTemplate let-rowData>
  <div class="text-ellipsis" [prcTooltip]="rowData | formatPort">
    <span class="name-wrapper"
      ><a class="jb_grid_topCellValue jb-link-600-14" *ngIf="rowData">
        {{ rowData | formatPort }}</a
      >
    </span>
    <div class="jb-breadcrumb">
      <div
        class="jb_grid_botCellValue text-ellipsis"
        [title]="rowData?.deliveryDate"
      >
        {{ rowData.deliveryDate | utcToLocal | jbDate }}
      </div>
    </div>
  </div>
</ng-template>

<ng-template #raisedDateTemplate let-rowData>
  <div class="template-column">
    <span class="text-ellipsis" [prcTooltip]="rowData.raisedDate | jbDate">
      {{ rowData.raisedDate | jbDate }}</span
    >
  </div>
</ng-template>

<ng-template #subjectTemplate let-rowData>
  <div class="template-column">
    <a
      class="jb_grid_topCellValue jb-link-600-14 text-ellipsis"
      [prcNavigationLink]="getLink(rowData)"
      [queryParams]="{ tab_title: rowData.recordNumber }"
      [prcTooltip]="rowData.subject"
      target="_blank"
      >{{ rowData.subject }}
    </a>
  </div>
</ng-template>

<prc-icon-template
  [attachmentColumn]="attachmentColumn"
  [gridColumns]="gridInputs.columns"
  [iconColumn]="iconColumn"
></prc-icon-template>
