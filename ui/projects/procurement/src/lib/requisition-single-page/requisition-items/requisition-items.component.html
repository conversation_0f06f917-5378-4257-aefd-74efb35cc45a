<prc-base-section
  [editableFields]="editableFields"
  [gridInputs]="gridInputs"
  [isEditMode]="isEditMode"
  (cellChange)="onCellChange($event)"
  (gridAction)="onGridAction($event)"
></prc-base-section>

<ng-template #iconTemplate let-rowData>
  <div class="requisition-item-icon">
    <i
      class="icons8-private-3 export-control-icon"
      *ngIf="rowData.ecStatus !== 'notEC'"
      pTooltip="EC"
      [ngClass]="rowData.ecStatus"
    ></i>
    <img
      *ngIf="rowData.criticality"
      alt="critical-icon"
      pTooltip="Critical"
      src="assets/images/criticality.svg"
    />
    <img
      *ngIf="rowData.dg"
      alt="dg-icon"
      pTooltip="Dangerous Goods (DG)"
      src="assets/images/DG-icon.svg"
    />
    <img
      *ngIf="rowData.ihm"
      alt="ihm-icon"
      pTooltip="Inventory of Hazardous Material (IHM)"
      src="assets/images/ihm.svg"
    />
  </div>
</ng-template>

<ng-template #itemTypeTemplate let-rowData>
  {{
    (rowData.itemTypeUid | jcdsItem : "ItemsCategory" | async)?.category_name
  }}
</ng-template>

<ng-template #robTemplate let-rowData>
  <span *ngIf="showDbRob; else showUpdatedRob">
    {{ rowData.rob }}
  </span>
  <ng-template #showUpdatedRob>
    <span *ngIf="(robs$ | async)?.length && rowData.itemUid as itemUid">{{
      itemUid | rob | async
    }}</span>
  </ng-template>
</ng-template>

<ng-template #tagsTemplate let-rowData>
  {{ getTagsNames(rowData.tags) | prcJoin }}
</ng-template>

<ng-template #itemNameTemplate let-rowData>
  <div class="item-name">
    <span
      class="text-ellipsis name"
      [prcTooltip]="rowData.itemName"
      appendTo="body"
    >
  <a
  class="jb_grid_topCellValue jb-link-600-14"
      target="_blank"
      [prcNavigationLink]="[itemSinglePageRoute, rowData.itemUid]"
      > {{rowData.itemName}}</a>
    </span>
    </div>
</ng-template>


