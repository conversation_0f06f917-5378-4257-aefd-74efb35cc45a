<prc-finance-table
  [bulkPurchase]="bulkPurchase"
  [deliveryDate]="deliveryDate"
  [financeGridInputs]="gridInputs"
  [selectedAccountsMap]="selectedAccountsMap"
  [vesselUid]="vesselUid"
  (getEditableData)="getEditableData($event)"
  (action)="onAction($event)"
  (matrixDataChange)="handleMatrixDataChange($event)"
></prc-finance-table>

<prc-split-popup
  *ngIf="splitItemUid"
  [selectedUid]="splitItemUid"
  (confirm)="onSplit($event)"
  (close)="closeSplitDialog()"
></prc-split-popup>
