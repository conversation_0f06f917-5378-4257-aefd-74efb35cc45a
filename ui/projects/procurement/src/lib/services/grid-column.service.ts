import { Injectable, TemplateRef } from '@angular/core';
import { supplant } from '@j3-procurement/dtos';
import { ColumnPreferencesDto } from '@j3-procurement/dtos/grid';
import { ApiRequestService, Column, ColumnTyped, eCrud } from 'jibe-components';
import { Observable } from 'rxjs';

import { ePrcRequestAction } from '../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../models/enums/prc-request-entity.enum';

@Injectable({
  providedIn: 'root',
})
export class GridColumnService {
  constructor(private readonly apiRequestService: ApiRequestService) {}

  public async configureColumns<T extends string>(
    gridName: string,
    columns: ColumnTyped<T>[],
    columnTemplateMap?: Record<string, TemplateRef<HTMLElement>>
  ): Promise<ColumnTyped<T>[]> {
    const columnPreferences = await this.getColumnPreferences(
      gridName
    ).toPromise();
    const columnPreferencesMap = new Map(
      columnPreferences.map((c) => [c.FieldName, c])
    );

    return columns
      .map(
        (column) =>
          ({
            ...column,
            cellTemplate: this.getColumnTemplate(column, columnTemplateMap),
            ...columnPreferencesMap.get(column.FieldName),
          } as ColumnTyped<T>)
      )
      .sort((a, b) => a.OrderBy - b.OrderBy);
  }

  public getColumnPreferences(
    gridName: string
  ): Observable<ColumnPreferencesDto[]> {
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Grid,
      action: supplant(ePrcRequestAction.ColumnPreferences, { gridName }),
      crud: eCrud.Get,
    });
  }

  public async saveColumnPreferences(
    gridName: string,
    columns: Column[]
  ): Promise<void> {
    await this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.Grid,
        action: supplant(ePrcRequestAction.ColumnPreferences, { gridName }),
        crud: eCrud.Put,
        body: columns?.map(
          ({ FieldName, IsVisible, OrderBy }): ColumnPreferencesDto => ({
            FieldName,
            IsVisible,
            OrderBy,
          })
        ),
      })
      .toPromise();
  }

  private getColumnTemplate(
    column: Column,
    columnTemplateMap?: Record<string, TemplateRef<HTMLElement>>
  ): TemplateRef<HTMLElement> {
    const columnTemplate = columnTemplateMap?.[column.FieldName];
    return columnTemplate ?? column.cellTemplate;
  }
}
