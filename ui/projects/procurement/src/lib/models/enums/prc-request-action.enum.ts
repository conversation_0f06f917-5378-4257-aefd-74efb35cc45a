export enum ePrcRequestAction {
  AdditionalChargesList = 'additional-charges/list',
  AgentList = 'agents/list',
  ApproveFlowStep = 'flow/{historyUid}/v2/approve',
  BrowsingItems = '{ownerUid}/items',
  BrowsingJobs = '{ownerUid}/jobs',
  CatalogMachineries = 'catalogs/machineries',
  ColumnPreferences = '{gridName}/column-preferences',
  CreateApprovalFlow = 'flow',
  DeliveryInstruction = '{objectUid}',
  FileUpload = 'file/upload',
  FilterOptions = 'filter-options',
  FinancesAdditionalChargesList = '{uid}/finances-additional-charges/list',
  FinancesItemsGLAccounts = '{uid}/glaccounts',
  FinancesItemsList = '{uid}/finances-items/list',
  GetAgent = '{supplierUid}/agent',
  GetApprovalFlow = 'flow/{objectUid}',
  GetApprovalFlowList = 'flow/list',
  GetClientUid = 'company/get-all-company-details',
  GetDirectPOHasInvalidItems = '{requisitionUid}/direct-po/invalid-items',
  GetFiles = 'file/getFiles',
  GetFlowState = 'flow/{historyUid}/steps',
  GetJCDSUrl = 'asl-supplier/get-client-jcds-url-details',
  GetLibraryDataByCode = 'get-library-data-by-code',
  GetNextWorkflow = 'task-manager-workflow/get-task-manager-next-workflow-configuration',
  GetOnboardAndOfficeUser = 'get-onboard-and-office-user',
  GetRfqItemListReq = '{requisitionUid}/rfqs-items/list',
  GetRolesByUser = 'access-rights/user-roles/getRolesByUser',
  GetTaskManagerJob = 'get-task-manager-by-uid',
  GetTaskManagerWorklist = 'task-manager-workflow/get-tm-all-wf-config-details-by-task-type',
  GetUserDetails = 'userDetails/getUserDetails',
  GetVesselDetails = 'vessel/get-details-of-vessels',
  GetVesselByUser = 'vessel-assignment/getVesselByUser',
  IhmTypes = 'ihm-types',
  InitApprovalFlow = 'flow-init',
  InvoiceLine = 'save-invoice-line/{ownerUid}',
  ItemListItems = '{itemListUid}/items',
  ItemListItemsFilterOptions = '{itemListUid}/items/filter-options',
  ItemListItemsList = '{itemListUid}/items/list',
  ItemListJobs = '{itemListUid}/jobs',
  ItemListJobsList = '{itemListUid}/jobs/list',
  ItemListSegments = '{itemListUid}/segments',
  ItemListClone = '{itemListUid}/clone',
  List = 'list',
  ListFilterOptions = 'list/filter-options',
  OrderStatusList = '{poUid}/order-status/list',
  PoConfirmDelivery = '{poUid}/confirm-delivery',
  PoItemList = '{poUid}/items/v2/list',
  PoPreview = '{poUid}/preview',
  QuotationAdditionalDetails = '{quotationUid}/additional-details',
  QuotationCharges = '{requisitionUid}/quotation/charges',
  QuotationEvaluation = 'evaluation',
  QuotationEvaluationDetails = 'evaluation/details',
  QuotationItemStates = '{requisitionUid}/quotation-item/states',
  QuotationList = '{requisitionUid}/quotation/list',
  QuotationSendForRework = '{quotationUid}/rework',
  RequisitionClosing = '{requisitionUid}/closing',
  RequisitionDirect = '{requisitionUid}/direct',
  RequisitionDuplicateValidations = '{requisitionUid}/duplicate/validations',
  RequisitionFinanceItems = '{requisitionUid}/finances-items',
  RequisitionItemsFilterOptions = '{requisitionUid}/items/filter-options',
  RequisitionItemsList = '{requisitionUid}/items/list',
  RequisitionParentCatalogs = '{requisitionUid}/parent-catalogs',
  RequisitionPendingItemsJobsCount = '{requisitionUid}/pending-items-jobs-count',
  RequisitionPendingWithMeCount = 'pending-with-me-count',
  RequisitionRfqs = '{requisitionUid}/rfqs',
  RequisitionSegments = '{requisitionUid}/segments',
  RequisitionSupplierList = '{requisitionUid}/suppliers/list',
  RfqExportPdf = '{rfqUid}/export',
  RfqItems = '{rfqUid}/{type}/list',
  RfqSaveCC = '{rfqUid}/cc',
  SaveTaskManagerJobs = 'save-task-manager-jobs/',
  ScanReviewLine = 'link-catalog-item-to-scan-review-line/{ownerUid}',
  RequisitionApprovalProccessInit = '{requisitionUid}/quotation/approval-proccess-init',
  UpdateDeliveryInstructions = '{poUid}/delivery-instructions',
  UpdateTaskManagerWorkflowAction = 'task-manager-generic/update-task-manager-work-flow-action',
  UpdateTaskStatusRole = '{objectUid}/role',
  VesselItemLists = '{vesselUid}/itemlists',
  SplitItem = '{uid}/split',
  SplitAdditionalCharge = '{requisitionUid}/finances-additional-charges/{additionalChargeUid}/split',
}
