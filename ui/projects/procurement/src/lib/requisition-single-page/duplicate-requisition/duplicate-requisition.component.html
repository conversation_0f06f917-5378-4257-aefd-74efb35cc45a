<jb-dialog
  [dialogContent]="duplicateRequisitionDialog"
  [(dialogVisible)]="isOpen"
  (closeDialog)="closeDuplicateRequisitionPopup()"
>
  <div jb-dialog-body class="requisition-dialog-body">
    <form [formGroup]="form">
      <div class="two-columns">
        <div class="column">
          <prc-form-label
            *ngIf="vesselDropdown"
            label="Vessel"
            [required]="true"
          >
            <jb-single-select-dropdown
              formControlName="vessel"
              class="margin-top"
              [content]="vesselDropdown"
            ></jb-single-select-dropdown>
          </prc-form-label>
          <prc-form-label label="Purchase Type" [required]="true">
            <jb-single-select-dropdown
              class="margin-top"
              [content]="poTypeDropdown"
            ></jb-single-select-dropdown>
          </prc-form-label>
          <prc-form-label label="Urgency" [required]="true">
            <jb-single-select-dropdown
              class="margin-top"
              [content]="urgencyDropdown"
            ></jb-single-select-dropdown>
          </prc-form-label>
        </div>
        <div class="column">
          <prc-form-label label="Port Call">
            <prc-upcoming-port-select
              formControlName="deliveryUpcomingPort"
              (selectedPortChange)="onUpcomingPortChange($event)"
              [vesselId]="vessel?.vesselId"
            ></prc-upcoming-port-select>
          </prc-form-label>
          <prc-form-label label="Delivery Port" [required]="true">
            <prc-generic-port-select
              formControlName="deliveryPort"
              (selectedPortChange)="onDeliveryPortChange($event)"
            ></prc-generic-port-select>
          </prc-form-label>
          <prc-form-label label="Delivery Date" [required]="true">
            <p-calendar
              formControlName="deliveryDate"
              [monthNavigator]="true"
              [yearNavigator]="true"
              yearRange="2000:2030"
              inputStyleClass="jb-text"
            ></p-calendar>
          </prc-form-label>
        </div>
      </div>
      <prc-form-label
        class="form-label-details"
        label="Subject"
        [required]="true"
      >
        <jb-textarea [content]="subjectTextArea"></jb-textarea>
      </prc-form-label>
    </form>
    <section
      *ngIf="vessel && gridInputs.data && gridInputs.data.length > 0"
      class="table ui-table-flex-scrollable duplicate-validation-outer"
    >
      <br />
      <b
        >Following items can not be added to requisition for
        {{ vessel.name }} while creating duplicate requisition:</b
      >
      <div class="inner">
        <jb-grid
          [colData]="gridInputs.columns"
          [getStyleByContainer]="true"
          [gridName]="gridInputs.gridName"
          [isDisplaySearchField]="false"
          [isDisplayAdvancedFilter]="false"
          [isDisplayAdvancedSettings]="false"
          [tableData]="gridInputs.data"
        ></jb-grid>
      </div>
    </section>
  </div>
  <div jb-dialog-footer>
    <jb-dialog-footer
      (cancel)="onCancel()"
      (ok)="onSave()"
      okBtnLabel="Create Duplicate"
      [isOkBtnDisabled]="form.invalid || !canDuplicate"
    ></jb-dialog-footer>
  </div>
</jb-dialog>
