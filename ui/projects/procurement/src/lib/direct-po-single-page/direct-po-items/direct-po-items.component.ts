import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NotificationService, UnsubscribeComponent } from 'j3-prc-components';
import { eGridCellType, GridAction } from 'jibe-components';
import {
  additionalColumns,
  ColumnKey,
  columns,
  eGridAction,
  getActions,
  gridName,
} from './direct-po-items-grid-inputs';
import { getAmountByRowData, mapDirectPOItem } from './utils';

import { WorkflowType } from '@j3-prc-shared/dtos/task-status';
import { DirectPOItemDto } from '@j3-procurement/dtos/direct-po';
import { SaveQuotationItemDto } from '@j3-procurement/dtos/quotation';
import { Observable } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DB_ROB_STATUSES } from '../../models/constants';
import { DirectPoPermission } from '../../models/enums/direct-po-permission.enum';
import { ePrcErrorMessages } from '../../models/enums/prc-messages.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { GridInputsWithData } from '../../models/interfaces/grid-inputs';
import { JbCellChangeEvent } from '../../models/interfaces/jb-cell-change-event';
import { DirectPOService } from '../../services/direct-po/direct-po.service';
import { PermissionService } from '../../services/permission/permission.service';
import { RobService } from '../../services/rob/rob.service';

@Component({
  selector: 'prc-direct-po-items',
  templateUrl: './direct-po-items.component.html',
  styleUrls: ['./direct-po-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectPoItemsComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() requisitionUid: string;
  @Input() quotationUid: string;
  @Input() vesselUid: string;
  @Input() set status(value: WorkflowType) {
    this.showDbRob = DB_ROB_STATUSES.includes(value);
    if (this.showDbRob) {
      this.loadGridData();
    }
  }
  @Output() directPOItemChange = new EventEmitter<SaveQuotationItemDto>();
  @Output() directPOItemsChange = new EventEmitter<DirectPOItemDto[]>();
  public gridInputs: GridInputsWithData<ColumnKey, DirectPOItemDto> = {
    columns: [...columns, ...additionalColumns],
    gridName,
    data: [],
    actions: [],
  };
  public robs$ = new Observable<Number[]>();
  public showDbRob: boolean;
  public itemSinglePageRoute = ePageRoutes.ItemSinglePage;
  private columnFieldToCellTemplateMapper: Partial<
    Record<ColumnKey, TemplateRef<HTMLElement>>
  >;

  @ViewChild('dgTemplate', { static: true })
  dgTemplate: TemplateRef<HTMLElement>;

  @ViewChild('itemNameTemplate', { static: true })
  itemNameTemplate: TemplateRef<HTMLElement>;

  @ViewChild('ihmTemplate', { static: true })
  ihmTemplate: TemplateRef<HTMLElement>;

  @ViewChild('activeStatusTemplate', { static: true })
  activeStatusTemplate: TemplateRef<HTMLElement>;

  @ViewChild('robTemplate', { static: true })
  robTemplate: TemplateRef<HTMLElement>;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly directPOService: DirectPOService,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly robService: RobService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    await this.loadGridData();
    const itemUids = this.gridInputs.data.map(({ itemUid }) => itemUid);
    this.robs$ = this.robService.getRobs(itemUids, this.vesselUid);
    this.directPOItemsChange.emit(this.gridInputs.data);
    this.loadGridInitiators();

    const hasAddEditItemAccess = await this.permissionService.hasPermissions(
      DirectPoPermission.EditItem
    );

    this.directPOService.isReadonly$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((isDirectPOClosed) => {
        this.disableDirectPO(hasAddEditItemAccess && !isDirectPOClosed);
      });
  }

  onCellChange(cellData: JbCellChangeEvent<DirectPOItemDto>): void {
    const { rowData, cellName } = cellData;
    const field = cellName.FieldName;
    let value = rowData[field];
    if (cellName.FieldName === 'discount' && +value > cellName.validMax) {
      this.notificationService.error(ePrcErrorMessages.InvalidDiscount);
      value = cellName.validMax;
      rowData[field] = +value;
    }
    const mappedItems = mapDirectPOItem(rowData);
    this.setFieldvalue(rowData.uid, 'amount', getAmountByRowData(rowData));
    this.directPOItemChange.emit(mappedItems);
  }

  public onAction({
    type,
    payload,
  }: GridAction<eGridAction, DirectPOItemDto>): void {
    const activeStatus = type === eGridAction.Add;
    this.setFieldvalue(payload.uid, 'activeStatus', activeStatus);
    this.directPOItemChange.emit({
      ...mapDirectPOItem(payload),
      activeStatus,
    });
    this.cdr.markForCheck();
  }

  private setFieldvalue(
    uid: string,
    fieldName: ColumnKey,
    fieldValue: number | boolean
  ): void {
    this.gridInputs.data = this.gridInputs.data.map((record) =>
      record.uid === uid ? { ...record, [fieldName]: fieldValue } : record
    );
    this.cdr.markForCheck();
  }

  loadGridInitiators(): void {
    this.columnFieldToCellTemplateMapper = {
      ihm: this.ihmTemplate,
      dg: this.dgTemplate,
      itemName: this.itemNameTemplate,
      activeStatus: this.activeStatusTemplate,
      rob: this.robTemplate,
    };
    this.gridInputs.columns.forEach((col) => {
      const template = this.columnFieldToCellTemplateMapper?.[col.FieldName];
      col.cellTemplate = template;
    });
  }

  disableDirectPO(isReadOnly: boolean): void {
    this.gridInputs.columns.forEach((col) => {
      if (col.hasOwnProperty(eGridCellType.Editable)) {
        col.Editable = isReadOnly;
      }
    });
    this.gridInputs.actions = getActions(isReadOnly);
    this.cdr.markForCheck();
  }

  private async loadGridData(): Promise<void> {
    this.gridInputs.data = await this.directPOService
      .getDirectPOItemsMatrix(this.quotationUid)
      .toPromise();
  }
}
