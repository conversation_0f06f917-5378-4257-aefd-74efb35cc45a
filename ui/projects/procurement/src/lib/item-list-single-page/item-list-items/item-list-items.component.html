<jb-grid
  [colData]="gridInputs.columns"
  [filterData]="gridInputs.filters"
  [filterListsSet]="gridInputs.filtersLists"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [isDisplayAdvancedFilter]="true"
  [searchFields]="gridInputs.searchFields"
  [setActions]="!ngControl?.disabled ? gridInputs.actions : undefined"
  [tableData]="gridInputs.data"
  [tableDataReq]="gridInputs.request"
  (action)="onGridAction($event)"
  (cellChange)="onCellChange($event)"
></jb-grid>

<ng-template #itemNameTemplate let-rowData>
  <div class="item-name">
    <span
      class="text-ellipsis name"
      [prcTooltip]="rowData.itemName"
      appendTo="body"
    > <a class="jb_grid_topCellValue jb-link-600-14"
    target="_blank"
    [prcNavigationLink]="[itemSinglePageRoute, rowData.itemUid]"
    > {{rowData.itemName}}</a>
    </span>
    <span class="icons">
      <img
      *ngIf="rowData.criticalityUid"
      alt="critical-icon"
      class="critical-icon"
      src="assets/images/criticality.svg"
    />
      <img
        *ngIf="rowData.ihm"
        alt="ihm-icon"
        class="ihm-icon"
        src="assets/images/ihm.svg"
      />
      <img
        *ngIf="rowData.dangerousGoodsUid"
        alt="dg-icon"
        class="dangerous-goods-icon"
        src="assets/images/DG-icon.svg"
      />
    </span>
  </div>
</ng-template>

<ng-template #itemTypeTemplate let-rowData>
  <span *ngIf="rowData.itemTypeUid as itemTypeUid">{{
    (itemTypeUid | jcdsItem : "ItemsCategory" | async)?.category_name || UNKNOWN
  }}</span>
</ng-template>

<ng-template #tagsTemplate let-rowData>
  <prc-label
    *ngFor="let tag of rowData.tags"
    [label]="
      tag?.name || (tag?.uid | jcdsItem : 'tags' | async)?.names || UNKNOWN
    "
  ></prc-label>
</ng-template>

<ng-template #robTemplate let-rowData>
  <span *ngIf="showDbRob; else showUpdatedRob">
    {{ rowData.rob }}
  </span>
  <ng-template #showUpdatedRob>
    <span *ngIf="(robs$ | async)?.length && rowData.itemUid as itemUid">{{
      itemUid | rob | async
    }}</span>
  </ng-template>
</ng-template>
