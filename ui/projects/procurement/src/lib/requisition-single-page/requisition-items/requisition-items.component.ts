import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Optional,
  Output,
  Self,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  ApiRequestService,
  eGridEvents,
  GridAction,
  GridService,
  GridShareDataService,
  MatrixDataChanged,
} from 'jibe-components';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import {
  getRequisitionItemsReq,
  RequisitionService,
} from '../../services/requisition/requisition.service';
import {
  additionalColumns,
  additionalFilters,
  columns,
  filters,
  getActions,
  getFiltersLists,
  gridName,
} from './grid-inputs';

import { NgControl } from '@angular/forms';
import { ItemExportControlDto } from '@j3-prc-catalog/dtos/item';
import { WorkflowType } from '@j3-prc-shared/dtos/task-status';
import {
  EcStatus,
  UpdateEcDto,
  UpdateEcResponseDto,
  UpdateItemEcDto,
} from '@j3-procurement/dtos/export-control';
import { Label } from '@j3-procurement/dtos/label';
import { ObjectCountDto } from '@j3-procurement/dtos/object-count';
import {
  RequisitionItemDto,
  UpdateItemDto,
} from '@j3-procurement/dtos/requisition';
import { UnsubscribeComponent } from 'j3-prc-components';
import { Observable } from 'rxjs';
import {
  debounceTime,
  filter,
  switchMap,
  take,
  takeUntil,
} from 'rxjs/operators';
import { DB_ROB_STATUSES, UNKNOWN } from '../../models/constants';
import { EcPermission } from '../../models/enums/ec-permssion.enum';
import { ePageRoutes } from '../../models/enums/prc-routes.enum';
import { RequisitionPermission } from '../../models/enums/requisition-permission.enum';
import { JbCellChangeEvent } from '../../models/interfaces/jb-cell-change-event';
import { EcService } from '../../services/export-control/ec.service';
import { PermissionService } from '../../services/permission/permission.service';
import { RequisitionItemService } from '../../services/requisition/requisition-item.service';
import { RobService } from '../../services/rob/rob.service';
import { BaseSectionService } from '../../shared/single-page-sections/base-section/base-section.service';
import { RowChangesService } from '../../shared/single-page-sections/base-section/row-changes.service';
import { searchFields } from '../requisition-supplier/requisition-supplier-attachments/requisition-supplier-attachments-grid';
import { editableFields, eGridRowActions } from './grid-constants';
import { ColumnKey } from './types';

@Component({
  selector: 'prc-requisition-items',
  templateUrl: './requisition-items.component.html',
  styleUrls: ['./requisition-items.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [GridShareDataService, BaseSectionService, RowChangesService],
})
export class RequisitionItemsComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public robs$ = new Observable<number[]>();
  public editableFields = editableFields;
  public showDbRob: boolean;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, RequisitionItemDto>;
  public UNKNOWN = UNKNOWN;
  public itemSinglePageRoute = ePageRoutes.ItemSinglePage;

  private mapEcStatus: Partial<Record<eGridRowActions, EcStatus>> = {
    'Block Export Control': 'blocked',
    'Release Export Control': 'released',
  };
  private removedItemUids = new Set<string>();

  @Input() isEditMode: boolean;
  @Input() requisitionUid: string;
  @Input() set status(value: WorkflowType) {
    this.showDbRob = DB_ROB_STATUSES.includes(value);
    if (this.showDbRob) {
      this.gridService.refreshGrid('Table', gridName);
    }
  }
  @Input() vesselUid: string;
  @Input() set readonly(value: boolean) {
    this.setActions(value);
  }

  @Output() ecStatusChanged = new EventEmitter<Partial<ObjectCountDto>>();

  @ViewChild('iconTemplate', { static: true })
  iconTemplate: TemplateRef<HTMLElement>;

  @ViewChild('itemTypeTemplate', { static: true })
  itemTypeTemplate: TemplateRef<HTMLElement>;

  @ViewChild('robTemplate', { static: true })
  robTemplate: TemplateRef<HTMLElement>;

  @ViewChild('tagsTemplate', { static: true })
  tagsTemplate: TemplateRef<HTMLElement>;

  @ViewChild('itemNameTemplate', { static: true })
  itemNameTemplate: TemplateRef<HTMLElement>;

  constructor(
    @Optional() @Self() private ngControl: NgControl,
    private readonly apiRequestService: ApiRequestService,
    private readonly baseSectionService: BaseSectionService,
    private readonly gridService: GridService,
    private readonly requisitionService: RequisitionService,
    private readonly rowChangesService: RowChangesService,
    private readonly permissionService: PermissionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly requisitionItemsService: RequisitionItemService,
    private readonly ecService: EcService,
    private readonly robService: RobService
  ) {
    super();
    this.baseSectionService.ngControl = ngControl;
  }

  ngOnInit(): void {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      icon: this.iconTemplate,
      itemType: this.itemTypeTemplate,
      rob: this.robTemplate,
      tags: this.tagsTemplate,
      itemName: this.itemNameTemplate,
    };

    this.gridInputs = {
      gridName,
      columns: [...columns, ...additionalColumns].map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      filters: [...filters, ...additionalFilters],
      filtersLists: getFiltersLists([], [], [], [], []),
      request: getRequisitionItemsReq(this.requisitionUid),
    };

    this.ngControl.control.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((v) => !v)
      )
      .subscribe((_) => this.rowChangesService.resetChanges());

    this.setFiltersLists();

    this.gridService.storeState$
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter(
          (event) =>
            event.gridName === gridName && event.type === eGridEvents.Pagination
        ),
        debounceTime(5),
        switchMap(() =>
          this.gridService.storeData$.pipe(
            filter((event) => event.gridName === gridName),
            take(1)
          )
        )
      )
      .subscribe(() => {
        const requisitionCounts = this.hideRemovedItems();
        this.updateEcIconHeader(requisitionCounts);
      });

    this.gridService.matrixDataChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === gridName)
      )
      .subscribe(
        async ({ matrixValues }: MatrixDataChanged<RequisitionItemDto>) => {
          if (!matrixValues?.length) {
            return;
          }
          const itemUids = [];
          const newMatrixValues = matrixValues.map(
            (matrixValue): RequisitionItemDto => {
              itemUids.push(matrixValue.itemUid);
              return {
                ...matrixValue,
                ...this.rowChangesService.getRowChanges(matrixValue.uid),
              };
            }
          );

          this.robs$ = this.robService.getRobs(itemUids, this.vesselUid);

          this.gridService.storeData$.next({
            gridName,
            data: newMatrixValues,
          });
        }
      );
  }

  private hideRemovedItems(hideOne = false): ObjectCountDto {
    const removedItemsNumber = this.removedItemUids.size;
    const matrixData =
      this.baseSectionService.getMatrixData<RequisitionItemDto>();
    if (removedItemsNumber !== null && matrixData) {
      const { count, records } = matrixData;

      const newRecords = records.filter(
        ({ uid }) => !this.removedItemUids.has(uid)
      );

      /** 0 if filter has been applied */
      const deduction = removedItemsNumber > count ? 0 : removedItemsNumber;
      const newCount = count - (hideOne ? 1 : deduction);

      this.gridInputs.data = {
        count: newCount,
        records: newRecords,
      };

      const { ecBlockedCount, ecReleasedCount } = newRecords.reduce(
        (acc, { ecStatus: status }) => {
          if (status === 'blocked') {
            acc.ecBlockedCount++;
          } else if (status === 'released') {
            acc.ecReleasedCount++;
          }
          return acc;
        },
        { ecBlockedCount: 0, ecReleasedCount: 0 }
      );

      const objectCounts: ObjectCountDto = {
        ecBlockedCount,
        ecReleasedCount,
        objectType: 'requisition',
        objectUid: this.requisitionUid,
      };
      return objectCounts;
    }
  }

  public onCellChange(event: JbCellChangeEvent<RequisitionItemDto>): void {
    const { catalogUid, itemUid, uid } = event.rowData;
    const change: Partial<UpdateItemDto> = {};
    switch (event.cellName.FieldName as ColumnKey) {
      case 'itemQuantity':
        change.itemQuantity = Number(event.cellvalue);
        break;
      case 'itemRemark':
        change.itemRemark = event.cellvalue && String(event.cellvalue);
        break;
      default:
        return;
    }
    this.baseSectionService.updateRowData(uid, {
      ...change,
      catalogUid,
      itemUid,
    });
  }

  public async onGridAction({
    payload,
    type,
  }: GridAction<eGridRowActions, RequisitionItemDto>): Promise<void> {
    const { itemUid, catalogUid, uid } = payload;
    const changes: Partial<UpdateItemDto> = {};

    switch (type) {
      case eGridRowActions.DeleteRemark:
        payload.itemRemark = '';
        changes.itemRemark = '';
        break;
      case eGridRowActions.RemoveItem:
        this.removedItemUids.add(uid);
        changes.activeStatus = false;
        const requisitionCounts = this.hideRemovedItems(true);
        this.updateEcIconHeader(requisitionCounts);
        break;
      case eGridRowActions.ReleaseEc:
      case eGridRowActions.BlockEc:
        const status = await this.updateEcStatus(
          itemUid,
          this.mapEcStatus[type],
          payload
        );
        changes.ecStatus = status;
        break;
      case eGridRowActions.MarkEc:
        const updateItemEcResult = await this.createItemEcStatus(itemUid);
        if (updateItemEcResult) {
          const markResult = await this.updateItemEcStatus(itemUid);
          payload.ecStatus = markResult.ecStatus;
          changes.ecStatus = markResult.ecStatus;
          this.updateEcIconHeader(markResult.objectCount);
        }
        break;
      default:
        return;
    }

    this.baseSectionService.updateRowData(uid, {
      ...changes,
      catalogUid,
      itemUid,
    });

    this.cdr.markForCheck();
  }

  private async setFiltersLists(): Promise<void> {
    const filtersRequest =
      this.requisitionService.getRequisitionItemFiltersRequest(
        this.requisitionUid
      );

    const filterOptions = await this.apiRequestService
      .sendApiReq(filtersRequest)
      .toPromise();

    this.gridInputs.filtersLists = getFiltersLists(
      filterOptions?.brands.map(({ uid, name }) => ({
        label: name,
        value: uid,
      })),

      filterOptions?.catalogs.map(({ uid, name }) => ({
        label: name,
        value: uid,
      })),
      filterOptions?.models.map((modelNumber) => ({
        label: modelNumber,
        value: modelNumber,
      })),
      [],
      filterOptions?.statuses.map((status) => ({
        label: status,
        value: status,
      }))
    );

    this.baseSectionService.setFilters(this.gridInputs);
  }

  private async setActions(readonly = false): Promise<void> {
    const [addEditItemPermission, blockEcPermission, releaseEcPermission] =
      await Promise.all([
        this.permissionService.hasPermissions(
          RequisitionPermission.AddEditItem
        ),
        this.permissionService.hasPermissions(EcPermission.BlockEcItems),
        this.permissionService.hasPermissions(EcPermission.ReleaseEcItems),
      ]);

    this.gridInputs = {
      ...this.gridInputs,
      actions: getActions({
        itemSection: !addEditItemPermission || readonly,
        bolckEC: !blockEcPermission,
        releaseEc: !releaseEcPermission,
      }),
    };
    this.cdr.markForCheck();
  }

  private updateRequisitionEcStatus(
    itemUid: string,
    status: EcStatus
  ): Promise<UpdateEcResponseDto> {
    const body: UpdateEcDto = { itemUid, ecStatus: status };
    return this.requisitionItemsService
      .updateEcStatus(this.requisitionUid, body)
      .toPromise();
  }

  private createItemEcStatus(itemUid: string): Promise<string> {
    const body: ItemExportControlDto = { exportControl: true };
    return this.ecService.createItemEc(itemUid, body).toPromise();
  }

  private async updateEcStatus(
    itemUid: string,
    status: EcStatus,
    payload: RequisitionItemDto
  ): Promise<EcStatus> {
    const result = await this.updateRequisitionEcStatus(itemUid, status);
    payload.ecStatus = result.ecStatus;
    this.updateEcIconHeader(result.objectCount);
    return result.ecStatus;
  }

  private updateItemEcStatus(itemUid: string): Promise<UpdateEcResponseDto> {
    const body: UpdateItemEcDto = {
      objectUid: this.requisitionUid,
      exportControl: true,
    };
    return this.ecService.updateItemEc(itemUid, body).toPromise();
  }

  private updateEcIconHeader(objectCount: Partial<ObjectCountDto>): void {
    this.ecStatusChanged.emit(objectCount);
  }

  public getTagsNames(tags: Label[]): string[] {
    return tags?.map(({ name }) => name);
  }
}
