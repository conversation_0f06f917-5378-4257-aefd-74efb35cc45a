import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { ExtNavigationService, UnsubscribeComponent } from 'j3-prc-components';
import {
  Column,
  eGridCellType,
  GridService,
  WebApiRequest,
} from 'jibe-components';
import { filter, takeUntil } from 'rxjs/operators';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import {
  ColumnKey,
  columns,
  filters,
  filtersLists,
  getActions,
  gridActions,
  gridName,
  searchFields,
} from './direct-po-jobs-grid-inputs';

import { Maybe } from '@j3-prc-shared/dtos';
import { calculatePercentage } from '@j3-procurement/dtos';
import { DirectPOJobDto } from '@j3-procurement/dtos/direct-po';
import { SaveQuotationItemDto } from '@j3-procurement/dtos/quotation';
import { DirectPoPermission } from '../../models/enums/direct-po-permission.enum';
import { JbCellChangeEvent } from '../../models/interfaces/jb-cell-change-event';
import { DirectPOService } from '../../services/direct-po/direct-po.service';
import { PermissionService } from '../../services/permission/permission.service';
import { DescriptionPopupData } from '../../shared/single-page-sections/job-section/description-popup/types';
import { generateNavigationUrl } from '../../utils/generate-url';

@Component({
  selector: 'prc-direct-po-jobs',
  templateUrl: './direct-po-jobs.component.html',
  styleUrls: ['./direct-po-jobs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DirectPoJobsComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() requisitionUid: string;
  @Input() quotationUid: string;
  @Output() directPOJobChange = new EventEmitter<SaveQuotationItemDto>();

  @ViewChild('descriptionTemplate', { static: true })
  descriptionTemplate: TemplateRef<HTMLElement>;

  @ViewChild('codeTemplate', { static: true })
  codeTemplate: TemplateRef<HTMLElement>;

  public directPODisabled: boolean;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, DirectPOJobDto> = {
    actions: [],
    columns: [...columns],
    filters,
    filtersLists,
    gridName,
    searchFields,
  };

  public descriptionPopupData: Partial<DescriptionPopupData> & { uid: string };
  public jobsRequest: WebApiRequest;
  public permissions = DirectPoPermission;

  private gridRecords: DirectPOJobDto[];

  constructor(
    private directPOService: DirectPOService,
    private cdr: ChangeDetectorRef,
    private gridService: GridService,
    private readonly permissionService: PermissionService,
    private extNavigationService: ExtNavigationService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const hasEditSeviceAccess = await this.permissionService.hasPermissions(
      DirectPoPermission.EditService
    );

    this.gridInputs = {
      ...this.gridInputs,
      request: this.directPOService.getDirectPOJobsMatrixReq(this.quotationUid),
    };

    this.directPOService.isReadonly$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((isDirectPOClosed) => {
        this.directPODisabled = isDirectPOClosed;
        this.disableDirectPO(hasEditSeviceAccess && !isDirectPOClosed);
      });

    const descriptionColumn = this.getColumn('description');
    descriptionColumn.cellTemplate = this.descriptionTemplate;

    const codeColumn = this.getColumn('code');
    codeColumn.cellTemplate = this.codeTemplate;

    this.gridService.storeData$
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === gridName)
      )
      .subscribe((source) => {
        this.gridRecords = source.data as DirectPOJobDto[];
      });
  }

  onCellChange(cellData: JbCellChangeEvent<DirectPOJobDto>): void {
    const { rowData } = cellData;
    const decimialPercent = calculatePercentage(Number(rowData.discount));
    const amount = rowData.unPrice * decimialPercent ?? rowData.amount;
    const { jobUid } = rowData;
    this.setFieldvalue(jobUid, 'amount', amount);
    this.jobChange({ ...rowData, amount });
  }

  public onCloseDescriptionPopup(description: string): void {
    if (description !== undefined) {
      const { jobUid } = this.descriptionPopupData;
      this.setFieldvalue(jobUid, 'description', description);
    }
    this.descriptionPopupData = undefined;
  }

  public openDescriptionPopup(rowData: DirectPOJobDto): void {
    const { jobUid } = rowData;
    this.descriptionPopupData = {
      uid: rowData.uid,
      description: rowData.description,
      jobUid,
    };
  }

  private getColumn(columnName: string): Maybe<Column> {
    return this.gridInputs.columns.find((col) => col.FieldName === columnName);
  }

  public onAction(event): void {
    const activeStatus = event.type === gridActions.Add ? true : false;
    const { jobUid } = event.payload;
    this.setFieldvalue(jobUid, 'activeStatus', activeStatus);
    this.jobChange({ ...event.payload, activeStatus });
  }

  private setFieldvalue(
    jobUid: string,
    fieldName: ColumnKey,
    fieldValue: string | number | boolean
  ): void {
    this.gridService.storeData$.next({
      gridName,
      data: this.gridRecords.map((record) =>
        record.jobUid === jobUid
          ? { ...record, [fieldName]: fieldValue }
          : record
      ),
    });
    this.cdr.markForCheck();
  }

  private jobChange(rowData: DirectPOJobDto): void {
    const mappedItems = {
      itemUid: rowData.jobUid,
      activeStatus: rowData.activeStatus,
      types: {
        [rowData.type]: {
          unPrice: rowData.unPrice,
          leadDays: rowData.leadDays,
          amount: rowData.amount,
          discount: rowData.discount,
        },
      },
    };

    this.directPOJobChange.emit(mappedItems);
  }

  disableDirectPO(isReadOnly: boolean): void {
    this.gridInputs.columns.forEach((col) => {
      if (col.hasOwnProperty(eGridCellType.Editable)) {
        col.Editable = isReadOnly;
      }
    });
    this.gridInputs.actions = getActions(isReadOnly);
    this.cdr.markForCheck();
  }

  public getNavigationUrl(rowData: DirectPOJobDto): string {
    return this.extNavigationService.transformUrl(
      generateNavigationUrl(rowData)
    );
  }
}
