import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  ExtNavigationService,
  ModalDialogService,
  NotificationService,
  UnsubscribeComponent,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  eAppLocation,
  eGridEvents,
  GridAction,
  GridService,
  GridShareDataService,
  IJbDialog,
  MatrixDataChanged,
  WebApiRequest,
} from 'jibe-components';
import { BehaviorSubject, from } from 'rxjs';
import { filter, map, switchMap, takeUntil } from 'rxjs/operators';
import {
  ePrcSuccessMessages,
  ePrcWarnMessages,
} from '../../models/enums/prc-messages.enum';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../models/interfaces/grid-inputs';
import {
  additionalColumns,
  columns,
  exportExtraColumns,
  getActions,
  gridName,
  searchFields,
} from './grid-inputs';
import { ColumnKey, JobActionDetails } from './types';

import { PrcModuleCodes } from '@j3-procurement/dtos';
import { PoJobDto } from '@j3-procurement/dtos/po';
import f from 'odata-filter-builder';
import { InfraService } from '../../services/infra.service';
import { JCDSService } from '../../services/jcds.service';
import { PoDeliveryService } from '../../services/po/po-delivery.service';
import { POService } from '../../services/po/po.service';
import { Job } from '../../shared/single-page-sections/job-section/types';
import { generateNavigationUrl } from '../../utils/generate-url';
import { ViewIntegrationLog } from '../types';
import { eGridRowActions } from './grid-constants';

@Component({
  selector: 'prc-po-job',
  templateUrl: './po-job.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./po-job.component.scss'],
  providers: [GridShareDataService],
})
export class PoJobComponent extends UnsubscribeComponent implements OnInit {
  @Input() getJobsRequest: WebApiRequest;
  @Input() readonly: boolean;
  @Input() vesselId: number;
  @Input() isEditMode: boolean;
  @Input() poUid: string;
  @Input() set isGridActionsDisabled(value: boolean) {
    const isOfficeApp =
      this.cds.userDetails?.AppLocation === eAppLocation.Office;
    this.gridInputs = {
      ...this.gridInputs,
      actions: getActions(!value, isOfficeApp),
    };
  }
  @Output() actionChange = new EventEmitter<JobActionDetails>();

  @ViewChild('descriptionTemplate', { static: true })
  descriptionTemplate: TemplateRef<HTMLElement>;
  @ViewChild('componentTemplate', { static: true })
  componentTemplate: TemplateRef<HTMLElement>;
  @ViewChild('statusTemplate', { static: true })
  statusTemplate: TemplateRef<HTMLElement>;
  @ViewChild('servicePerformanceTemplate', { static: true })
  servicePerformanceTemplate: TemplateRef<HTMLElement>;
  @ViewChild('deliveryPortTemplate', { static: true })
  deliveryPortTemplate: TemplateRef<HTMLElement>;
  @ViewChild('attachmentTemplate', { static: true })
  attachmentTemplate: TemplateRef<HTMLElement>;
  @ViewChild('codeTemplate', { static: true })
  codeTemplate: TemplateRef<HTMLElement>;

  public content: IJbDialog = {
    dialogHeader: 'Description',
    dialogWidth: 480,
    showHeader: true,
    closableIcon: true,
  };

  public popupDescription: string;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, PoJobDto>;
  public isDeliveryPopupVisible = false;
  public isPoJobAttachmentsPopupVisible = false;
  public deliveryDetails: PoJobDto;
  public servicePerformances: Record<string, string> = {};
  public ports: Record<string, string> = {};
  public deliveryAction: eGridRowActions;
  public exportExtraColumn = exportExtraColumns;
  public currentPoJobUid: string;
  private showIntegrationLogSubject = new BehaviorSubject<boolean>(false);
  public showIntegrationLogPopup$ =
    this.showIntegrationLogSubject.asObservable();

  public viewIntegrationLogObject: ViewIntegrationLog;

  constructor(
    private gridService: GridService,
    private jcdsService: JCDSService,
    private modalDialogService: ModalDialogService,
    private poDeliveryService: PoDeliveryService,
    private poService: POService,
    private readonly cdr: ChangeDetectorRef,
    private readonly notificationService: NotificationService,
    private readonly infraService: InfraService,
    private readonly cds: CentralizedDataService,
    private extNavigationService: ExtNavigationService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      description: this.descriptionTemplate,
      catalogName: this.componentTemplate,
      status: this.statusTemplate,
      servicePerformanceUid: this.servicePerformanceTemplate,
      deliveryPortId: this.deliveryPortTemplate,
      attachment: this.attachmentTemplate,
      code: this.codeTemplate,
    };

    this.gridInputs = {
      ...this.gridInputs,
      gridName,
      columns: [...columns, ...additionalColumns].map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      request: this.getJobsRequest,
    };

    this.loadMappingData();

    this.gridService.matrixDataChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((event) => event.gridName === gridName),
        map(({ matrixValues }: MatrixDataChanged<PoJobDto>) => ({
          matrixValues,
          uids: matrixValues.map(({ uid }) => uid),
        })),
        switchMap(({ matrixValues, uids }) =>
          from(
            this.infraService.getFiles({
              key1: this.poUid,
              modulecode: PrcModuleCodes.Procurement,
              $filter: new f().in('key2', uids).toString(),
            })
          ).pipe(
            map(({ records }) => {
              const filesMap = new Map(
                records?.map(({ key2, ...rest }) => [key2, rest])
              );
              return matrixValues.map(({ uid, ...rest }) => ({
                ...rest,
                uid,
                hasAttachments: filesMap.get(uid),
              }));
            })
          )
        )
      )
      .subscribe((data) =>
        this.gridService.storeData$.next({
          gridName: this.gridInputs.gridName,
          data,
        })
      );
    this.cdr.markForCheck();
  }

  public async onGridAction({
    payload,
    type,
  }: GridAction<eGridRowActions, PoJobDto>): Promise<void> {
    this.deliveryAction = type;
    switch (type) {
      case eGridRowActions.MarkAsPending:
        await this.markPending(payload);
        break;
      case eGridRowActions.MarkAsCancelled:
        await this.markCancelled(payload);
        break;
      case eGridRowActions.MarkAsCompleted:
      case eGridRowActions.UpdateCompletionDetails:
        this.isDeliveryPopupVisible = true;
        this.deliveryDetails = payload;
        break;
      case eGridRowActions.ViewIntegrationLog:
        this.showIntegrationLog(payload.deliveryUid);
        break;
    }
  }

  public onActionChange(uid: string): void {
    this.gridService.refreshGrid(eGridEvents.Table, gridName);
    this.actionChange.emit({ deliveryUid: uid, action: 'Confirmed' });
  }

  public onClosePoJobAttachmentsPopup(): void {
    this.isPoJobAttachmentsPopupVisible = false;
    this.gridService.refreshGrid(eGridEvents.Table, gridName);
  }

  public onCloseDescriptionPopup(): void {
    this.popupDescription = undefined;
  }

  public onCloseDeliveryDetailsPopup(): void {
    this.isDeliveryPopupVisible = false;
  }

  public onSaveDeliveryDetailsPopup(): void {
    this.isDeliveryPopupVisible = false;
    this.gridService.refreshGrid(eGridEvents.Table, gridName);
  }

  public openAttachmentsPopup(rowData: PoJobDto): void {
    this.isPoJobAttachmentsPopupVisible = true;
    this.currentPoJobUid = rowData.uid;
  }

  public openDescriptionPopup({ description }: PoJobDto): void {
    this.popupDescription = description;
  }

  public showIntegrationLog(deliveryUid: string): void {
    this.viewIntegrationLogObject = {
      moduleCode: 'Procurement',
      functionCode: 'prc_delivery',
      entityUid: deliveryUid,
    };
    this.cdr.detectChanges();
    this.showIntegrationLogSubject.next(true);
  }

  private async markCancelled(payload: PoJobDto): Promise<void> {
    const confirmed = await this.modalDialogService.openDialog({
      jbDialog: {
        dialogHeader: eGridRowActions.MarkAsCancelled,
        closableIcon: true,
      },
      text: ePrcWarnMessages.MarkPOJobAsCancelled,
    });
    if (!confirmed) {
      return;
    }
    try {
      const result = payload.deliveryUid
        ? await this.poDeliveryService
            .actionServiceDelivery(payload.deliveryUid, 'cancel')
            .toPromise()
        : await this.poDeliveryService
            .cancelPendingServiceDelivery(this.poUid, {
              jobUid: payload.uid,
            })
            .toPromise();

      this.gridService.refreshGrid(eGridEvents.Table, gridName);
      this.notificationService.success(
        ePrcSuccessMessages.POMarkJobCancelledSuccess
      );
      if (result) {
        this.actionChange.emit({
          deliveryUid: payload.deliveryUid,
          action: 'Confirmed',
        });
      }
    } catch ({ error, message }) {
      this.notificationService.error(error?.message ?? message);
    }
  }

  private async markPending(payload: PoJobDto): Promise<void> {
    const confirmed = await this.modalDialogService.openDialog({
      jbDialog: {
        dialogHeader: eGridRowActions.MarkAsPending,
        closableIcon: true,
      },
      text: ePrcWarnMessages.MarkPOJobAsPending,
    });
    if (!confirmed) {
      return;
    }

    try {
      const result = await this.poDeliveryService
        .actionServiceDelivery(payload.deliveryUid, 'unconfirm')
        .toPromise();
      this.gridService.refreshGrid(eGridEvents.Table, gridName);
      this.notificationService.success(
        ePrcSuccessMessages.POMarkJobPendingSuccess
      );
      if (result) {
        this.actionChange.emit({
          deliveryUid: payload.deliveryUid,
          action: 'Unconfirmed',
        });
      }
    } catch ({ error, message }) {
      this.notificationService.error(error?.message ?? message);
    }
  }

  private async loadMappingData(): Promise<void> {
    const [servicePerformanceResponse, portResponse] = await Promise.all([
      this.jcdsService.getData('servicePerformance'),
      this.poService.getPortsData(),
    ]);
    this.servicePerformances = servicePerformanceResponse.reduce(
      (acc, item) => {
        acc[item.uid] = item.display_names;
        return acc;
      },
      {}
    );

    this.ports = portResponse.reduce((acc, port) => {
      acc[port.PORT_ID] = `${port.port_name} | ${port.port_country}`;
      return acc;
    }, {});
    this.cdr.markForCheck();
  }

  public getNavigationUrl(rowData: Job): string {
    return this.extNavigationService.transformUrl(
      generateNavigationUrl(rowData)
    );
  }
}
