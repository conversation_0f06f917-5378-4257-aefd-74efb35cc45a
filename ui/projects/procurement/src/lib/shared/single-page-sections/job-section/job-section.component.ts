import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  Optional,
  Self,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { NgControl } from '@angular/forms';
import { UpdateJobDto } from '@j3-procurement/dtos/item-list';
import {
  eGridEvents,
  GridAction,
  GridService,
  GridShareDataService,
  WebApiRequest,
} from 'jibe-components';

import { ExtNavigationService, UnsubscribeComponent } from 'j3-prc-components';
import { Observable } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { RequisitionPermission } from '../../../models/enums/requisition-permission.enum';
import {
  GridInputsWithDataObject,
  GridInputsWithRequest,
} from '../../../models/interfaces/grid-inputs';
import { PermissionService } from '../../../services/permission/permission.service';
import { generateNavigationUrl } from '../../../utils/generate-url';
import { BaseSectionService } from '../base-section/base-section.service';
import { RowChangesService } from '../base-section/row-changes.service';
import { DescriptionPopupData } from './description-popup/types';
import { eGridRowActions } from './grid-constants';
import {
  additionalColumns,
  columns,
  getActions,
  gridName,
  searchFields,
} from './job-section-grid-inputs';
import { ColumnKey, Job, JobActionPermissionMap, JobEvent } from './types';

@Component({
  selector: 'prc-job-section',
  templateUrl: './job-section.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./job-section.component.scss'],
  providers: [GridShareDataService, BaseSectionService, RowChangesService],
})
export class JobSectionComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() event$: Observable<JobEvent>;
  @Input() getJobsRequest: WebApiRequest;
  @Input() isEditMode: boolean;
  @Input() jobPermissions: JobActionPermissionMap;

  @ViewChild('descriptionTemplate', { static: true })
  descriptionTemplate: TemplateRef<HTMLElement>;

  @ViewChild('codeTemplate', { static: true })
  codeTemplate: TemplateRef<HTMLElement>;

  public descriptionPopupData: DescriptionPopupData;
  public permission = RequisitionPermission;
  public gridInputs: GridInputsWithRequest<ColumnKey> &
    GridInputsWithDataObject<ColumnKey, Job>;

  private removedItemUids = new Set<string>();

  constructor(
    @Optional() @Self() private ngControl: NgControl,
    private readonly baseSectionService: BaseSectionService,
    private readonly cdr: ChangeDetectorRef,
    private readonly gridService: GridService,
    private readonly permissionService: PermissionService,
    private readonly rowChangesService: RowChangesService,
    private extNavigationService: ExtNavigationService
  ) {
    super();
    this.baseSectionService.ngControl = ngControl;
  }

  ngOnInit(): void {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      description: this.descriptionTemplate,
      code: this.codeTemplate,
    };

    this.gridInputs = {
      gridName,
      actions: getActions(),
      columns: [...columns, ...additionalColumns].map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      request: this.getJobsRequest,
    };

    this.ngControl.control.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((v) => !v)
      )
      .subscribe((_) => this.rowChangesService.resetChanges());

    this.toggleGridActions();

    if (this.event$) {
      this.event$
        .pipe(
          takeUntil(this.componentDestroyed$),
          filter(({ type }) => type === 'refreshGrid')
        )
        .subscribe(() => {
          this.gridInputs.request.body = undefined;
          this.gridService.refreshGrid(eGridEvents.Table, gridName);
        });
    }
  }

  private hideRemovedItems(): void {
    this.gridInputs = {
      ...this.gridInputs,
      data: this.baseSectionService.filterRemovedRows(this.removedItemUids),
      request: {
        ...this.gridInputs.request,
        body: { uidsToExclude: [...this.removedItemUids.values()] },
      },
    };
  }

  public onCloseDescriptionPopup(description: string): void {
    if (description !== undefined) {
      const { catalogUid, jobUid, uid } = this.descriptionPopupData;

      const { count, records } = this.baseSectionService.getMatrixData<Job>();

      this.gridInputs = {
        ...this.gridInputs,
        data: {
          count,
          records: records.map((record) =>
            record.jobUid === jobUid && record.catalogUid === catalogUid
              ? { ...record, description }
              : record
          ),
        },
      };

      this.baseSectionService.updateRowData(uid, {
        catalogUid,
        description,
        jobUid,
      });
    }
    this.descriptionPopupData = undefined;
  }

  public onGridAction({
    payload,
    type,
  }: GridAction<eGridRowActions, Job>): void {
    const { catalogUid, jobUid, uid } = payload;
    const changes: Partial<UpdateJobDto> = {};

    if (type === eGridRowActions.RemoveService) {
      this.removedItemUids.add(uid);
      changes.activeStatus = false;
      this.hideRemovedItems();
    }

    this.baseSectionService.updateRowData(uid, {
      ...changes,
      catalogUid,
      jobUid,
    });
  }

  public openDescriptionPopup(rowData: Job): void {
    const { jobUid, catalogUid, uid } = rowData;

    this.descriptionPopupData = {
      catalogUid,
      description: rowData.description,
      jobUid,
      uid,
    };
  }

  private async toggleGridActions(): Promise<void> {
    const canDeleteJobs = await this.permissionService.hasPermissions(
      this.jobPermissions?.deleteJobs
    );
    this.gridInputs.actions = getActions(!canDeleteJobs);

    this.cdr.markForCheck();
  }

  public getNavigationUrl(rowData: Job): string {
    return this.extNavigationService.transformUrl(
      generateNavigationUrl(rowData)
    );
  }
}
