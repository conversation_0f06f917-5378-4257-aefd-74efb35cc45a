import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { AccountDto } from '@j3-prc-catalog/dtos/account-mapping';
import { CatalogAccountDto } from '@j3-prc-catalog/dtos/catalog';
import { WebApiRequest } from 'jibe-components';
import { RequisitionService } from '../../services/requisition/requisition.service';
import { PurchaseRequestType } from './finance-item-configuration/types';
@Component({
  selector: 'prc-requisition-finance',
  templateUrl: './requisition-finance.component.html',
  styleUrls: ['./requisition-finance.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequisitionFinanceComponent implements OnInit {
  @Input() additionalChargeAccountsMap: Map<string, AccountDto[]>;
  @Input() additionalChargesRequest: WebApiRequest;
  @Input() bulkPurchase: boolean;
  @Input() catalogGLAccounts: Record<string, CatalogAccountDto[]>;
  @Input() deliveryDate: Date;
  @Input() financeItemsRequest: WebApiRequest;
  @Input() isBudgetReadOnly: boolean;
  @Input() isPOIssued: boolean;
  @Input() objectUid: string;
  @Input() prcType: PurchaseRequestType;
  @Input() readonly: boolean;
  @Input() vesselUid: string;

  @Output() sendAdditionalChargesData = new EventEmitter();
  @Output() sendItemConfigurationData = new EventEmitter();
  constructor(private readonly requisitionService: RequisitionService) {}

  getItemConfigurationData(event): void {
    this.sendItemConfigurationData.emit(event);
  }
  getAdditionalChargesData(event): void {
    this.sendAdditionalChargesData.emit(event);
  }

  async ngOnInit(): Promise<void> {
    const { pageState$ } = this.requisitionService;
    const { additionalCharges } = pageState$.value;
    if (!additionalCharges?.length) {
      return;
    }
  }
}
