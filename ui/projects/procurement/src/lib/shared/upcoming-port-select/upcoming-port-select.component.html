<input
  class="jb-text"
  [class.no-background]="!isBackground"
  type="text"
  pInputText
  [disabled]="disabled"
  [ngModel]="portDisplayName"
  (click)="clickInput()"
/>

<jb-dialog
  [dialogContent]="dialogContent"
  [dialogVisible]="dialogOpen"
  (dialogVisibleChange)="handleDialogClose()"
>
  <div jb-dialog-body *ngIf="dialogOpen">
    <div class="table-container">
      <jb-grid
        selectionMode="single"
        [colData]="gridInputs.columns"
        [filterData]="gridInputs.filters"
        [filterListsSet]="gridInputs.filtersLists"
        [getStyleByContainer]="true"
        [gridName]="gridInputs.gridName"
        [isDisplayAdvancedFilter]="false"
        [isDisplayAdvancedSettings]="false"
        [searchFields]="gridInputs.searchFields"
        [searchFields]="gridInputs.searchFields"
        [tableDataReq]="gridInputs.request"
        (matrixSelection)="gridAction($event)"
      ></jb-grid>
    </div>
  </div>
  <div class="footer" jb-dialog-footer>
    <jb-button
      [label]="cancelText"
      (click)="handleDialogClose()"
      type="NoButton"
    ></jb-button>
    <jb-button
      class="confirm-button"
      [label]="confirmText"
      (click)="handleDialogConfirm()"
    ></jb-button>
  </div>
</jb-dialog>
