<div class="right-pane-header">
  <button class="close-button" (click)="closeRightPanePopup()">
    <i class="pi pi-times"></i>
  </button>
  <div class="vessel">
    <i class="icons8-water-transportation"></i>
    <div>{{ headerDetails?.vessel }}</div>
  </div>
  <div class="title">
    {{ headerDetails?.title }}
    <a
      class="link"
      [prcNavigationLink]="headerDetails?.link"
      [queryParams]="{ tab_title: headerDetails?.title }"
      ><i class="icons8-external-link"></i
    ></a>
  </div>

  <div
    class="segments text-ellipsis"
    [prcTooltip]="headerDetails?.segments?.join(', ')"
  >
    {{ headerDetails?.segments?.join(", ") }}
  </div>

  <prc-right-pane-divider class="divider"></prc-right-pane-divider>

  <div class="bottom-section">
    <div class="status">
      <div class="status-dot"></div>
      <div>{{ headerDetails?.status }}</div>
    </div>

    <div class="icons">
      <img
        *ngIf="headerDetails?.icons?.criticality"
        alt="critical-icon"
        class="icons8-copyright critical-icon"
        pTooltip="Critical"
        src="assets/images/criticality-white.svg"
      />

      <img
        *ngIf="headerDetails?.icons?.dg"
        alt="dg-icon"
        class="icons8-falling-objects-hazard warning-icon"
        pTooltip="Dangerous Goods (DG)"
        src="assets/images/dg-white.svg"
      />

      <img
        *ngIf="headerDetails?.icons?.ihm"
        alt="ihm-icon"
        class="icons8-high-priority-3 info-icon"
        pTooltip="Inventory of Hazardous Material (IHM)"
        src="assets/images/recycle-white.svg"
      />

      <img
        *ngIf="headerDetails?.icons?.directPo"
        alt="direct-po-icon"
        pTooltip="No Quotation Requisition"
        src="assets/images/questionnaire-new.svg"
      />

      <img
        *ngIf="headerDetails?.icons?.exportControl"
        alt="export-control-icon"
        pTooltip="Export Control"
        src="assets/images/export-control.svg"
      />
    </div>
  </div>
</div>
