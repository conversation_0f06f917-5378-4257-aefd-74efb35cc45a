import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { stringsEqual } from '@j3-procurement/dtos';
import { IdLabel, Label } from '@j3-procurement/dtos/label';
import { CreateRequisitionDto } from '@j3-procurement/dtos/requisition';
import {
  localToUTC,
  ModalDialogService,
  NotificationService,
  SimpleChangesTyped,
  transformDateFormat,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { TypedFormGroup } from 'j3-prc-components';
import {
  CentralizedDataService,
  eDateFormats,
  IJbDialog,
  IJbTextArea,
  IMultiSelectDropdown,
  ISingleSelectDropdown,
  JbControlOutputService,
} from 'jibe-components';
import { takeUntil } from 'rxjs/operators';

import { AT_LEAST_ONE_CHARACTER } from '../../models/constants';
import {
  ePrcConfirmLabel,
  ePrcErrorMessages,
  ePrcModalMessages,
} from '../../models/enums/prc-messages.enum';
import { ItemListService } from '../../services/item-list/item-list.service';
import { PoTypeService } from '../../services/po/po-type.service';
import { PrcSharedService } from '../../services/prc-shared.service';
import { VesselService } from '../../services/vessel/vessel.service';
import { GenericPort } from '../../shared/generic-port-select/generic-port';
import { UpcomingPort } from '../../shared/upcoming-port-select/upcoming-port';
import { mapToLabel } from '../../utils/label-utils';
import { CREATE_REQUISITION, jcdsPropMap } from './constants';
import { getItemListDropdown, urgencyDropdown } from './dropdowns';
import { CreateRequisitionForm, CreateRequisitionPopupData } from './types';

@Component({
  selector: 'prc-create-requisition-popup',
  templateUrl: './create-requisition-popup.component.html',
  styleUrls: ['./create-requisition-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreateRequisitionPopupComponent
  extends UnsubscribeComponent
  implements OnInit, OnChanges
{
  @Input() data: CreateRequisitionPopupData;
  @Input() dialogHeader: string;

  @Output() close = new EventEmitter<CreateRequisitionDto>();

  public dialogContent: IJbDialog = {
    dialogHeader: CREATE_REQUISITION,
    dialogWidth: 920,
    showHeader: true,
    closableIcon: true,
  };
  public form: TypedFormGroup<CreateRequisitionForm>;
  public genericPort: GenericPort;
  public isDirectPO = false;
  public itemListDropdown = getItemListDropdown();
  public poTypeDropdown: ISingleSelectDropdown;
  public requisitionCreatedDialogOpen = false;
  public requisitionNumber: string;
  public requisitionUid: string;
  public subjectTextArea: IJbTextArea = {
    placeholder: 'Subject',
    id: 'description',
    maxlength: 200,
    rows: 6,
    tooltipValue: 'Text Range: Minimum: 1 Maximum: 200',
  };
  public urgencyDropdown = urgencyDropdown;
  public userDateControl: string;
  public vesselDepartment: IdLabel;
  public vesselId: number;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly cds: CentralizedDataService,
    private readonly fb: FormBuilder,
    private readonly itemListService: ItemListService,
    private readonly jbControlService: JbControlOutputService,
    private readonly modalDialogService: ModalDialogService,
    private readonly notificationService: NotificationService,
    private readonly prcSharedService: PrcSharedService,
    private readonly vesselService: VesselService,
    private readonly poTypeService: PoTypeService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const { urgencyUid, description, deliveryPortId, deliveryDate, poTypeUid } =
      this.data ?? {};
    this.form = this.fb.group<CreateRequisitionForm>({
      poType: [poTypeUid ? { uid: poTypeUid } : null, Validators.required],
      urgency: [urgencyUid ? { uid: urgencyUid } : null, Validators.required],
      itemListUids: [[]],
      deliveryPort: [deliveryPortId, Validators.required],
      deliveryUpcomingPort: [],
      deliveryDate: [
        deliveryDate && new Date(deliveryDate),
        Validators.required,
      ],
      description: [
        description,
        [
          Validators.required,
          Validators.maxLength(200),
          Validators.pattern(AT_LEAST_ONE_CHARACTER),
        ],
      ],
    });

    const { Date_Format: userDate } = this.cds.userDetails ?? {
      Date_Format: eDateFormats.DefaultFormat,
    };
    this.userDateControl = transformDateFormat(userDate);
    this.setPoTypeDropdown(this.data.vesselUid);

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ id, dataSource, selectedValue }) => {
        const controlName = id?.split('-')[0];
        const propName = jcdsPropMap.get(controlName);
        if (!propName) {
          return;
        }
        const label: Label = mapToLabel(dataSource, selectedValue, propName);
        const ctrl = this.form.get(controlName);
        ctrl.patchValue(label);
        ctrl.markAsDirty();
      });
  }

  ngOnChanges({ data, dialogHeader }: SimpleChangesTyped<this>): void {
    if (data) {
      const { itemListUid, urgencyUid, vesselUid, poTypeUid } = this.data;
      this.updateUrgencyDropdown({ selectedValue: urgencyUid });
      this.updatePoTypeDropdown({ selectedValue: poTypeUid });

      if (itemListUid) {
        this.itemListService
          .getItemLists(vesselUid)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((lists) =>
            this.updateItemListDropdown({
              dataSource: lists.filter(
                itemListUid
                  ? (l) => l.itemCount && !stringsEqual(l.uid, itemListUid)
                  : (l) => l.itemCount
              ),
            })
          );
      }

      this.setVesselId(vesselUid);
    }

    if (dialogHeader) {
      this.dialogContent.dialogHeader = this.dialogHeader ?? CREATE_REQUISITION;
    }
  }

  public onDeliveryPortChange(event: GenericPort): void {
    if (!this.genericPort || this.genericPort.Port_ID !== event?.PORT_ID) {
      this.form.get('deliveryUpcomingPort').reset();
      const port: GenericPort = {
        PORT_ID: event?.PORT_ID,
        port_country: event?.port_country,
        port_name: event?.port_name,
        UN_LOCODE: event?.UN_LOCODE,
      };
      this.genericPort = port;
    }
  }

  public onUpcomingPortChange(event: UpcomingPort): void {
    if (!event) {
      return;
    }
    const port: GenericPort = {
      PORT_ID: event.Port_ID,
      port_country: event.country,
      port_name: event.Port_Name,
      UN_LOCODE: event.UN_LOCODE,
    };
    this.genericPort = port;

    if (event.arrival) {
      this.form.get('deliveryDate').setValue(new Date(event.arrival));
    }
    this.form
      .get('deliveryPort')
      .setValue(port.PORT_ID, { emitViewToModelChange: false });
  }

  public async onSave(): Promise<void> {
    if (!this.form.valid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }

    const isConfirmed = await this.modalDialogService.openDialog({
      confirmButtonLabel: ePrcConfirmLabel.Confirm,
      text: ePrcModalMessages.CreateRequisitionPopup,
      jbDialog: {
        dialogHeader: ePrcModalMessages.CreateRequisition,
      },
    });

    if (!isConfirmed) {
      return;
    }

    const { deliveryPort, deliveryUpcomingPort, deliveryDate, ...rest } =
      this.form.value;
    const { itemListUid, vesselUid } = this.data;
    const [vesselDetails, { clientUid }] = await Promise.all([
      this.vesselService.getVesselDetailsByUid(vesselUid).toPromise(),
      this.prcSharedService.getClientUid().toPromise(),
    ]);

    const dto: CreateRequisitionDto = {
      ...rest,
      deliveryPort: {
        id: deliveryPort,
        country: this.genericPort?.port_country,
        name: this.genericPort?.port_name,
        code: this.genericPort?.UN_LOCODE,
      },
      department: this.vesselDepartment,
      vessel: {
        uid: vesselUid,
        name: vesselDetails.vesselName,
        imoNo: vesselDetails.imoNo,
        managementCompanyCode: vesselDetails.managementCompanyCode,
        managementCompanyName: vesselDetails.managementCompanyName,
        shortName: vesselDetails.shortName,
        vesselClass: vesselDetails.vesselClass,
        vesselHullNo: vesselDetails.vesselHullNo,
        vesselId: vesselDetails.vesselId,
        vesselManagerEmail: vesselDetails.vesselManagerEmail,
        vesselManagerName: vesselDetails.vesselManagerName,
        vesselManagerPhoneNo: vesselDetails.vesselManagerPhoneNo,
        vesselName: vesselDetails.vesselName,
        vesselOwnerCode: vesselDetails.vesselOwnerCode,
        vesselOwnerName: vesselDetails.vesselOwnerName,
        yardNumber: vesselDetails.yardNumber,
      },
      deliveryDate: localToUTC(deliveryDate),
      itemListUids: [itemListUid, ...this.itemListDropdown.selectedValues],
      clientUid,
    };

    this.close.emit(dto);
  }

  private async setPoTypeDropdown(vesselUid: string): Promise<void> {
    const newPoTypeDropdown = await this.poTypeService.getPoTypeDropdown(
      vesselUid
    );

    this.poTypeDropdown = { ...this.poTypeDropdown, ...newPoTypeDropdown };
    this.cdr.markForCheck();
  }

  /** TODO: remove this once the OperationsApi starts returning upcoming-ports using vesselUid */
  private setVesselId(vesselUid: string): void {
    const vessel = this.vesselService.getVesselByUid(vesselUid);
    this.vesselId = vessel?.vesselId;
  }

  private updateItemListDropdown(config: Partial<IMultiSelectDropdown>): void {
    this.itemListDropdown = {
      ...this.itemListDropdown,
      ...config,
    };
  }

  private updatePoTypeDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.poTypeDropdown = {
      ...this.poTypeDropdown,
      ...config,
    };
  }

  private updateUrgencyDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.urgencyDropdown = {
      ...this.urgencyDropdown,
      ...config,
    };
  }
}
