import { Injectable } from '@angular/core';
import { supplant } from '@j3-procurement/dtos';
import {
  DirectPoDetailsDto,
  DirectPOItemDto,
  SaveDirectPoDto,
  SaveDirectPoResponseDto,
} from '@j3-procurement/dtos/direct-po';
import { ApiRequestService, eCrud, WebApiRequest } from 'jibe-components';
import { BehaviorSubject, Observable } from 'rxjs';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';
@Injectable({
  providedIn: 'root',
})
export class DirectPOService {
  private readonlySubject = new BehaviorSubject<boolean>(false);
  public isReadonly$ = this.readonlySubject.asObservable();
  get isReadonly(): boolean {
    return this.readonlySubject.value;
  }

  set isReadonly(value: boolean) {
    this.readonlySubject.next(value);
  }

  constructor(private apiRequestService: ApiRequestService) {}

  /**
   * @description: Used to get direct po details records
   **/
  getDirectPODetailsMatrix(
    requisitionUid: string
  ): Observable<DirectPoDetailsDto> {
    const directPODetailstMatrixReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/direct-po`,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(directPODetailstMatrixReq);
  }

  getDirectPOItemsMatrix(quotationUid: string): Observable<DirectPOItemDto[]> {
    const directPOItemMatrixReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: `${quotationUid}/direct-po-items/list`,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(directPOItemMatrixReq);
  }

  getDirectPOJobsMatrixReq(quotationUid: string): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: `${quotationUid}/direct-po-jobs/list`,
      crud: eCrud.Post,
    };
  }

  saveDirectPODetails(
    requisitionUid: string,
    body: SaveDirectPoDto
  ): Observable<SaveDirectPoResponseDto> {
    const directPODetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/direct-po`,
      crud: eCrud.Post,
      body: body,
    };
    return this.apiRequestService.sendApiReq(directPODetails);
  }

  validateDirectPOItems(requisitionUid: string): Observable<boolean> {
    const directPODetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.GetDirectPOHasInvalidItems, {
        requisitionUid,
      }),
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(directPODetails);
  }
}
