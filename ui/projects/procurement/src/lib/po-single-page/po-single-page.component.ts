import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
  ViewChild,
} from '@angular/core';
import { PrcFunctionCodes, PrcModuleCodes } from '@j3-procurement/dtos';
import {
  DeliveryStatus,
  POAdditionalChargeDto,
  PoCancelResponseDto,
  PoDto,
  PoInfoDto,
  PoItemFinanceDto,
  UpdatePoDto,
} from '@j3-procurement/dtos/po';
import { WithSlf } from '@j3-procurement/dtos/slf';
import {
  AddCertificateComponent,
  NotificationService,
  TypedFormGroup,
  UnsubscribeComponent,
  utcToLocal,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  eAppLocation,
  eGridEvents,
  GridService,
  IJbAttachment,
  ITaskMangerDetails,
  JbAttachmentsComponent,
  JbDatePipe,
  WebApiRequest,
} from 'jibe-components';
import {
  BehaviorSubject,
  combineLatest,
  from,
  Observable,
  of,
  Subject,
} from 'rxjs';
import {
  catchError,
  filter,
  map,
  startWith,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';
import {
  ePrcErrorMessages,
  ePrcSuccessMessages,
} from '../models/enums/prc-messages.enum';
import {
  HeaderStatus,
  HeaderStatusColors,
} from '../models/interfaces/header-status';
import { ActionHook, WorkflowMenuContext } from '../workflow/services/types';
import { GET_REPORT_FILES_PARAMS, poButtonTooltip } from './constants';
import {
  defaultSectionEditMode,
  SectionEditMode,
  SectionKey,
} from './section-edit-mode';
import {
  DeliveryChange,
  ECStatusChangeEvent,
  PoDetailsForm,
  Section,
  View,
  ViewIntegrationLog,
} from './types';

import { FormBuilder } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { UpdateDeliveryInstructionDto } from '@j3-procurement/dtos/delivery-instruction';
import { EcStatus } from '@j3-procurement/dtos/export-control';
import { ObjectCountDto } from '@j3-procurement/dtos/object-count';
import { PoStatusBranchResponseDto } from '@j3-procurement/dtos/po-status-branch';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import { StateLabel } from '@j3-procurement/dtos/workflow';
import { IDocumentPreview } from 'jibe-components/lib/components/jb-document-preview/model/jb-document-preview.model';
import { MenuItem } from 'primeng';
import { ItemCertificatePermission } from '../models/enums/item-certificate-permission.enum';
import { PoPermission } from '../models/enums/po-permission.enum';
import { eApiResponseType } from '../models/enums/prc-api-response-type.enum';
import { PrcEnvironment } from '../models/enums/prc-environment.enum';
import { ePrcRequestEntity } from '../models/enums/prc-request-entity.enum';
import { ePrcWorklistType } from '../models/enums/prc-worklist-type.enum';
import { IDiscussionWorkflow } from '../models/interfaces/discussion.interface';
import { HistoryGraphKey } from '../models/interfaces/history-graph-key';
import { RouteConstants } from '../requisition-single-page/router-constants';
import { FeedDiscussionService } from '../services/feed-discussion.service';
import { FinanceService } from '../services/finance.service';
import { PrcHelpMaterialService } from '../services/help-button/prc-help-material.service';
import { InfraService } from '../services/infra.service';
import { PermissionService } from '../services/permission/permission.service';
import { PoDeliveryService } from '../services/po/po-delivery.service';
import { POItemsService } from '../services/po/po-items.service';
import { POService } from '../services/po/po.service';
import { SidebarMenuService } from '../services/sidebar-menu/sidebar-menu.service';
import { HeaderButton } from '../shared/generic-header/header-button';
import { HeaderSection } from '../shared/generic-header/header-section';
import { formatPort } from '../shared/pipes/format-port.pipe';
import { getHeaderSections } from '../shared/single-page-sub-header/utils';
import { WorkflowBtnService } from '../workflow/services/workflow-btn.service';
import { WorkflowService } from '../workflow/services/workflow.service';
import { CertificateEvent } from './po-item-certificates/types';
import { JobActionDetails } from './po-job/types';
import { sidebarMenu } from './sidebar-menu';

@Component({
  selector: 'prc-po-single-page',
  templateUrl: './po-single-page.component.html',
  styleUrls: ['./po-single-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [JbDatePipe, PrcHelpMaterialService, WorkflowBtnService],
})
export class PoSinglePageComponent
  extends UnsubscribeComponent
  implements OnInit
{
  constructor(
    fb: FormBuilder,
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly cds: CentralizedDataService,
    private readonly feedDiscussionService: FeedDiscussionService,
    private readonly financeService: FinanceService,
    private readonly gridService: GridService,
    private readonly helpMaterialService: PrcHelpMaterialService,
    private readonly infraService: InfraService,
    private readonly jbDate: JbDatePipe,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly poDeliveryService: PoDeliveryService,
    private readonly poItemsService: POItemsService,
    private readonly poService: POService,
    private readonly sidebarMenuService: SidebarMenuService<View, Section>,
    private readonly titleService: Title,
    private readonly workflowService: WorkflowService,
    private readonly workflowBtnService: WorkflowBtnService
  ) {
    super();
    this.poFormGroup = fb.group<PoDetailsForm>({
      approvalFlow: [],
      assignee: [],
      deliveryDate: [],
      deliveryPort: [{ value: null, disabled: true }],
      department: [{ value: null, disabled: true }],
      description: [],
      poType: [],
      titleNumber: [{ value: '', disabled: true }],
      urgency: [],
    });
  }
  private additionalChargesData: POAdditionalChargeDto[];
  private deliveryInstructionData: UpdateDeliveryInstructionDto;
  private itemConfigurationData: PoItemFinanceDto[];
  private poCertificateEventSubject = new Subject<CertificateEvent>();
  private timestamp: Date;

  private preWfActionMap = new Map<WorkflowType, ActionHook>([
    [
      'IN PROGRESS',
      (_, forceRework) => (forceRework ? undefined : this.raisePo()),
    ],
    ['CLOSE', (_, forceRework) => (forceRework ? undefined : this.closePo())],
  ]);
  private isSaving$ = new BehaviorSubject<boolean>(false);
  private showIntegrationLogSubject = new BehaviorSubject<boolean>(false);

  public attachmentConfig: IJbAttachment;
  public confirmedDeliveries: Set<string>;
  public currentView: View = 'purchase-order-details';
  public documentContent: IDocumentPreview;
  public feedDetail: IDiscussionWorkflow;
  public functionCode = PrcFunctionCodes.PoDetails;
  public hasConfirmedDelivery: boolean;
  public getJobsRequest: WebApiRequest;
  public poItemsRequest: WebApiRequest;
  public hasPoItems: boolean;
  public isCancelPoPopupVisible = false;
  public isDocumentPreview = false;
  public isPOIssued = false;
  public isShowRaisePurchaseOrder: boolean;
  public isUpdateDelivery: boolean;
  public isUpdateDeliveryVisible: boolean;
  public linkedRecordsDetails: ITaskMangerDetails;
  public moduleCode = PrcModuleCodes.Procurement;
  public readonly = false;

  public headerSections: HeaderSection[];
  public permission = PoPermission;
  public itemCertificatePermission = ItemCertificatePermission;
  public po: WithSlf<PoInfoDto>;
  public poFormGroup: TypedFormGroup<PoDetailsForm>;
  public poIssueUid: string;
  public poUid: string;
  public sectionEditMode: SectionEditMode = {
    ...defaultSectionEditMode,
    generalInformation: true,
  };
  public segmentUids: string[];
  public showIntegrationLog$ = this.showIntegrationLogSubject.asObservable();
  public statusGraphKeys: HistoryGraphKey;

  public poCertificateEvent$: Observable<CertificateEvent> =
    this.poCertificateEventSubject.asObservable();

  public headerButtons$: Observable<HeaderButton[]>;
  public headerStatus$: Observable<HeaderStatus>;
  public settingsOptions$: Observable<MenuItem[]>;
  public statusBranch$ = new BehaviorSubject<PoStatusBranchResponseDto>({
    lastActiveSeq: 0,
    list: [],
  });

  public viewIntegrationLog: ViewIntegrationLog;
  public canAddDelivery: boolean;
  public canAddEditAttachment: boolean;
  public canDeleteAttachment: boolean;
  public canEditDeliveryInstructionInfo: boolean;
  public canEditFinanceConfiguration: boolean;
  public canEditGeneralInfo: boolean;
  public canEditService: boolean;
  public canSlfEdit: boolean;
  public canUpdateOrderStatus: boolean;
  public canAddCertificate: boolean;

  @ViewChild('addCertificate', { static: false })
  public addCertificateDialog: AddCertificateComponent;
  @ViewChild('attachments', { static: false })
  public attachmentsDialog: JbAttachmentsComponent;

  additionalChargesRequest: WebApiRequest;
  financeItemsRequest: WebApiRequest;

  private saveBtn: HeaderButton = {
    buttonClass: 'save',
    buttonType: 'Standard',
    command: () => this.savePo(),
    label: 'Save',
  };

  private raisePoPopupCb = (_: boolean) => {};

  async ngOnInit(): Promise<void> {
    const { snapshot } = this.activatedRoute;
    this.poUid = snapshot.params[RouteConstants.poUid];

    this.setItemsJobsRequest(this.poUid);
    await this.loadPo(this.poUid);
    this.additionalChargesRequest =
      this.financeService.getAdditionalChargesRequest(
        this.poUid,
        ePrcRequestEntity.Po
      );
    this.financeItemsRequest = this.financeService.getFinancialItemsRequest(
      this.poUid,
      ePrcRequestEntity.Po
    );
    this.initWorkFlow();
    this.applyFormRestictions();
    this.loadSidebarMenu();

    if (this.poUid) {
      this.initializeAttachments(this.poUid);
    }
    this.headerSections = getHeaderSections(this.po.vessel?.name);
    this.setIsPoIssued();
    this.getStatusBranch();
    this.cdr.markForCheck();
    this.helpMaterialService.init(PrcFunctionCodes.PoDetails);
  }
  public onSaveCertificate(): void {
    this.poCertificateEventSubject.next({ refreshGrid: true });
  }

  private applyFormRestictions(): void {
    const fieldsToDisable: (keyof PoDetailsForm)[] = ['titleNumber'];
    if (!this.canEditGeneralInfo) {
      fieldsToDisable.push('description');
    }
    fieldsToDisable.forEach((field) => {
      this.poFormGroup.get(field)?.disable();
    });
  }

  public openAttachmentsDialog(): void {
    if (!this.readonly || this.canAddEditAttachment) {
      this.attachmentsDialog.dialogOnDemand();
    }
  }

  public async openAddCertificateDialog(): Promise<void> {
    if (!this.readonly && this.canAddCertificate) {
      this.addCertificateDialog.showAddCertificatePopup();
    }
  }

  public async savePo(): Promise<void> {
    this.isSaving$.next(true);

    try {
      const canEdit =
        this.canEditGeneralInfo ||
        this.canEditDeliveryInstructionInfo ||
        this.canEditFinanceConfiguration;

      if (this.readonly || !canEdit) {
        throw new Error(ePrcErrorMessages.RecordNotSavedSuccess);
      }
      const po: UpdatePoDto = {
        assignee: this.poFormGroup.value.assignee,
        description: this.poFormGroup.value.description,
        deliveryInstruction: this.deliveryInstructionData,
        poAdditionalCharges: this.additionalChargesData,
        poItemsConfiguration: this.itemConfigurationData,
        timestamp: this.timestamp,
      };
      const timestamp = await this.poService.updatePo(this.poUid, po);
      this.timestamp = new Date(timestamp);

      const deliveryPortDto = this.deliveryInstructionData?.deliveryPortDto;

      const deliveryDate =
        deliveryPortDto?.requestedDeliveryDate &&
        this.jbDate.transform(
          utcToLocal(deliveryPortDto.requestedDeliveryDate)
        );

      const deliveryPort = deliveryPortDto?.portName &&
        deliveryPortDto?.portCountry && {
          deliveryPort: formatPort(
            deliveryPortDto?.portName,
            deliveryPortDto?.portCountry
          ),
        };
      this.poFormGroup.patchValue({
        deliveryDate,
        ...deliveryPort,
      });

      this.notificationService.success(ePrcSuccessMessages.RecordSavedSuccess);
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message || err.message
      );
    } finally {
      this.isSaving$.next(false);
    }
  }

  private async buildCancelOption(state: StateLabel): Promise<MenuItem> {
    const CANCEL_ID = 'CANCEL';
    const { additional_steps } = state.configDetails ?? {};
    const showCancelOption = additional_steps?.some(
      (step) => step.workflow_action === CANCEL_ID
    );

    if (!showCancelOption) {
      return null;
    }

    const cancelOption: MenuItem = {
      command: () => (this.isCancelPoPopupVisible = true),
      label: 'Cancel',
    };

    const cancelAction = await this.workflowService
      .findAction(ePrcWorklistType.Po, CANCEL_ID)
      .toPromise();

    if (cancelAction) {
      const { name, rightCode } = cancelAction;
      const enabled =
        !rightCode || (await this.permissionService.hasPermissions(rightCode));

      cancelOption.disabled = !enabled;
      cancelOption.label = name;
    }

    return cancelOption;
  }

  private async buildHeaderMenuItems(state: StateLabel): Promise<MenuItem[]> {
    const menuItems: MenuItem[] = [];
    if (state?.isRework) {
      menuItems.push({
        command: () => this.workflowBtnService.reworkAction(),
        label: 'Rework',
      });
    }

    if (this.isPOIssued) {
      menuItems.push({ label: 'View PO', command: () => this.previewFile() });
    }

    if (!['RAISE', 'CLOSE', 'CANCEL'].includes(state.id)) {
      const enabled =
        (await this.permissionService.hasPermissions(
          PoPermission.UpdateDeliveryInstruction
        )) && this.canSlfEdit;
      menuItems.push({
        label: 'Update Delivery Instructions',
        disabled: !enabled,
        command: () => this.openIssuePoPopup(),
      });
    }

    const cancelItem = await this.buildCancelOption(state);
    if (cancelItem) {
      menuItems.push(cancelItem);
    }

    const { AppLocation } = this.cds.userDetails;
    if (AppLocation === eAppLocation.Office) {
      menuItems.push({
        label: 'View Integration Log',
        visible: true,
        command: () => {
          this.viewIntegrationLog = {
            moduleCode: PrcModuleCodes.Procurement,
            functionCode: PrcFunctionCodes.Po,
            entityUid: this.poUid,
          };
          this.showIntegrationLogSubject.next(true);
        },
      });
    }
    return menuItems;
  }

  private async getStatusBranch(): Promise<void> {
    const result = await this.poService
      .getPoStatusBranches(this.poUid)
      .pipe(takeUntil(this.componentDestroyed$))
      .toPromise();
    this.setStatusBranch(result);
  }

  /**
   * Initializes the workflow for the requisition by setting up the context and configuring pre and post workflow action maps.
   * It also updates the header buttons based on the current workflow state.
   */
  private initWorkFlow(): void {
    const context: WorkflowMenuContext = {
      objectUid: this.poUid,
      objectNumber: this.po.poNumber,
      vesselId: this.po.vesselId,
      vesselUid: this.po.vessel?.uid,
      wfList: ePrcWorklistType.Po,
    };
    this.workflowBtnService.initWorkflow({
      context,
      preRunActionMap: this.preWfActionMap,
    });
    this.setHeaderButtons();
  }

  private setHeaderButtons(): void {
    const canEdit =
      this.canEditGeneralInfo ||
      this.canEditDeliveryInstructionInfo ||
      this.canEditFinanceConfiguration;

    this.headerButtons$ = combineLatest([
      this.workflowBtnService.getWfButton(),
      this.isSaving$,
    ]).pipe(
      map(([wfButton, isSaving]) => {
        const saveDisabled = isSaving || this.readonly || !canEdit;
        const saveBtn = { ...this.saveBtn, disabled: saveDisabled };
        if (!wfButton) {
          return [saveBtn];
        }
        const actionId = wfButton.id as WorkflowType;
        const actionValid = this.actionValid(actionId);
        const disabled = wfButton.disabled || !actionValid;
        // customise default tooltip only when the action is invalid.
        const tooltip = actionValid
          ? wfButton.tooltip
          : poButtonTooltip.get(actionId);
        return [saveBtn, { ...wfButton, disabled, tooltip }];
      }),
      startWith([{ ...this.saveBtn, disabled: this.readonly }])
    );

    this.headerStatus$ = this.workflowBtnService.wfState$.pipe(
      filter((state) => Boolean(state)),
      tap((state) => this.updatePageMode(state?.id)),
      map(({ id, name }) => ({
        color: HeaderStatusColors.Purple,
        text: name ?? id,
      }))
    );

    this.settingsOptions$ = this.workflowBtnService.wfAction$.pipe(
      filter((actionRes) => Boolean(actionRes)),
      switchMap(({ state }) =>
        from(this.buildHeaderMenuItems(state)).pipe(catchError(() => of([])))
      )
    );
  }

  private setFeedDetailAndStatusGraph(): void {
    const entityEnvironment =
      this.po.poNumber?.slice(-1)[0] === 'S'
        ? PrcEnvironment.Vessel
        : PrcEnvironment.Office;

    this.feedDetail = this.feedDiscussionService.getDetails(
      this.poUid,
      this.po.vesselId,
      entityEnvironment,
      ePrcWorklistType.Po,
      this.po.poNumber
    );

    this.statusGraphKeys = {
      key1: this.poUid,
      key2: entityEnvironment.toString(),
      key3: this.po.vesselId?.toString(),
    };
  }

  private setLinkedRecordsDetails(): void {
    this.linkedRecordsDetails = {
      function_code: this.functionCode,
      module_code: this.moduleCode,
      uid: this.poUid,
      Vessel_ID: this.po.vesselId,
      Vessel_Name: this.po.vessel.name,
      vessel_uid: this.po.vesselUid,
      WL_TYPE: ePrcWorklistType.Po,
    };
  }

  private setStatusBranch(poStatusBranches: PoStatusBranchResponseDto): void {
    this.statusBranch$.next(poStatusBranches);
  }

  sendItemConfigurationData(event: PoItemFinanceDto[]): void {
    this.itemConfigurationData = event;
  }

  sendAdditionalChargesData(event: POAdditionalChargeDto[]): void {
    this.additionalChargesData = event;
  }

  sendDeliveryInstructionData(event: UpdateDeliveryInstructionDto): void {
    this.deliveryInstructionData = event;
  }

  /**
   * This function is triggered within the workflow to Close the purchase order (PO)
   */
  private async closePo(): Promise<void> {
    await this.poService.closePo(this.poUid).toPromise();
  }

  /**
   * This function is triggered within the workflow to Raise Purchase Order (PO)
   */
  private raisePo(): Promise<boolean> {
    this.openRaisePoPopup();
    return new Promise((ok) => (this.raisePoPopupCb = ok));
  }

  private async initializeAttachments(id: string): Promise<void> {
    this.attachmentConfig = {
      Module_Code: PrcModuleCodes.Procurement,
      Function_Code: PrcFunctionCodes.PoAttachments,
      Key1: id,
      // for some reason jb-components doesn't show actions by default, these are a copy from jb-attachment.json
      actions: [
        {
          name: 'Edit',
          icon: 'icons8-edit',
          disabled: this.readonly || !this.canAddEditAttachment,
        },
        {
          name: 'Delete',
          icon: 'icons8-delete',
          disabled: this.readonly || !this.canDeleteAttachment,
        },
        {
          name: 'Download',
          icon: 'icons8-download',
          disabled: this.readonly,
        },
      ],
    };
  }

  private loadSidebarMenu(): void {
    this.sidebarMenuService.init(sidebarMenu, this.componentDestroyed$);
    this.sidebarMenuService.selectedView$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((currentView: View) => {
        this.currentView = currentView;
        this.cdr.markForCheck();
      });
  }

  private setItemsJobsRequest(poUid: string): void {
    this.getJobsRequest = this.poService.getJobsRequest(poUid);
    this.poItemsRequest = this.poItemsService.getPoItemListReq(this.poUid);
  }

  public setSectionEditMode(key: SectionKey): void {
    this.sectionEditMode = { ...defaultSectionEditMode, [key]: true };
  }

  openIssuePoPopup(): void {
    this.isUpdateDelivery = true;
    this.openRaisePoPopup();
  }

  async closeCancelPo(event: PoCancelResponseDto): Promise<void> {
    this.isCancelPoPopupVisible = false;
    if (!event) {
      return;
    }
    this.setStatusBranch(event.poStatusBranch);
  }

  private openRaisePoPopup(): void {
    this.isShowRaisePurchaseOrder = true;
    this.cdr.markForCheck();
    this.poIssueUid = null; // reset the poIssueUid to null as delivery instruction is not detect changes with the same value.
  }

  public closeRaisePoPopup(po: Partial<PoDto>): void {
    this.isShowRaisePurchaseOrder = false;
    this.raisePoPopupCb?.(Boolean(po));
    this.raisePoPopupCb = null;
    this.poIssueUid = this.po.poIssueUid; // Reassign the poIssueUid to the original value
  }

  private async loadPo(poUid: string): Promise<void> {
    const [poInfo, confirmedDeliveries, hasPoItems] = await Promise.all([
      this.poService.getPoInformation(poUid).toPromise(),
      this.poDeliveryService.getConfirmedDeliveries(poUid).toPromise(),
      this.poItemsService.hasPoItems(this.poItemsRequest).toPromise(),
    ]);

    const [
      editGeneralInfoPermission,
      editDeliveryInstructionInfoPermission,
      updateOrderStatusPermission,
      editServicePermission,
      editFinanceConfigurationPermission,
      addEditAttachmentPermission,
      deleteAttachmentPermission,
      addDeliveryPermission,
      canAddCertificate,
    ] = await Promise.all([
      this.permissionService.hasPermissions(PoPermission.EditGeneralInfo),
      this.permissionService.hasPermissions(
        PoPermission.UpdateDeliveryInstruction
      ),
      this.permissionService.hasPermissions(PoPermission.UpdateOrderStatus),
      this.permissionService.hasPermissions(
        PoPermission.ChangeServiceLineStatus
      ),
      this.permissionService.hasPermissions(
        PoPermission.EditFinanceConfiguration
      ),
      this.permissionService.hasPermissions(PoPermission.AddEditAttachments),
      this.permissionService.hasPermissions(PoPermission.DeleteAttachments),
      this.permissionService.hasPermissions(PoPermission.AddDelivery),
      this.permissionService.hasPermissions(
        this.itemCertificatePermission.AddCertificate
      ),
    ]);

    const { segmentUids, timestamp } = poInfo;
    this.po = poInfo;
    this.hasPoItems = hasPoItems;
    this.confirmedDeliveries = new Set(confirmedDeliveries);
    this.hasConfirmedDelivery = this.confirmedDeliveries.size > 0;
    this.poIssueUid = this.po.poIssueUid;
    this.canSlfEdit = poInfo.slfPermission === 'edit';

    this.canEditGeneralInfo = this.canSlfEdit && editGeneralInfoPermission;
    this.canUpdateOrderStatus = this.canSlfEdit && updateOrderStatusPermission;
    this.canEditDeliveryInstructionInfo =
      this.canSlfEdit && editDeliveryInstructionInfoPermission;

    this.canEditService = this.canSlfEdit && editServicePermission;
    this.canEditFinanceConfiguration =
      this.canSlfEdit && editFinanceConfigurationPermission;

    this.canAddEditAttachment = this.canSlfEdit && addEditAttachmentPermission;
    this.canDeleteAttachment = this.canSlfEdit && deleteAttachmentPermission;

    this.canAddDelivery = this.canSlfEdit && addDeliveryPermission;

    this.canAddCertificate = canAddCertificate;

    this.patchFormValue();
    this.segmentUids = segmentUids;
    this.timestamp = timestamp;

    this.titleService.setTitle(this.po?.poNumber);
    this.setActionsVisibility();
    this.setFeedDetailAndStatusGraph();
    this.setLinkedRecordsDetails();
    this.cdr.markForCheck();
  }

  private setActionsVisibility(): void {
    if (this.po) {
      this.isUpdateDeliveryVisible = !['RAISE', 'CLOSE', 'CANCEL'].includes(
        this.po.statusId
      );
    }
  }

  public async onDeliveryChangeHandler(
    deliveryDetails: DeliveryChange
  ): Promise<void> {
    const { action, deliveryUid, poUid } = deliveryDetails;

    try {
      const deliveryResponse = await this.poDeliveryService
        .addDelivery(poUid, deliveryUid)
        .toPromise();

      this.gridService.refreshGrid(eGridEvents.Table, 'poitemGrid');
      this.setConfirmedDeliveries(deliveryResponse.deliveryUid, action);
      this.getStatusBranch();
      this.cdr.markForCheck();
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message
      );
    }
  }

  private setConfirmedDeliveries(
    deliveryUid: string,
    action: DeliveryStatus
  ): void {
    if (action === 'Confirmed' && !this.confirmedDeliveries.has(deliveryUid)) {
      this.confirmedDeliveries.add(deliveryUid);
    } else if (
      action === 'Unconfirmed' &&
      this.confirmedDeliveries.has(deliveryUid)
    ) {
      this.confirmedDeliveries.delete(deliveryUid);
    }
    this.hasConfirmedDelivery = this.confirmedDeliveries.size > 0;
  }

  public onJobActionChange({ action, deliveryUid }: JobActionDetails): void {
    this.setConfirmedDeliveries(deliveryUid, action);
    this.getStatusBranch();
  }

  public onEcStatusChanged({
    objectCount,
    invalidECItems,
  }: ECStatusChangeEvent): void {
    this.po.invalidECItems = invalidECItems;
    this.updateEcIconHeader(objectCount);
  }

  /**
   * Validates whether the given actionId can be performed.
   * @param actionId - The actionId to be validated.
   * @returns true if the action is valid, false otherwise.
   */
  private actionValid(actionId: WorkflowType): boolean {
    return actionId !== 'CLOSE' || this.po.invalidECItems === false;
  }

  private patchFormValue(): void {
    const { poNumber, deliveryDate, deliveryPort, ...poProps } = this.po;

    this.poFormGroup.patchValue({
      ...poProps,
      titleNumber: poNumber,
      ...(deliveryDate && {
        deliveryDate: this.jbDate.transform(utcToLocal(deliveryDate)),
      }),
      ...(deliveryPort && {
        deliveryPort: formatPort(deliveryPort.name, deliveryPort.country),
      }),
    });
  }

  private async previewFile(): Promise<void> {
    const { records } = await this.infraService.getFiles({
      ...GET_REPORT_FILES_PARAMS,
      key1: this.poUid,
    });
    if (!records?.length) {
      return;
    }
    const {
      upload_file_path: filePath,
      upload_file_name: fileName,
      file_type: fileType,
    } = records[0];

    this.documentContent = {
      FileName: fileName,
      FileType: fileType,
      FilePath: filePath,
      Width: '100%',
      Height: '900px',
    };

    this.isDocumentPreview = true;
    this.cdr.markForCheck();
  }

  private setIsPoIssued(): void {
    this.statusBranch$
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((branches) => branches?.list?.length > 0)
      )
      .subscribe((branches) => {
        const issuePOBranch = branches.list.find(
          ({ status }) => status === 'PO Issued'
        );
        this.isPOIssued = issuePOBranch?.isComplete ?? false;
      });
  }

  private updateEcIconHeader({
    ecBlockedCount,
    ecReleasedCount,
  }: Partial<ObjectCountDto>): void {
    let ecStatus: EcStatus;
    if (ecBlockedCount > 0) {
      ecStatus = 'blocked';
    } else if (ecReleasedCount > 0) {
      ecStatus = 'released';
    } else {
      ecStatus = 'notEC';
    }
    this.po = { ...this.po, ecStatus };
  }

  private updatePageMode(stateId: WorkflowType): void {
    if (['CANCEL', 'CLOSE'].includes(stateId)) {
      this.readonly = true;
      this.poFormGroup.disable();
      this.cdr.markForCheck();
    }
  }
}
