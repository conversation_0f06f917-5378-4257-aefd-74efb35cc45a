import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { IJbRating } from 'jibe-components';
import { QuotationRecord } from '../../quotation-summary/types';
import { ItemsSummary } from '../../types';
import { defalutCurrency, eCurrency } from '../../utils';

@Component({
  selector: 'prc-supplier-card-table-header',
  templateUrl: './supplier-card-table-header.component.html',
  styleUrls: ['./supplier-card-table-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SupplierCardTableHeaderComponent implements OnInit {
  constructor() {}
  @Input() quotation: QuotationRecord;
  @Input() warnings: string[];
  @Input() selectedItemInformation: ItemsSummary;
  @Input() currencyMode: eCurrency = defalutCurrency;
  ratingContent: IJbRating = {};

  ngOnInit(): void {
    this.ratingContent = {
      Stars: 5,
      CancelIcon: false,
      Readonly: true,
      LabelRating: this.quotation?.supplierRating?.toString(),
    };
  }
}
