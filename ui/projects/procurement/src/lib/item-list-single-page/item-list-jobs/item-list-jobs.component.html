<jb-grid
  [colData]="gridInputs.columns"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [searchFields]="gridInputs.searchFields"
  [setActions]="!ngControl?.disabled ? gridInputs.actions : undefined"
  [tableData]="gridInputs.data"
  [tableDataReq]="gridInputs.request"
  (action)="onGridAction($event)"
></jb-grid>

<ng-template #descriptionTemplate let-rowData>
  <div class="description-column">
    <ng-container *ngIf="rowData.description || ' ' | stripHtml as description">
      <span class="text-ellipsis" [prcTooltip]="description">
        {{ description }}
      </span>
    </ng-container>

    <img
      class="external-link-icon"
      [class.disabled]="!(permission.ListAddServices | hasPermissions)"
      src="assets/procurement/images/external-link.svg"
      (click)="openDescriptionPopup(rowData)"
    />
  </div>
</ng-template>

<prc-description-popup
  *ngIf="descriptionPopupData"
  [data]="descriptionPopupData"
  (close)="onCloseDescriptionPopup($event)"
></prc-description-popup>

<ng-template #codeTemplate let-rowData>
  <a class="jb_grid_topCellValue jb-link-600-14"
  target="_blank"
  [href]="getNavigationUrl(rowData)"
  >{{ rowData.code }}
</a>
</ng-template>  
