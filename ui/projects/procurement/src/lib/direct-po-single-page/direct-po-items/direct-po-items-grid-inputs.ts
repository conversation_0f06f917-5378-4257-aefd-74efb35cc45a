import {
  eColor,
  eFieldControlType,
  eGridColumnsWidth,
  eIconNames,
  GridRowActions,
} from 'jibe-components';

import { DirectPOItemDto } from '@j3-procurement/dtos/direct-po';
import { createColumns } from 'j3-prc-components';

export type ColumnKey = Extract<
  keyof DirectPOItemDto,
  | 'catalogPath'
  | 'runningNumber'
  | 'type'
  | 'itemName'
  | 'leadDays'
  | 'brandName'
  | 'orderQty'
  | 'unPrice'
  | 'discount'
  | 'amount'
  | 'dg'
  | 'ihm'
  | 'reqUomName'
  | 'model'
  | 'activeStatus'
  | 'rob'
>;

export const gridName = 'directPOItemsGrid';
export const columns = createColumns<string, ColumnKey>(
  [
    ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
    ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
    ['Item Name', 'itemName', { width: eGridColumnsWidth.LongDescription }],
    [
      'Item Type',
      'type',
      {
        ControlType: eFieldControlType.Dropdown,
        FieldType: eFieldControlType.Dropdown,
        width: eGridColumnsWidth.LongDescription,
        Editable: true,
        ChangeEditableState: false,
        ValidatorRequired: true,
        IsMandatory: true,
        EditTable: {
          editListSearch: 'label',
          editTableLabelKey: '',
          editTableLabelValue: '',
          editList: [
            { label: 'org', value: 'org' },
            { label: 'oem', value: 'oem' },
            { label: 'rep', value: 'rep' },
          ],
        },
      },
    ],
    ['Status', 'activeStatus'],
    ['Brand', 'brandName'],
    [
      'Offered Quantity',
      'orderQty',
      {
        ControlType: eFieldControlType.Number,
        KeyFilter: 'num',
        Precision: '1.4-4',
        Editable: true,
      },
    ],
    ['UOM', 'reqUomName'],
    [
      'Unit Price',
      'unPrice',
      {
        ControlType: eFieldControlType.Number,
        Precision: '1.2-2',
        Editable: true,
        ValidatorRequired: true,
        IsMandatory: true,
      },
    ],
    [
      'Discount',
      'discount',
      {
        ControlType: eFieldControlType.Number,
        Precision: '1.2-2',
        Editable: true,
        validMin: 0,
        validMax: 99.99,
        width: eGridColumnsWidth.LongNumber,
      },
    ],
    ['Total Amount', 'amount'],
    [
      'Lead Time',
      'leadDays',
      {
        ControlType: eFieldControlType.Number,
        Editable: true,
        KeyFilter: 'pint',
      },
    ],
  ],
  {
    IsMandatory: true,
  }
);
export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['DG', 'dg'],
    ['IHM', 'ihm'],
    ['Model', 'model'],
    ['ROB', 'rob', { DisableSort: true, width: eGridColumnsWidth.ShortNumber }],
  ],
  { IsVisible: false }
);

export enum eGridAction {
  Add = 'Add to PO',
  Remove = 'Remove from PO',
}

const getAaddGridAction = (hasAddEditItemAccess: boolean): GridRowActions => ({
  name: eGridAction.Add,
  icon: eIconNames.AddNew,
  color: eColor.JbBlack,
  disabled: !hasAddEditItemAccess,
});

const getRemoveGridAction = (
  hasAddEditItemAccess: boolean
): GridRowActions => ({
  name: eGridAction.Remove,
  icon: eIconNames.Cancel,
  color: eColor.JbRed,
  disabled: !hasAddEditItemAccess,
});

export const getActions = (hasAddEditItemAccess: boolean): GridRowActions[] => [
  {
    name: eGridAction.Add,
    icon: eIconNames.AddNew,
    color: eColor.JbBlack,
    actionFunction: (dto: DirectPOItemDto) =>
      dto.activeStatus
        ? [getRemoveGridAction(hasAddEditItemAccess)]
        : [getAaddGridAction(hasAddEditItemAccess)],
  },
];
