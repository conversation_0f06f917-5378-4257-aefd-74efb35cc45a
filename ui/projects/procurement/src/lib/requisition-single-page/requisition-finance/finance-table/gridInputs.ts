import { IExportAdditionalColumn } from 'jibe-components';

const hiddenExportColumns: IExportAdditionalColumn[] = [
  {
    DisplayText: 'Project',
    FieldName: 'projectUid',
    HideColumn: true,
  },
  {
    DisplayText: 'Analysis Code',
    FieldName: 'analysisCodeUid',
    HideColumn: true,
  },
  {
    DisplayText: 'Budget',
    FieldName: 'budgetUid',
    HideColumn: true,
  },
];

const extraExportColumns: IExportAdditionalColumn[] = [
  {
    DisplayText: 'Project',
    FieldName: 'project.name',
    HideColumn: false,
  },
  {
    DisplayText: 'Analysis Code',
    FieldName: 'analysisCode.name',
    HideColumn: false,
  },
  {
    DisplayText: 'Budget',
    FieldName: 'budget.name',
    HideColumn: false,
  },
];

export const exportExtraColumns = [
  ...hiddenExportColumns,
  ...extraExportColumns,
];
