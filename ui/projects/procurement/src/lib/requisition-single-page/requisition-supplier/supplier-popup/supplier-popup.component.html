<jb-dialog
  [dialogContent]="supplierDialogContent"
  [(dialogVisible)]="supplierDialog"
  (closeDialog)="closeAddSupplierDialog()"
>
  <ng-container jb-dialog-body>
    <form [formGroup]="supplierFormGroup" class="supplier-form">
      <div class="group">
        <prc-form-label [required]="true" label="Quotation Due Date">
          <div class="supplier-quotation-date">
            <p-calendar
              [showIcon]="true"
              formControlName="supplierQuotationDate"
              [monthNavigator]="true"
              placeholder="Select"
              [dateFormat]="userDateControl"
              showButtonBar="true"
              [required]="true"
              inputStyleClass="jb-text"
              (onSelect)="onQuotationDate($event)"
              (onClearClick)="supplierValidation()"
              (onInput)="supplierValidation()"
            >
            </p-calendar>
          </div>
        </prc-form-label>

        <prc-form-label label="Port Call">
          <div class="supplier-portcall jb-text">
            <prc-upcoming-port-select
              formControlName="portCall"
              [isBackground]="true"
              [vesselId]="vesselId"
              (selectedPortChange)="onUpcomingPortChange($event)"
            ></prc-upcoming-port-select>
          </div>
        </prc-form-label>

        <prc-form-label label="Delivery Port">
          <div class="supplier-delport jb-text">
            <prc-generic-port-select
              [isBackground]="true"
              formControlName="deliveryPortId"
              (selectedPortChange)="onDeliveryPortChange($event)"
            ></prc-generic-port-select>
          </div>
        </prc-form-label>

        <prc-form-label label="Delivery Date">
          <div class="supplier-delDate">
            <p-calendar
              [showIcon]="true"
              formControlName="supplierDeliveryDate"
              [monthNavigator]="true"
              placeholder="Select"
              [dateFormat]="userDateControl"
              showButtonBar="true"
              inputStyleClass="jb-text"
            >
            </p-calendar>
          </div>
        </prc-form-label>
      </div>

      <prc-form-label class="rfq-remarks" label="RFQ Remarks">
        <jb-textarea-wrapper
          [charCount]="charCount"
          [maxCharCount]="maxCharCount"
          *ngIf="rfqRemarkTxt"
        >
          <jb-textarea [autoResize]="false" [content]="rfqRemarkTxt">
          </jb-textarea>
        </jb-textarea-wrapper>
      </prc-form-label>
    </form>

    <div class="supplier-grid-container">
      <jb-grid
        class="supplier-grid"
        [advancedSettings]="gridInputs.advancedSettings"
        [colData]="gridInputs.columns"
        [filterData]="gridInputs.filters"
        [filterListsSet]="gridInputs.filtersLists"
        [getStyleByContainer]="true"
        [gridName]="gridInputs.gridName"
        [searchFields]="gridInputs.searchFields"
        [showSettings]="gridInputs.showSettings"
        [tableDataReq]="gridInputs.request"
        (cellChange)="onCellChange($event)"
      ></jb-grid>
      <ng-template #supplierRatingTemplate let-rowData>
        <jb-rating
          [ratingContent]="ratingOptions"
          [ratingData]="rowData.rating"
        >
        </jb-rating>
      </ng-template>
      <ng-template #contractCodeTemplate let-rowData>
        <i
          *ngIf="rowData.contractCode as contractCode"
          class="icons8-prize"
          [pTooltip]="contractCode"
        ></i>
      </ng-template>
    </div>
  </ng-container>

  <ng-container jb-dialog-footer>
    <jb-dialog-footer
      okBtnLabel="Done"
      cancelBtnLabel="Cancel"
      [isOkBtnDisabled]="validate"
      (ok)="addSupplierRecord()"
      (cancel)="closeAddSupplierDialog()"
    ></jb-dialog-footer>
  </ng-container>
</jb-dialog>
