import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';

import {
  applyActionsToQuotationItems,
  getQuotationItemSelection,
} from '@j3-procurement/dtos';
import {
  IhmDgGridFilter,
  OnItemReceivedFn,
  OnStateFoundFn,
  QuotationDto,
  QuotationEvaluationDetails,
  QuotationEvaluationItemDto,
  QuotationItemDetails,
  QuotationItemStateDto,
  QuotationItemStateMap,
  SelectFilterValue,
  ShowGridFilter,
} from '@j3-procurement/dtos/quotation';
import { eGridEvents, Filter, GridAction, GridService } from 'jibe-components';
import { LazyLoadEvent, Table } from 'primeng';
import { Observable } from 'rxjs';
import { startWith, takeUntil } from 'rxjs/operators';

import { WorkflowType } from '@j3-prc-shared/dtos/task-status';
import {
  ModalDialogService,
  SimpleChangesTyped,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { AppConfig, APP_CONFIG } from '../../app.config';
import {
  ePrcConfirmLabel,
  ePrcWarnMessages,
} from '../../models/enums/prc-messages.enum';
import { VirtualScrollConfig } from '../../models/interfaces/virtual-scroll-config';
import { ContractsService } from '../../services/contracts.service';
import { CurrencyPipe } from '../currency-pipe/currency.pipe';
import { QuotationEvaluationActionsService } from '../quotation-evaluation-actions/quotation-evaluation-actions.service';
import { getQuotationWarnings } from '../quotation-warning-utils';
import { QuotationService } from '../quotation.service';
import {
  ItemsSummary,
  QuotationItemsSummary,
  ReqItemOrderQty,
  RowStateChange,
} from '../types';
import { defalutCurrency, eCurrency } from '../utils';
import {
  FilterKey,
  filters,
  filtersLists,
  gridName,
  SortSupplierFilterValue,
} from './quotation-evaluation-grid-inputs';
import { QuotationItemRowFilled, RowChanges } from './supplier-table/types';
import { calculateAmount } from './supplier-table/utils';

@Component({
  selector: 'prc-quotation-evaluation',
  templateUrl: './quotation-evaluation.component.html',
  styleUrls: ['./quotation-evaluation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class QuotationEvaluationComponent
  extends UnsubscribeComponent
  implements OnInit, OnChanges
{
  @Input() bulkPurchase: boolean;
  @Input() quotationItemStateMap: QuotationItemStateMap;
  @Input() requisitionUid: string;
  @Input() selectedQuotations: QuotationDto[];
  @Input() vesselUid: string;
  @Input() status: WorkflowType;
  @Input() readonly: boolean;

  @Output() quotationItemStateMapChange =
    new EventEmitter<QuotationItemStateMap>();
  @Output() quotationEvaluationSummaryChange =
    new EventEmitter<QuotationItemsSummary>();
  @Output() itemDetailsChange = new EventEmitter<
    Record<string, QuotationItemDetails>
  >();

  @ViewChild('quotationEvaluationTable') quotationEvaluationTable: Table;

  public additionalFilters: Filter[];
  /**
   * TODO: Use virtualScrollConfig.bufferSize value instead.
   * We're temporarily setting it to 4000 to disable virtual scroll, as it was before.
   * Enabling virtual scroll may introduce new issues in the quotation evaluation section.
   * The first issue to fix is that the "select all" feature doesn't work correctly.
   */
  public bufferSize = 4000;
  public contractCodesMap: Record<string, string> = {};
  public currencyMode$: Observable<eCurrency>;
  public expandedRowKeys: Record<string, boolean> = {};
  public gridName = gridName;
  public isReqItemsTableInCollapsedMode = false;
  public quotationEvaluationItemCount: number;
  public quotationEvaluationItems: QuotationEvaluationItemDto[] = [];
  public quotationWarnings: Record<string, string[]> = {};
  public summaryFields: QuotationItemsSummary = {};
  public summaryReqItemsOrderQty: Record<string, number> = {};
  public virtualScrollConfig: VirtualScrollConfig;

  private gridFilters: (ShowGridFilter | IhmDgGridFilter)[] = [];
  private gridSearch: string;
  private hasPendingItems = true;
  private itemDetails: Record<string, QuotationItemDetails> = {};
  private orgCount: Record<string, number> = {};
  private remarksIsHidden = false;
  private quotationCurrencyRateMap: Map<string, number> = new Map();
  private selectedQuotationUids: string[];
  private requestId = 0;
  private selection: SelectFilterValue;
  private sortSupplierColumnsKey: SortSupplierFilterValue;
  private itemsOrderQtyAllQuotations: Map<string, ReqItemOrderQty> = new Map();

  constructor(
    @Inject(APP_CONFIG) config: AppConfig,
    private readonly actionsService: QuotationEvaluationActionsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly gridService: GridService,
    private readonly modalDialogService: ModalDialogService,
    private readonly quotationService: QuotationService,
    private readonly currency: CurrencyPipe,
    private readonly contractService: ContractsService
  ) {
    super();
    this.virtualScrollConfig = config.virtualScroll;
  }

  ngOnChanges({
    selectedQuotations,
    quotationItemStateMap,
  }: SimpleChangesTyped<this>): void {
    if (selectedQuotations || quotationItemStateMap) {
      this.quotationWarnings = {};

      if (quotationItemStateMap?.currentValue) {
        const summary = this.calculateSummaryFields(
          [...this.quotationItemStateMap.values()],
          this.itemDetails
        );
        this.setSummaryFields(summary);
      }

      this.selectedQuotationUids = [];
      this.quotationCurrencyRateMap = new Map();
      this.selectedQuotations.forEach((quote) => {
        this.selectedQuotationUids.push(quote.uid);
        this.quotationWarnings[quote.uid] = getQuotationWarnings(quote);
        this.quotationCurrencyRateMap.set(quote.uid, quote.currencyRate);
      });

      if (
        selectedQuotations?.currentValue &&
        !selectedQuotations?.firstChange
      ) {
        this.lazyLoad({ first: 0, rows: this.bufferSize });
      }
    }
  }

  ngOnInit(): void {
    this.additionalFilters = this.gridService.getFilterLists(
      filters,
      filtersLists
    );
    this.currencyMode$ = this.actionsService
      .listenActions(['currencyConverter'])[0]
      .action.pipe(startWith(defalutCurrency));

    this.gridService.storeState$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(
        ({
          gridName: name,
          payload,
          type,
        }: GridAction<eGridEvents, unknown>) => {
          if (name === gridName && type === eGridEvents.SearchTable) {
            this.onSearch(payload as string);
          }
        }
      );
  }

  public async lazyLoad(event: LazyLoadEvent): Promise<void> {
    const { first, rows } = event;
    if (first > this.quotationEvaluationItemCount) {
      return;
    }

    if (!first) {
      /**
       * When a new request comes in, we should cancel the previous one.
       * Note: When we scroll to the bottom for the first time, the virtual scroll in PrimeNG triggers
       * two events instead of one. The first request fetches rows starting from index 50,
       * and the second one starts from index 100. The requestId was added to handle this case properly.
       * */
      this.requestId++;
      this.quotationEvaluationTable.clearCache();
    }

    const quotationUids = this.selectedQuotations.map(({ uid }) => uid);
    const prevRequestId = this.requestId;
    const { records, count } =
      await this.quotationService.getQuotationEvaluationItems({
        gridFilters: this.gridFilters,
        gridSearch: this.gridSearch ? { value: this.gridSearch } : null,
        odata: {
          $skip: first.toString(),
          $top: rows.toString(),
        },
        quotationUids,
        requisitionUid: this.requisitionUid,
      });

    if (this.requestId !== prevRequestId) {
      return;
    }

    this.quotationEvaluationItemCount = count;
    let oldItems = [...this.quotationEvaluationItems];

    const contractUids = new Set<string>();
    records.forEach(({ quotationItems }) => {
      Object.values(quotationItems).forEach((item) => {
        if (item.contractUid) {
          contractUids.add(item.contractUid);
        }
      });
    });

    if (!first) {
      oldItems = new Array(count).fill({});
      this.quotationEvaluationTable.resetScrollTop();
    }

    this.hasPendingItems = this.quotationEvaluationItemCount > first + rows;

    oldItems.splice(first, rows, ...records);
    this.quotationEvaluationItems = oldItems;

    this.updateExpandedRowKeys();
    await this.loadDetails();
    await this.loadContractCodes([...contractUids]);

    this.cdr.markForCheck();
  }

  private async loadContractCodes(uids: string[]): Promise<void> {
    if (!uids?.length) {
      return;
    }

    const newUids = uids.filter((uid) => !this.contractCodesMap[uid]);
    if (newUids.length) {
      const contractCodes = await this.contractService.getContractCodes(
        newUids
      );

      const newMapEntries: Record<string, string> = {};
      contractCodes?.forEach(({ uid, contractCode }) => {
        newMapEntries[uid] = contractCode;
      });

      this.contractCodesMap = { ...this.contractCodesMap, ...newMapEntries };
    }
  }

  private updateExpandedRowKeys(): void {
    this.expandedRowKeys = this.quotationEvaluationItems.reduce(
      (accum, { uid }) => ({ ...accum, [uid]: !this.remarksIsHidden }),
      {}
    );
  }

  private updateGridFilters(filter: ShowGridFilter | IhmDgGridFilter): void {
    const filterIndex = this.gridFilters.findIndex(
      (f) => f.odataKey === filter.odataKey
    );
    if (filter.selectedValues?.length) {
      const index = filterIndex > -1 ? filterIndex : this.gridFilters.length;
      this.gridFilters[index] = filter;
    } else if (filterIndex > -1) {
      this.gridFilters.splice(filterIndex);
    }
  }

  public onFilterChange({
    odataKey,
    selectedValues,
  }: Omit<Filter, 'odataKey'> & {
    odataKey: FilterKey;
  }): void {
    switch (odataKey) {
      case 'hideRemarks':
        this.onHideRemarksChanged(selectedValues);
        break;
      case 'ihmDg':
      case 'show':
        this.updateGridFilters({ odataKey, selectedValues, type: '' });

        this.lazyLoad({
          first: 0,
          rows: this.bufferSize,
        });
        break;
      case 'sortSupplierCardBy':
        this.sortSupplierColumnsKey = selectedValues;
        this.sortSupplierColumnsBy(selectedValues);
        break;
      case 'select':
        this.selection = selectedValues;
        this.loadDetails(!this.selection);
        break;
    }
  }

  private getTotalAmountBySummary(summary: QuotationItemsSummary): number {
    return Object.values(summary).reduce(
      (accum, { amount }) => accum + amount,
      0
    );
  }

  private async checkForCheapestOption(): Promise<void> {
    if (
      this.selection === 'cheapestByItems' &&
      this.selectedQuotationUids.length > 1
    ) {
      const newDetails = await this.getDetails();
      const newSummary = this.calculateSummaryFields(
        newDetails.state,
        newDetails.itemDetails
      );
      const newTotatAmount = this.getTotalAmountBySummary(this.summaryFields);
      const currentTotalAmount = this.getTotalAmountBySummary(newSummary);
      if (newTotatAmount < currentTotalAmount) {
        this.showCheapestConfirmDialog(newDetails, newSummary);
      }
    }
  }

  private showCheapestConfirmDialog(
    details: QuotationEvaluationDetails,
    summary: QuotationItemsSummary
  ): void {
    this.modalDialogService
      .openDialog({
        jbDialog: {
          dialogHeader: ePrcConfirmLabel.Confirm,
        },
        text:
          Object.keys(summary).length > 1
            ? ePrcWarnMessages.CheapestSupplierAvailableLimited
            : ePrcWarnMessages.CheapestSupplierAvailable,
        confirmButtonLabel: ePrcConfirmLabel.SelectCheapestSupplier,
        rejectButtonLabel: ePrcConfirmLabel.KeepSelection,
      })
      .then((confirmed) => {
        if (confirmed) {
          this.setDetails(details);
          this.setSummaryFields(summary);
          this.cdr.markForCheck();
        }
        return confirmed;
      });
  }

  private async getDetails(
    resetSelection = false
  ): Promise<QuotationEvaluationDetails> {
    if (this.hasPendingItems) {
      const evaluationDetails = await this.quotationService
        .getQuotationEvaluationDetails({
          quotationUids: this.selectedQuotationUids,
          requisitionUid: this.requisitionUid,
          gridFilters: this.gridFilters,
          select: this.selection,
        })
        .toPromise();

      return evaluationDetails;
    } else {
      const itemDetails: Record<string, QuotationItemDetails> = {};
      const orgCount: Record<string, number> = {};

      const onStateFound: OnStateFoundFn = ({ uid, ...item }) => {
        const details = {
          discount: item.discount,
          leadDays: item.leadDays,
          unPrice: item.unPrice,
          uomQty: item.uomQty,
          uomConversion: item.uomConversion,
          reqItemUid: item.reqItemUid,
        };

        itemDetails[uid] = details;
      };

      const onItemReceived: OnItemReceivedFn = ({ quotationUid, types }) => {
        orgCount[quotationUid] =
          (types?.org?.qty ?? 0) + (orgCount[quotationUid] ?? 0);
      };

      let state: QuotationItemStateDto[];

      if (this.selection) {
        state = getQuotationItemSelection({
          condition: this.selection,
          quotationEvaluationItems: this.quotationEvaluationItems,
          selectedQuotations: this.selectedQuotations,
          onStateFound,
          onItemReceived,
          quotationCurrencyRateMap: this.quotationCurrencyRateMap,
        });
      } else {
        state = resetSelection ? [] : [...this.quotationItemStateMap.values()];
        applyActionsToQuotationItems(
          this.quotationEvaluationItems,
          state,
          onItemReceived,
          onStateFound,
          this.quotationCurrencyRateMap
        );
      }

      return {
        itemDetails,
        orgCount,
        state,
      };
    }
  }

  private async loadDetails(resetSelection = false): Promise<void> {
    if (!this.selectedQuotationUids?.length) {
      return;
    }

    const details = await this.getDetails(resetSelection);
    this.checkForCheapestOption();
    this.setDetails(details);
    this.initilizeReqItemOrderQty(details.state);

    const summary = this.calculateSummaryFields(
      [...this.quotationItemStateMap.values()],
      this.itemDetails
    );
    this.setSummaryFields(summary);
    this.cdr.markForCheck();
  }

  private setDetails({
    state,
    itemDetails,
    orgCount,
  }: QuotationEvaluationDetails): void {
    this.quotationItemStateMap = new Map(
      state.map((item) => [item.quotationItemUid, item])
    );
    this.itemDetails = { ...this.itemDetails, ...itemDetails };
    this.orgCount = orgCount;
    this.itemDetailsChange.emit(this.itemDetails);
    this.quotationItemStateMapChange.emit(this.quotationItemStateMap);
  }
  private initilizeReqItemOrderQty(state: QuotationItemStateDto[]): void {
    this.itemsOrderQtyAllQuotations = new Map(
      state.map(({ quotationItemUid, orderQty }) => [
        quotationItemUid,
        {
          orderQty,
          reqItemUid: this.itemDetails[quotationItemUid].reqItemUid,
        },
      ])
    );
    this.calculateSummaryReqItemsOrderQty([
      ...this.itemsOrderQtyAllQuotations.values(),
    ]);
  }
  private sortSupplierColumnsBy(key: SortSupplierFilterValue): void {
    const columns = [...this.selectedQuotations];
    switch (key) {
      case 'supplierRating':
        columns.sort((a, b) => b.supplierRating - a.supplierRating);
        break;
      case 'cheapest':
        columns.sort(
          (a, b) =>
            this.summaryFields[a.uid]?.amountUsd -
            this.summaryFields[b.uid]?.amountUsd
        );
        break;
      case 'leadTime':
        columns.sort(
          (a, b) =>
            this.summaryFields[a.uid].leadDays -
            this.summaryFields[b.uid].leadDays
        );
        break;
      case 'mostOriginalItemsQuoted':
        columns.sort((a, b) => this.orgCount[b.uid] - this.orgCount[a.uid]);
        break;
      default:
        this.selectedQuotations = columns.reduce((accum, quotation) => {
          const index = this.selectedQuotationUids.indexOf(quotation.uid);
          accum[index] = quotation;
          return accum;
        }, []);
        return;
    }
    this.selectedQuotations = columns;
  }

  private onSearch(searchText: string): void {
    this.gridSearch = searchText;
    this.lazyLoad({
      first: 0,
      rows: this.bufferSize,
    });
  }

  private updateItemDetails(rowChanges: RowChanges): void {
    [...rowChanges.selected, ...rowChanges.modified].forEach((item) => {
      if (item?.itemStatus === 'Declined') {
        return;
      }
      const { discount, leadDays, unPrice, uomQty, uomConversion } =
        item.types[item.selectedType];
      this.itemDetails[item.uid] = {
        discount,
        leadDays,
        reqItemUid: item.reqItemUid,
        unPrice,
        uomQty,
        uomConversion,
      };
    });
  }

  public onRowStateChange({ rowChanges }: RowStateChange): void {
    const { selected, unselected, modified } = rowChanges;
    [...selected, ...modified].forEach(({ uid, orderQty, reqItemUid }) => {
      this.itemsOrderQtyAllQuotations.set(uid, { orderQty, reqItemUid });
    });
    unselected.forEach(({ uid }) => {
      this.itemsOrderQtyAllQuotations.delete(uid);
    });

    this.updateQuotationItemStateMap(rowChanges);
    this.updateItemDetails(rowChanges);
    const summary = this.calculateSummaryFields(
      [...this.quotationItemStateMap.values()],
      this.itemDetails
    );
    this.setSummaryFields(summary);
    this.calculateSummaryReqItemsOrderQty([
      ...this.itemsOrderQtyAllQuotations.values(),
    ]);

    if (
      this.sortSupplierColumnsKey &&
      Object.keys(this.summaryFields).length === this.selectedQuotations.length
    ) {
      this.sortSupplierColumnsBy(this.sortSupplierColumnsKey);
    }
    this.cdr.markForCheck();
  }

  private updateQuotationItemStateMap(rowChanges: RowChanges): void {
    if (this.bulkPurchase) {
      this.ensureSingleQuoteItemSelection(rowChanges.selected);
    }
    rowChanges.unselected.forEach(({ uid }) =>
      this.quotationItemStateMap.delete(uid)
    );
    [...rowChanges.selected].forEach(({ uid, ...row }) =>
      this.quotationItemStateMap.set(uid, {
        itemUid: row.itemUid,
        orderQty: row.orderQty,
        quotationItemUid: uid,
        quotationUid: row.quotationUid,
        type: row.selectedType,
      })
    );

    [...rowChanges.modified].forEach(({ uid, ...row }) => {
      if (this.quotationItemStateMap.has(uid)) {
        this.quotationItemStateMap.set(uid, {
          itemUid: row.itemUid,
          orderQty: row.orderQty,
          quotationItemUid: uid,
          quotationUid: row.quotationUid,
          type: row.selectedType,
        });
      }
    });
    this.quotationItemStateMapChange.emit(this.quotationItemStateMap);
  }

  private ensureSingleQuoteItemSelection(
    selected: QuotationItemRowFilled[]
  ): void {
    if (!selected?.length) {
      return;
    }

    // For bulk PO the quotationItemStateMap can't include more then 1 record for the same item uid
    const existingItemsMap = new Map(
      [...this.quotationItemStateMap.values()].map(
        ({ itemUid, quotationItemUid }) => [itemUid, quotationItemUid]
      )
    );
    selected.forEach(({ itemUid }) => {
      const existingQuoteItemUid = existingItemsMap.get(itemUid);
      if (existingQuoteItemUid) {
        this.quotationItemStateMap.delete(existingQuoteItemUid);
      }
    });

    // Reassign to trigger change detection
    this.quotationItemStateMap = new Map(this.quotationItemStateMap);
  }

  private calculateSummaryReqItemsOrderQty(items: ReqItemOrderQty[]): void {
    this.summaryReqItemsOrderQty = items.reduce(
      (accum, { orderQty, reqItemUid }) => ({
        ...accum,
        [reqItemUid]: (accum[reqItemUid] ?? 0) + orderQty,
      }),
      {}
    );
  }

  private areQuotationItemDetailsValid(details: QuotationItemDetails): boolean {
    return Boolean(
      details &&
        details.discount >= 0 &&
        details.unPrice &&
        details.uomQty &&
        details.uomConversion
    );
  }

  private calculateItemsSummary(
    details: QuotationItemDetails,
    orderQty: number,
    prevSummary: ItemsSummary = {
      amount: 0,
      amountUsd: 0,
      items: 0,
      leadDays: 0,
    },
    currencyRate: number
  ): ItemsSummary {
    if (!this.areQuotationItemDetailsValid(details)) {
      console.warn('Quotation Item details are not valid', details);
      return prevSummary;
    }
    const amount = calculateAmount(
      details.discount,
      details.unPrice,
      details.uomQty,
      details.uomConversion,
      orderQty
    );
    return {
      amount: amount + prevSummary.amount,
      amountUsd: this.currency.transform(
        amount + prevSummary.amount,
        currencyRate,
        eCurrency.Usd
      ),
      items: (prevSummary.items ?? 0) + 1,
      leadDays:
        details.leadDays > prevSummary.leadDays
          ? details.leadDays
          : prevSummary.leadDays,
    };
  }

  private calculateSummaryFields(
    state: QuotationItemStateDto[],
    itemDetails: Record<string, QuotationItemDetails>
  ): QuotationItemsSummary {
    return state.reduce(
      (acc, { quotationUid, quotationItemUid, orderQty }) => ({
        ...acc,
        [quotationUid]: this.calculateItemsSummary(
          itemDetails[quotationItemUid],
          orderQty,
          acc[quotationUid],
          this.quotationCurrencyRateMap.get(quotationUid)
        ),
      }),
      {}
    );
  }

  public onToggleReqItemsTableMode(event: boolean): void {
    this.isReqItemsTableInCollapsedMode = event;
  }

  public onHideRemarksChanged(event: boolean): void {
    this.remarksIsHidden = event;
    this.updateExpandedRowKeys();
  }

  public setSummaryFields(summaryFields: QuotationItemsSummary): void {
    this.summaryFields = summaryFields;
    this.quotationEvaluationSummaryChange.emit(summaryFields);
  }
}
