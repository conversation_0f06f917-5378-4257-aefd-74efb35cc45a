import {
  InitApprovalProccessResponse,
  QuotationAdditionalDetailsDto,
  QuotationDto,
  QuotationEvaluationDetails,
  QuotationEvaluationDetailsRequestDto,
  QuotationEvaluationItemDto,
  QuotationItemsRequestDto,
  QuotationItemStateDto,
  ReworkQuotationRequestDto,
  SaveEvaluationRequestDto,
} from '@j3-procurement/dtos/quotation';
import {
  ApiRequestService,
  eCrud,
  OData,
  WebApiRequest,
} from 'jibe-components';

import { Injectable } from '@angular/core';
import { SearchResponse } from '@j3-prc-shared/dtos/odata';
import { supplant } from '@j3-procurement/dtos';
import { InitApprovalProccessRequest } from '@j3-procurement/dtos/quotation';
import { Observable } from 'rxjs';
import { ePrcRequestAction } from '../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../models/enums/prc-request-entity.enum';

@Injectable({
  providedIn: 'root',
})
export class QuotationService {
  constructor(private readonly apiRequest: ApiRequestService) {}

  getQuotationList(
    requisitionUid: string,
    odata?: OData
  ): Observable<SearchResponse<QuotationDto>> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.QuotationList, { requisitionUid }),
      crud: eCrud.Post,
      odata,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }

  public getQuotationEvaluationItems(
    body: QuotationItemsRequestDto
  ): Promise<SearchResponse<QuotationEvaluationItemDto>> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: ePrcRequestAction.QuotationEvaluation,
      crud: eCrud.Post,
      body,
    };
    return this.apiRequest.sendApiReq(apiReq).toPromise();
  }

  public getQuotationEvaluationDetails(
    body: QuotationEvaluationDetailsRequestDto
  ): Observable<QuotationEvaluationDetails> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: ePrcRequestAction.QuotationEvaluationDetails,
      crud: eCrud.Post,
      body: body,
    };

    return this.apiRequest.sendApiReq(apiReq);
  }

  public updateQuotationItemStates(
    requisitionUid: string,
    body: SaveEvaluationRequestDto
  ): Observable<string[]> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.QuotationItemStates, {
        requisitionUid,
      }),
      body,
      crud: eCrud.Put,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }

  public getQuotationItemStateByRequisition(
    requisitionUid: string
  ): Observable<QuotationItemStateDto[]> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.QuotationItemStates, {
        requisitionUid,
      }),
      crud: eCrud.Get,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }

  sendQuotationForRework(
    quotationUid: string,
    body: ReworkQuotationRequestDto
  ): Observable<void> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: supplant(ePrcRequestAction.QuotationSendForRework, {
        quotationUid,
      }),
      body,
      crud: eCrud.Post,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }

  getQuotationAdditionalDetails(
    quotationUid: string
  ): Observable<QuotationAdditionalDetailsDto> {
    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Quotation,
      action: supplant(ePrcRequestAction.QuotationAdditionalDetails, {
        quotationUid,
      }),
      crud: eCrud.Get,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }

  public initiateApprovalProccess(
    requisitionUid: string,
    body: InitApprovalProccessRequest
  ): Observable<InitApprovalProccessResponse> {

    const apiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: supplant(ePrcRequestAction.RequisitionApprovalProccessInit, {
        requisitionUid,
      }),
      body,
      crud: eCrud.Post,
    };
    return this.apiRequest.sendApiReq(apiReq);
  }
}
