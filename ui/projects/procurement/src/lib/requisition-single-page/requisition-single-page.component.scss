@import "node_modules/j3-prc-components/styles/_mixins";
@include no-breadcrumb;

.jb-section-scrollfix {
  min-height: 550px;
  overflow-y: scroll;
  width: 100%;
}

.formcontainer {
  display: inline-block;
  width: 100%;
  padding: 0% 1% 0% 1%;
}

.formsection {
  width: 100%;
  margin: 0 15px;
  background: var(--jbLight);
}

.leftControls {
  padding: 2% 3% 2% 0%;
}

.rightControls {
  padding: 2% 0% 2% 3%;
}

.borderPix {
  border: 1px solid var(--jbGreyBlue100) !important;
}

.itemDescription {
  padding: 2%;
}

.labelAlign {
  padding-top: 1%;
}

:host::ng-deep .ui-chips-input-token input {
  width: 53.5em;
}

:host::ng-deep .control-upload {
  text-align: right;
  padding-right: 0.5rem;
}

.jb-singleSelect-dropdown {
  min-width: 80px;
  background-color: var(--jbLight) !important;
}

:host .footer {
  width: 80px;
  height: 14px;
  font-weight: 700;
  font-size: 12px;
  line-height: 14px;
  text-transform: capitalize;
  color: var(--jbGreyBlue400);
}
:host .footer-details {
  width: 80px;
  height: 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 6px;
  color: var(--jbGreyBlue900);
  margin-top: 24px;
  border-block-end: 1px solid var(--greyBlue400);
}
:host .approval-flow {
  width: 108px;
  height: 16px;
  font-weight: 500;
  font-size: 14px;
  line-height: 6px;
  color: var(--jbGreyBlue900);
  margin-top: 19px;
  border-block-end: 1px solid var(--greyBlue400);
}

:host .sections {
  display: flex;
  gap: 12px;
  flex: 1;
  min-height: inherit;
}
:host .box {
  overflow-y: scroll;
  padding-right: 5px;
}

.icons8-chat-room {
  font-size: 25px;
  margin-top: 5px;
  padding-bottom: 10px;
}

jb-layout-widget:last-child {
  margin-bottom: 24px;
}

.general-information {
  height: 248px;
}

.section {
  &.main {
    flex: 1;
    overflow: auto;
  }
}

:host::ng-deep {
  .requisition-items,
  .requisition-services,
  .requisition-attachments {
    .ui-table {
      .ui-table-scrollable-body {
        max-height: 240px !important;
        height: auto !important;
      }
    }
  }
  .requisition-machinery {
    .ui-treetable {
      .ui-treetable-scrollable-body {
        max-height: 240px;
        height: auto !important;
      }
    }
  }
}
