import { JobDto } from '@j3-prc-catalog/dtos/job';
import { stringsEqual } from '@j3-prc-shared/dtos';
import { FINDING_JOB_ACTION } from '@j3-procurement/dtos';

type PmsJob = Pick<JobDto, 'action' | 'jobType' | 'pmsUid'>;

export function generateNavigationUrl(job: PmsJob): string {
  if (!job) {
    return null;
  }
  return stringsEqual(job.action, FINDING_JOB_ACTION)
    ? generateTMUrl(job)
    : generatePMSUrl(job);
}

function generatePMSUrl({ pmsUid }: PmsJob): string {
  const data = { payload: { uid: pmsUid } };
  const encrypted = btoa(JSON.stringify(data));
  return `/pms/operationalJobDetailsPage?enc=${encrypted}`;
}

function generateTMUrl({ jobType, pmsUid }: PmsJob): string {
  const route = stringsEqual(jobType, 'VETTING OBSERVATION')
    ? 'vetting/detail-page'
    : 'jmsmaster';
  const data = {
    type: 'click',
    payload: { uid: pmsUid, WL_TYPE: jobType },
  };
  const encrypted = btoa(JSON.stringify(data));
  return `/jms/${route}?enc=${encrypted}`;
}
