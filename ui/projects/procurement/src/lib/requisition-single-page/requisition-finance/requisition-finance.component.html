<div class="jb-section-header" #financeSummary>
  <jb-layout-widget
    titleText="Summary"
    id="summary"
    titleIconClass="icons8-shopping-cart"
  >
    <prc-requisition-finance-summary
      awidget-content
      [bulkPurchase]="bulkPurchase"
      [objectUid]="objectUid"
      [isPOIssued]="isPOIssued"
      [procurementType]="prcType"
      [vesselUid]="vesselUid"
      [deliveryDate]="deliveryDate"
    ></prc-requisition-finance-summary>
  </jb-layout-widget>
</div>
<div class="jb-section-header" #financeItemConfig>
  <jb-layout-widget
    id="item-configuration"
    titleText="Item Finance Configuration"
    titleIconClass="icons8-shopping-cart"
  >
    <prc-finance-item-configuration
      awidget-content
      [bulkPurchase]="bulkPurchase"
      [catalogGLAccounts]="catalogGLAccounts"
      [deliveryDate]="deliveryDate"
      [financeItemsRequest]="financeItemsRequest"
      [isPOIssued]="isPOIssued"
      [isBudgetReadOnly]="isBudgetReadOnly"
      [procurementType]="prcType"
      [readonly]="readonly"
      [vesselUid]="vesselUid"
      (getItemConfigurationData)="getItemConfigurationData($event)"
    ></prc-finance-item-configuration>
  </jb-layout-widget>
</div>
<div class="jb-section-header" #financeAdditionalCharges>
  <jb-layout-widget
    titleText="Additional Charges Finance Configuration"
    titleIconClass="icons8-shopping-cart"
    id="additional-charges-configuration"
  >
    <prc-finance-additional-charges
      awidget-content
      [bulkPurchase]="bulkPurchase"
      [additionalChargeAccountsMap]="additionalChargeAccountsMap"
      [additionalChargesRequest]="additionalChargesRequest"
      [deliveryDate]="deliveryDate"
      [isPOIssued]="isPOIssued"
      [isBudgetReadOnly]="isBudgetReadOnly"
      [objectUid]="objectUid"
      [procurementType]="prcType"
      [readonly]="readonly"
      [vesselUid]="vesselUid"
      (getAdditionalChargesData)="getAdditionalChargesData($event)"
    ></prc-finance-additional-charges>
  </jb-layout-widget>
</div>
