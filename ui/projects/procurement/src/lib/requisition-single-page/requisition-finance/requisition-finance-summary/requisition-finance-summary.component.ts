import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  calculateRemainingBudget,
  getFinanceSummary,
} from '@j3-procurement/dtos';
import { BudgetDto, FinanceBudgetDto } from '@j3-procurement/dtos/budgets';
import { FinanceSummaryDto } from '@j3-procurement/dtos/finance';
import {
  POFinancesRequestDto,
  POFinanceSummaryDto,
} from '@j3-procurement/dtos/po';
import { UnsubscribeComponent } from 'j3-prc-components';
import { eGridEvents, GridService } from 'jibe-components';
import { takeUntil } from 'rxjs/operators';
import {
  PO_PROCUREMENT_TYPE,
  REQUISITION_PROCUREMENT_TYPE,
} from '../../../models/enums/prc-single-page.enum';
import { GridInputsWithData } from '../../../models/interfaces/grid-inputs';
import { FinanceService } from '../../../services/finance.service';
import { POService } from '../../../services/po/po.service';
import {
  filters,
  filtersLists,
  getColumns,
  gridName,
  searchFields,
} from './grid-inputs';
import { ColumnKey } from './types';

@Component({
  selector: 'prc-requisition-finance-summary',
  templateUrl: './requisition-finance-summary.component.html',
  styleUrls: ['./requisition-finance-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequisitionFinanceSummaryComponent
  extends UnsubscribeComponent
  implements OnInit, OnDestroy
{
  @Input() bulkPurchase: boolean;
  @Input() deliveryDate: Date;
  @Input() objectUid: string;
  @Input() procurementType: string;
  @Input() vesselUid: string;
  @Input() set isPOIssued(value: boolean) {
    this.isPoBudget = value;
    if (value) {
      this.initializeGridData();
    }
  }
  @ViewChild('remainingBudgetTemplate', { static: true })
  remainingBudgetTemplate: TemplateRef<HTMLElement>;

  @ViewChild('vesselTemplate', { static: true })
  vesselTemplate: TemplateRef<HTMLElement>;

  public data: POFinanceSummaryDto[];
  public gridInputs: GridInputsWithData<ColumnKey, FinanceSummaryDto>;
  private isPoBudget: boolean;
  constructor(
    private financeService: FinanceService,
    private poService: POService,
    private readonly gridService: GridService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }
  ngOnInit(): void {
    const columnTemplateMap: Partial<
      Record<ColumnKey, TemplateRef<HTMLElement>>
    > = {
      remainingBudget: this.remainingBudgetTemplate,
      vesselUid: this.vesselTemplate,
    };

    this.gridInputs = {
      gridName,
      data: [],
      columns: getColumns(this.bulkPurchase).map((column) => ({
        ...column,
        cellTemplate: columnTemplateMap[column.FieldName],
      })),
      searchFields,
      filters,
      filtersLists,
    };
    this.initializeGridData();
  }

  async initializeGridData(): Promise<void> {
    switch (this.procurementType) {
      case REQUISITION_PROCUREMENT_TYPE:
        this.data = await this.financeService
          .getFinanceSummaryMatrix(this.objectUid)
          .toPromise();
        this.getSummaryWithRemainingBudget();
        break;
      case PO_PROCUREMENT_TYPE:
        const data: POFinancesRequestDto[] = await this.poService
          .getPoFinanceSummaryMatrix(this.objectUid)
          .toPromise();

        const { poPageState$ } = this.financeService;
        poPageState$.next({
          ...poPageState$.value,
          poFinanceSummary: data,
        });

        this.loadFinanceSummary();
        break;
      default:
        break;
    }
    this.cdr.markForCheck();
  }

  loadFinanceSummary(): void {
    const { poPageState$ } = this.financeService;
    poPageState$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((state) => {
        const { financialItems, additionalCharges, poFinanceSummary } = state;
        if (financialItems.length) {
          this.data = getFinanceSummary(
            poFinanceSummary,
            additionalCharges,
            financialItems
          );
          this.getSummaryWithRemainingBudget();
        }
      });
  }

  async getAccountingBudgetsForGLAccounts(): Promise<Map<string, BudgetDto[]>> {
    if (!this.financeService.accountBudgets.size) {
      const glAccountUids = this.data?.map(({ glAccount }) => glAccount?.uid);
      const payload: FinanceBudgetDto = {
        vessel_uid: this.vesselUid,
        delivery_date: this.deliveryDate,
        gl_accounts: glAccountUids,
      };
      await this.financeService.loadBudgetsAndCodes(payload, this.bulkPurchase);
    }
    return this.financeService.accountBudgets;
  }

  async getSummaryWithRemainingBudget(): Promise<void> {
    // if po is issued, get budgets from j3_prc_po_budget else get it from accounting api
    const glAccountBudgetMap = await (this.isPoBudget
      ? this.getPoGLAccountBudgets()
      : this.getAccountingBudgetsForGLAccounts());

    if (glAccountBudgetMap?.size) {
      this.data = calculateRemainingBudget(
        this.data,
        glAccountBudgetMap,
        this.bulkPurchase
      );
    }

    this.gridInputs.data = this.data.map((item) => ({
      ...item,
      glAccount: item.glAccount?.name,
      budget: item.budget?.name,
      accountType: item.accountType?.name,
      project: item.project?.name,
      analysisCode: item.analysisCode?.name,
    }));

    this.gridService.refreshGrid(
      eGridEvents.StaticTable,
      this.gridInputs.gridName
    );
  }

  private async getPoGLAccountBudgets(): Promise<Map<string, BudgetDto[]>> {
    const PoGLAccountBudgets = await this.poService
      .getPoBudgets(this.objectUid)
      .toPromise();

    return PoGLAccountBudgets.reduce((acc, { uid, glAccountUid, amount }) => {
      const budgets = acc.get(glAccountUid) ?? [];
      budgets.push({ uid, amount });
      acc.set(glAccountUid, budgets);
      return acc;
    }, new Map<string, BudgetDto[]>());
  }
}
