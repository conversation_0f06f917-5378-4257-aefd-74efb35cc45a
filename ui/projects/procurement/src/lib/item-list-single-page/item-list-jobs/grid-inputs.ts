import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  GridRowActions,
} from 'jibe-components';

import { createColumns } from 'j3-prc-components';

export type ColumnKey =
  | 'action'
  | 'brandName'
  | 'catalogName'
  | 'catalogPath'
  | 'code'
  | 'department'
  | 'description'
  | 'dueDate'
  | 'jobName'
  | 'modelNumber'
  | 'runningNumber';

export enum GridRowAction {
  RemoveService = 'Remove Service from List',
}

export const gridName = 'itemListJobsGrid';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  [
    'Code',
    'code',
    { width: eGridColumnsWidth.LongDescription, hyperlink: true },
  ],
  ['Title', 'jobName', { width: eGridColumnsWidth.LongDescription }],
  ['Description', 'description', { width: eGridColumnsWidth.LongDescription }],
  [
    'Due Date',
    'dueDate',
    {
      ControlType: eFieldControlType.Date,
      FieldType: eGridCellType.Date,
      width: eGridColumnsWidth.Date,
    },
  ],
]);

export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['Job Action', 'action', { width: eGridColumnsWidth.LongDescription }],
    ['Brand / Make', 'brandName', { width: eGridColumnsWidth.LongDescription }],
    ['Model', 'modelNumber', { width: eGridColumnsWidth.LongDescription }],
    [
      'Department ',
      'catalogName',
      { width: eGridColumnsWidth.LongDescription },
    ],
  ],
  { IsVisible: false }
);

export const searchFields: ColumnKey[] = [
  'code',
  'jobName',
  'description',
  'catalogName',
];

export function getActions(isRemoveServiceDisabled = true): GridRowActions[] {
  return [
    {
      name: GridRowAction.RemoveService,
      icon: eIconNames.Cancel,
      color: eColor.JbRed,
      disabled: isRemoveServiceDisabled,
    },
  ];
}
