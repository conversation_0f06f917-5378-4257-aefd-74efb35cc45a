.topPadding {
  margin-top: -68px;
  display: flex;
}

.supplieritem_link {
  color: var(--jbBrandBlue500);
  cursor: pointer;
  text-decoration: underline;
}

.commentIcon {
  color: var(--jbBrandBlue500);
  cursor: pointer;
  text-decoration: underline;
}

:host .header-container__side-actions {
  display: flex;
  justify-content: flex-end;
  text-align: right;
  height: 32px;
  margin-top: -40px !important;
  background-color: var(--jbLightgrayishblue) !important;
}

.delete-pop-up-message {
  font-size: 14px;
  line-height: 16px;
  color: var(--darkOrGrey);
  margin: 10px 20px 20px;
  height: 16px;
  .confirm-button {
    float: right;
  }
}
:host::ng-deep .icons8-menu-2-filled:before {
  font-size: 120% !important;
}
:host .previewItemButtons {
  display: flex;
  justify-content: space-between;
  margin-right: 8px;
  margin-top: -44px !important;
  float: right;
  width: 464px;
}

.supplier-grid {
  white-space: nowrap;
}
.iconalign {
  margin-top: 10px;
  cursor: pointer;
}
.invoice-panel:hover {
  background-color: color-mix(in srgb, var(--jbBlack) 10%, transparent);
  cursor: pointer;
}
.p-grid-supgrid {
  display: flex;
  gap: 20px;
  justify-content: right;
  margin-left: 15px;
}

.template-column {
  display: flex;
  justify-content: space-between;
}
