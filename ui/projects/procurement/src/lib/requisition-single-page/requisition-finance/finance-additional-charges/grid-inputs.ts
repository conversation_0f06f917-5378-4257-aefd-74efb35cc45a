import { createColumns, getDataSourceReq } from 'j3-prc-components';
import {
  eFieldControlType,
  eGridColors,
  eGridColumnsWidth,
  eGridEvents,
  eGridIcons,
  GridRowActions,
  ShowSettings,
} from 'jibe-components';
import { ePrcSinglePageGridNames } from '../../../models/enums/prc-single-page.enum';

import { ColumnKey } from './types';

export type AdditionalChargeAction = 'Split Between Vessel';

export const gridName = ePrcSinglePageGridNames.FinanceAdditionalChargesGrid;

const columns = createColumns<string, ColumnKey>([
  [
    '#',
    'runningNumber',
    { DisableSort: true, width: eGridColumnsWidth.ShortNumber },
  ],
  [
    'Additional Charges',
    'additionalCost',
    { width: eGridColumnsWidth.LongDescription },
  ],
  [
    'Amount',
    'amount',
    {
      width: eGridColumnsWidth.ShortNumber,
      ControlType: eFieldControlType.Input,
    },
  ],
  [
    'GL Code',
    'glAccount.name',
    {
      width: eGridColumnsWidth.ShortDescription,
      ChangeEditableState: false,
      ControlType: eFieldControlType.Input,
      DisableSort: true,
      Editable: true,
      ReadOnly: true,
      IsMandatory: true,
    },
  ],
  [
    'Analysis Code',
    'analysisCodeUid',
    {
      width: eGridColumnsWidth.ShortDescription,
      ControlType: eFieldControlType.Dropdown,
      ChangeEditableState: false,
      DataSourceLabelKey: 'analysisCode.name',
      DisableSort: true,
      Editable: true,
      EditTable: {
        editTableLabelKey: 'analysisCode',
        editTableLabelValue: 'uid',
      },
      FieldType: eFieldControlType.Dropdown,
      IsMandatory: true,
    },
  ],
  [
    'Project',
    'projectUid',
    {
      width: eGridColumnsWidth.ShortDescription,
      ControlType: eFieldControlType.Dropdown,
      ChangeEditableState: false,
      DataSourceLabelKey: 'project.name',
      DisableSort: true,
      Editable: true,
      EditTable: {
        editTableLabelKey: 'project_name',
        editTableLabelValue: 'uid',
        editListApiReq: getDataSourceReq('projectCode'),
      },
      FieldType: eFieldControlType.Dropdown,
      IsMandatory: true,
    },
  ],
  [
    'Budget',
    'budgetUid',
    {
      width: eGridColumnsWidth.ShortDescription,
      ControlType: eFieldControlType.Dropdown,
      ChangeEditableState: false,
      DataSourceLabelKey: 'budget.name',
      DisableSort: true,
      Editable: true,
      EditTable: {
        editTableLabelKey: 'budget_name',
        editTableLabelValue: 'uid',
      },
      FieldType: eFieldControlType.Dropdown,
      IsMandatory: true,
    },
  ],
]);

export const getColumns = (isBulk: boolean) => {
  const bulkColumns = createColumns([
    [
      'Vessel',
      'vesselUid',
      { IsMandatory: false, width: eGridColumnsWidth.ShortDescription },
    ],
  ]);
  return isBulk ? [...columns, ...bulkColumns] : columns;
};
export const searchFields: ColumnKey[] = ['additionalCost'];

export const showSettings: ShowSettings = {
  'Clear Filters': false,
  Export: true,
  'Adjust Columns': true,
  showDefaultLables: false,
};
export const advancedSettings = [
  {
    label: eGridEvents.ClearFilters,
    icon: eGridIcons.ClearFilters3,
    color: eGridColors.JbBlack,
    show: false,
  },
  {
    label: eGridEvents.Export,
    icon: eGridIcons.MicrosoftExcel2,
    color: eGridColors.JbBlack,
    show: true,
  },
  {
    label: eGridEvents.AdjustColumns,
    icon: eGridIcons.Settings,
    color: eGridColors.JbBlack,
    show: true,
  },
];
export function getActions(bulkPurchase: boolean): GridRowActions[] {
  return bulkPurchase
    ? [
        {
          condition: bulkPurchase,
          name: 'Split Between Vessel',
          actionFunction: (dto: { amount: number }) => [
            {
              name: 'Split Between Vessel',
              icon: 'icons8-split',
              disabled: !dto.amount,
            },
          ],
        },
      ]
    : [];
}
