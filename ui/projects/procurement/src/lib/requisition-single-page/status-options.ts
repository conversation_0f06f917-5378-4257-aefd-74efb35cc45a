import { WorkflowType } from '@j3-procurement/dtos/task-status';
import {
  HeaderStatus,
  HeaderStatusColors,
} from '../models/interfaces/header-status';

export const statusOptions: { [P in WorkflowType]?: HeaderStatus } = {
  APPROVE: {
    color: HeaderStatusColors.Purple,
  },
  CANCEL: {
    color: HeaderStatusColors.Red,
  },
  CLOSE: {
    color: HeaderStatusColors.Purple,
  },
  COMPLETE: {
    color: HeaderStatusColors.Purple,
  },
  'IN PROGRESS': {
    color: HeaderStatusColors.Purple,
  },
  RAISE: {
    color: HeaderStatusColors.Purple,
  },
  REVIEW: {
    color: HeaderStatusColors.Purple,
  },
};
