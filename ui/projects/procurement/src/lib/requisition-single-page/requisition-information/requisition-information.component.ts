import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ControlContainer } from '@angular/forms';

import { IdLabel, Label } from '@j3-procurement/dtos/label';
import {
  CentralizedDataService,
  eDateFormats,
  IJbTextArea,
  IMultiSelectDropdown,
  ISingleSelectDropdown,
  JbControlOutputService,
} from 'jibe-components';
import { startWith, takeUntil } from 'rxjs/operators';

import {
  transformDateFormat,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { RequisitionPermission } from '../../models/enums/requisition-permission.enum';
import { PoTypeService } from '../../services/po/po-type.service';
import { POService } from '../../services/po/po.service';
import { formatPort } from '../../shared/pipes/format-port.pipe';
import { mapToLabel } from '../../utils/label-utils';
import { poTypesDropdown, vesselDepartmentDropdown } from '../sidebar-menu';
import { RequisitionDetailsForm } from '../types';
import { subjectTxtArea } from './constants';
import { getSegmentDropdown } from './dropdowns';

const jcdsPropMap = new Map<string, string>([
  ['poType', 'po_types'],
  ['department', 'VALUE'],
]);

@Component({
  selector: 'prc-requisition-information',
  templateUrl: './requisition-information.component.html',
  styleUrls: ['./requisition-information.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class RequisitionInformationComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() bulkPurchase = false;
  @Input() isEditMode: boolean;
  @Input() readonly: boolean;
  @Input() reviewMode: boolean;
  @Input() segmentUids: string[];
  @Input() vesselUid: string;

  public departmentTypeDropdown: ISingleSelectDropdown =
    vesselDepartmentDropdown;
  public form: TypedFormGroup<RequisitionDetailsForm>;
  public permissions = RequisitionPermission;
  public segmentDropdown: IMultiSelectDropdown = getSegmentDropdown();
  public subjectTxt: IJbTextArea = subjectTxtArea;
  public userDateControl: string;
  isDeliveryPortVisible = false;
  selectedPortDetails: IdLabel;
  deliveryPort: string;
  poTypesDropdown: ISingleSelectDropdown = poTypesDropdown;
  isEditAccess: boolean;
  disablePoType = false;
  constructor(
    private readonly cds: CentralizedDataService,
    private readonly cdr: ChangeDetectorRef,
    private readonly controlContainer: ControlContainer,
    private readonly jbControlService: JbControlOutputService,
    private readonly poService: POService,
    private readonly poTypeService: PoTypeService
  ) {
    super();
  }

  ngOnInit(): void {
    const { Date_Format: userDate } = this.cds.userDetails ?? {
      Date_Format: eDateFormats.DefaultFormat,
    };
    this.userDateControl = transformDateFormat(userDate);

    this.form = this.controlContainer
      .control as TypedFormGroup<RequisitionDetailsForm>;

    this.updatePOTypeDropdown({
      selectedValue: this.form.value.poType?.uid,
    });

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ id, dataSource, selectedValue }) => {
        const [controlName] = id?.split('_');
        const propName = jcdsPropMap.get(controlName);
        if (!propName) {
          return;
        }
        const ctrl = this.form.get(controlName);
        let label: Label | IdLabel;
        if (controlName === 'department') {
          const record = dataSource.find((ds) => ds.ID === selectedValue);
          label = { id: selectedValue, name: record.VALUE };
        } else {
          label = mapToLabel(dataSource, selectedValue, propName);
        }

        ctrl.patchValue(label);
        ctrl.markAsDirty();
      });

    const descriptionCtrl = this.form.get('description');
    descriptionCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((selectedValue) => {
        this.updateDescriptionTextBox({
          selectedValue,
        });
        this.cdr.markForCheck();
      });

    const deliveryPortCtrl = this.form.get('deliveryPort');
    deliveryPortCtrl.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(deliveryPortCtrl.value)
      )
      .subscribe((portValue) => {
        this.deliveryPort =
          portValue && formatPort(portValue.name, portValue.country);
        this.cdr.markForCheck();
      });

    const vesselDepartmentCtrl = this.form.get('department');

    vesselDepartmentCtrl.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(vesselDepartmentCtrl.value)
      )
      .subscribe((label) => {
        this.departmentDropdown({
          selectedValue: label?.id,
        });
        this.cdr.markForCheck();
      });

    const poTypeCtrl = this.form.controls.poType;
    poTypeCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$), startWith(poTypeCtrl.value))
      .subscribe((label) => {
        this.setPoTypeDropdown(label);
      });
  }

  deliveryPortSelected(port: IdLabel): void {
    this.isDeliveryPortVisible = false;
    this.selectedPortDetails = port;

    this.deliveryPort = port && formatPort(port.name, port.country);
    const ctrl = this.form.get('deliveryPort');
    ctrl.patchValue(port);
    ctrl.markAsDirty();
  }

  private departmentDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.departmentTypeDropdown = {
      ...this.departmentTypeDropdown,
      ...config,
    };
  }

  private updateDescriptionTextBox(config: Partial<IJbTextArea>): void {
    this.subjectTxt = {
      ...this.subjectTxt,
      ...config,
    };
    this.form.value.description = this.subjectTxt.selectedValue;
  }

  private updatePOTypeDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.poTypesDropdown = {
      ...this.poTypesDropdown,
      ...config,
    };
  }

  private async getPoTypeList(): Promise<Label[]> {
    const filter = await this.poTypeService.getPoTypeFilter(this.vesselUid);
    return this.poService.getPoTypes(filter).toPromise();
  }

  private async setPoTypeDropdown(poType: Label): Promise<void> {
    const { uid, name } = poType ?? {};
    const poTypes =
      this.poTypesDropdown.dataSource ?? (await this.getPoTypeList());
    const selectedPoExist = uid && poTypes?.some((t) => t.uid === uid);
    const dataSource = selectedPoExist ? poTypes : [{ uid, po_types: name }];

    this.disablePoType = !selectedPoExist || this.bulkPurchase;
    this.poTypesDropdown = {
      ...this.poTypesDropdown,
      dataSource,
      selectedValue: uid,
    };
    this.cdr.markForCheck();
  }
}
