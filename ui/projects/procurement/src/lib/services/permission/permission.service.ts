import { Inject, Injectable } from '@angular/core';
import { from, timer } from 'rxjs';
import { concatMap, filter, map, take } from 'rxjs/operators';
import { FeatureSwitch } from '../../models/interfaces/feature-switch';
import { FEATURE_SWITCH } from '../feature-switch';
import { InfraService } from '../infra.service';
import { UserRight } from '../types';

@Injectable({ providedIn: 'root' })
export class PermissionService {
  private rightCodeSetPromise: Promise<Set<string>>;
  private roleIdSetPromise: Promise<Set<number>>;

  constructor(
    @Inject(FEATURE_SWITCH) private featureSwitch: FeatureSwitch,
    private readonly infraService: InfraService
  ) {}

  public dispose(): void {
    this.rightCodeSetPromise = null;
    this.roleIdSetPromise = null;
  }

  public async hasAtLeastOnePermission(
    rightCodes: string | string[]
  ): Promise<boolean> {
    if (this.featureSwitch.procurementDisableAccessRights) {
      return true;
    }

    const rightCodesSet = await this.getRightCodeSet();
    const codes = Array.isArray(rightCodes) ? rightCodes : [rightCodes];
    return codes.some((code) => rightCodesSet.has(code));
  }

  public async hasAtLeastOneRole(roleIds: number | number[]): Promise<boolean> {
    const roleIdSet = await this.getRoleIdSet();
    const ids = Array.isArray(roleIds) ? roleIds : [roleIds];
    return ids.some((id) => roleIdSet.has(id));
  }

  public async hasPermissions(rightCodes: string | string[]): Promise<boolean> {
    if (this.featureSwitch.procurementDisableAccessRights) {
      return true;
    }

    const rightCodesSet = await this.getRightCodeSet();
    const codes = Array.isArray(rightCodes) ? rightCodes : [rightCodes];
    return codes.every((code) => rightCodesSet.has(code));
  }

  public async hasRoles(roleIds: number | number[]): Promise<boolean> {
    const roleIdSet = await this.getRoleIdSet();
    const ids = Array.isArray(roleIds) ? roleIds : [roleIds];
    return ids.every((id) => roleIdSet.has(id));
  }

  private async getRightCodeSet(): Promise<Set<string>> {
    if (!this.rightCodeSetPromise) {
      this.rightCodeSetPromise = this.initRightCodeSet();
    }
    return this.rightCodeSetPromise;
  }

  private async getRoleIdSet(): Promise<Set<number>> {
    if (!this.roleIdSetPromise) {
      this.roleIdSetPromise = this.initRoleIdsSet();
    }
    return this.roleIdSetPromise;
  }

  private getUserRightsFromLocalStorage(): UserRight[] {
    return JSON.parse(localStorage.getItem('userDetails'))?.UserRoles;
  }

  private async initRightCodeSet(): Promise<Set<string>> {
    const userRights = await this.waitForUserRights();
    return new Set(userRights?.map((role) => role.Right_Code));
  }

  private async initRoleIdsSet(): Promise<Set<number>> {
    const { records: userRoles } = await this.infraService.getUserRoles();
    return new Set(
      userRoles?.filter((role) => role.IsAssigned).map((role) => role.Role_ID)
    );
  }

  private async waitForUserRights(): Promise<UserRight[]> {
    const userRights = this.getUserRightsFromLocalStorage();
    if (userRights) {
      return userRights;
    }
    /**
     * A workaround for the iframe vesion that waits until userRights are set in the localStorage from jb-components.
     * Nice to have an event that will be emitted once userDetails are set in jb-components.
     */
    const timeDiffs = [
      50, 50, 100, 100, 200, 200, 400, 400, 800, 800, 1600, 1600, 3200, 3200,
      6400,
    ];
    return from(timeDiffs)
      .pipe(
        concatMap((delay) => timer(delay)),
        map(() => this.getUserRightsFromLocalStorage()),
        filter((value) => !!value),
        take(1)
      )
      .toPromise();
  }
}
