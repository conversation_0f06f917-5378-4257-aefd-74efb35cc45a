<div class="column-left">
  <div
    class="status-checkmark"
    [ngClass]="{
      active: item.isComplete && !item.isCancel,
      declined: item.isDecline,
      cancel: item.isCancel
    }"
  >
    <i
      *ngIf="item.isComplete || item.isDecline"
      class="pi"
      [ngClass]="{
        'pi-times': item.isDecline || item.isCancel,
        'pi-check': item.isComplete && !item.isCancel
      }"
    ></i>
  </div>

  <div class="status-line">
    <div
      class="status-line-inside"
      [ngClass]="{
        active: item.isComplete && !item.isCancel,
        declined: item.isDecline,
        cancel: item.isCancel
      }"
    ></div>
  </div>
</div>

<div class="column-right">
  <div class="status-date" *ngIf="item.date">{{ formatDate(item.date) }}</div>

  <div
    class="status-status"
    [ngClass]="{
      active: item.isComplete && !item.isCancel,
      declined: item.isDecline,
      cancel: item.isCancel
    }"
  >
    <i [ngClass]="orderStatusIcons.get(item.status)" class="pi"></i>

    {{ item.status }}
  </div>

  <div class="status-name">{{ item.name }}</div>

  <div class="status-date-ready" *ngIf="item.readyDate">
    {{ formatDate(item.readyDate) }}
  </div>

  <div
    class="status-number"
    *ngIf="item.orderConfirmationNumber && item.isComplete"
  >
    {{ item.orderConfirmationNumber }}
  </div>
  <div class="status-number" *ngIf="item.isDecline">
    Order Declined by Supplier
  </div>

  <div class="status-date-ready" *ngIf="item.cancelReason">
    {{ item.cancelReason }}
  </div>
  <span class="status-button" *ngIf="!hideLink" (click)="openStatus()">
    <span
      *ngIf="viewUpdateStatus && permission.UpdateOrderStatus | hasPermissions"
      >Update status</span
    >
    <span *ngIf="viewMoreDetails">More Details</span>
    <i *ngIf="showExpandArrow" class="icon icons8-expand-arrow"></i>
  </span>
</div>
