import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import {
  bulkCheck,
  CreateRequisitionErrorData,
  directPOCheck,
  stringsEqual,
  WARNING,
} from '@j3-procurement/dtos';
import { ItemListBaseDto } from '@j3-procurement/dtos/item-list';
import { IdLabel, PoTypeLabel } from '@j3-procurement/dtos/label';
import { CreateRequisitionDto } from '@j3-procurement/dtos/requisition';
import {
  localToUTC,
  ModalDialogService,
  NotificationService,
  transformDateFormat,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { TypedFormGroup } from 'j3-prc-components';
import {
  CentralizedDataService,
  eDateFormats,
  IJbDialog,
  IJbTextArea,
  IMultiSelectDropdown,
  ISingleSelectDropdown,
  JbControlOutputService,
} from 'jibe-components';
import { filter, map, take, takeUntil } from 'rxjs/operators';
import { AT_LEAST_ONE_CHARACTER } from '../../models/constants';
import { DirectPoPermission } from '../../models/enums/direct-po-permission.enum';
import {
  ePrcConfirmLabel,
  ePrcErrorMessages,
  ePrcModalMessages,
} from '../../models/enums/prc-messages.enum';
import { JCDSPoTypeData } from '../../models/interfaces/jcds/jcds-po-type-data';
import { Vessel } from '../../models/interfaces/vessel';
import { PrcError } from '../../search-preview/types';
import { ItemListService } from '../../services/item-list/item-list.service';
import { PermissionService } from '../../services/permission/permission.service';
import { PoTypeService } from '../../services/po/po-type.service';
import { PrcSharedService } from '../../services/prc-shared.service';
import { RequisitionService } from '../../services/requisition/requisition.service';
import { SegmentService } from '../../services/segment/segment.service';
import { VesselService } from '../../services/vessel/vessel.service';
import { GenericPort } from '../../shared/generic-port-select/generic-port';
import { RequisitionCreatedPopupData } from '../../shared/requisition-created-popup/types';
import { UpcomingPort } from '../../shared/upcoming-port-select/upcoming-port';
import { mapToLabel } from '../../utils/label-utils';
import { DUPLICATE_ITEMS_FOUND } from './constants';
import { CreateRequisitionForm } from './create-requisition-form';
import { getItemListDropdown, urgencyDropdown } from './dropdowns';

/** Added to avoid chokidar issue */
type ItemListBaseDtoType = ItemListBaseDto;

const jcdsPropMap = new Map<
  string,
  string | ((value: JCDSPoTypeData) => PoTypeLabel)
>([
  [
    'poType',
    ({ uid, po_types, types }) => ({
      uid,
      name: po_types,
      type: types,
    }),
  ],
  ['urgency', 'display_name'],
]);
@Component({
  selector: 'prc-item-list-create-requisition',
  templateUrl: './item-list-create-requisition.component.html',
  styleUrls: [
    '../item-list-section-layout.scss',
    './item-list-create-requisition.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ItemListCreateRequisitionComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public vesselDepartment: IdLabel;

  @Input() itemListUid: string;
  @Input() set itemList({
    urgency,
    name,
    vesselDepartment,
  }: ItemListBaseDtoType) {
    this.vesselDepartment = vesselDepartment;

    this.form.patchValue({
      urgency,
      description: name,
    });
    if (urgency) {
      this.updateUrgencyDropdown({ selectedValue: urgency?.uid });
    }
  }
  @Input() segmentUids: string[];
  @Input() vessel: Vessel;
  @Input() set popupOpen(isOpen: boolean) {
    this.isOpen = isOpen;
  }
  @Output() close = new EventEmitter<boolean>();
  @Output() requsitionCreated = new EventEmitter<void>();

  public disableSaveButton = false;
  public isOpen = true;
  public createRequisitionDialog: IJbDialog = {
    dialogHeader: 'Create Requisition',
    dialogWidth: 920,
    showHeader: true,
    closableIcon: true,
  };
  public form: TypedFormGroup<CreateRequisitionForm>;
  public itemListDropdown = getItemListDropdown();
  public poTypeDropdown: ISingleSelectDropdown;
  public urgencyDropdown = urgencyDropdown;

  public subjectTextArea: IJbTextArea = {
    placeholder: 'Subject',
    id: 'description',
    maxlength: 200,
    rows: 6,
    tooltipValue: 'Text Range: Minimum: 1 Maximum: 200',
  };
  public genericPort: GenericPort;
  public upcomingPortDepartureDate: Date;

  public userDateControl: string;
  public requisitionCreatedPopupData: RequisitionCreatedPopupData;
  public isDirectPO = false;
  public requisitionUid: string;
  public requisitionNumber: string;
  hasCreateDirectPOAccess: boolean;

  constructor(
    fb: FormBuilder,
    private cdr: ChangeDetectorRef,
    private itemListService: ItemListService,
    private jbControlService: JbControlOutputService,
    private modalDialogService: ModalDialogService,
    private notificationService: NotificationService,
    private requsitionService: RequisitionService,
    private readonly cds: CentralizedDataService,
    private readonly permissionService: PermissionService,
    private readonly poTypeService: PoTypeService,
    private readonly prcSharedService: PrcSharedService,
    private readonly segmentService: SegmentService,
    private readonly vesselService: VesselService
  ) {
    super();
    this.form = fb.group<CreateRequisitionForm>({
      poType: [null, Validators.required],
      urgency: [null, Validators.required],
      itemListUids: [[]],
      deliveryPort: [null, Validators.required],
      deliveryUpcomingPort: [],
      deliveryDate: [null, Validators.required],
      movement: [],
      description: [
        '',
        [
          Validators.required,
          Validators.maxLength(200),
          Validators.pattern(AT_LEAST_ONE_CHARACTER),
        ],
      ],
    });
  }

  async ngOnInit(): Promise<void> {
    const { Date_Format: userDate } = this.cds.userDetails ?? {
      Date_Format: eDateFormats.DefaultFormat,
    };
    const vesselUid = this.vessel.uid;

    this.userDateControl = transformDateFormat(userDate);
    this.hasCreateDirectPOAccess = await this.permissionService.hasPermissions(
      DirectPoPermission.CreateDirectPo
    );
    this.poTypeDropdown = await this.poTypeService.getPoTypeDropdown(vesselUid);

    this.itemListService
      .getItemLists(vesselUid)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((lists) =>
        this.updateItemListDropdown({
          dataSource: lists.filter(
            (l) => l.itemCount && !stringsEqual(l.uid, this.itemListUid)
          ),
        })
      );

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ id, dataSource, selectedValue }) => {
        const controlName = id?.split('-')[0];
        const propName = jcdsPropMap.get(controlName);
        if (!propName) {
          return;
        }
        const label: PoTypeLabel = mapToLabel(
          dataSource,
          selectedValue,
          propName
        );
        const ctrl = this.form.get(controlName);
        ctrl.patchValue(label);
        ctrl.markAsDirty();
      });
  }

  public onDeliveryPortChange(event: GenericPort): void {
    if (
      (this.genericPort &&
        this.genericPort.Port_ID !== event?.PORT_ID &&
        this.genericPort.PORT_ID !== event?.PORT_ID) ||
      !this.genericPort
    ) {
      this.form.get('deliveryUpcomingPort').reset();
      const port: GenericPort = {
        PORT_ID: event?.PORT_ID,
        port_country: event?.port_country,
        port_name: event?.port_name,
        UN_LOCODE: event?.UN_LOCODE,
      };
      this.genericPort = port;
      this.form.patchValue({ movement: 'other' });
    }
  }

  public onUpcomingPortChange(event: UpcomingPort): void {
    if (!event) {
      this.upcomingPortDepartureDate = null;
      return;
    }
    const port: GenericPort = {
      PORT_ID: event.Port_ID,
      port_country: event.country,
      port_name: event.Port_Name || event.port,
      UN_LOCODE: event.UN_LOCODE,
    };
    this.genericPort = port;

    this.upcomingPortDepartureDate = event.departure_date
      ? new Date(event.departure_date)
      : null;

    if (event.arrival) {
      this.form.get('deliveryDate').setValue(new Date(event.arrival));
    }
    this.form.patchValue({ movement: 'port' });
    this.form
      .get('deliveryPort')
      .setValue(port.PORT_ID, { emitViewToModelChange: false });
  }

  public async onSave(mergeItems?: boolean): Promise<void> {
    if (!this.form.valid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }

    const isBulk = bulkCheck(this.form.value.poType?.type);

    if (isBulk) {
      const onlyStoreSegments = await this.segmentService.storesSegmentUid$
        .pipe(
          filter((storeSegmentUids) => storeSegmentUids != null),
          map((storeSegmentUids) =>
            this.segmentUids.every((uid) => storeSegmentUids?.has(uid))
          ),
          take(1)
        )
        .toPromise();

      if (
        !onlyStoreSegments &&
        !(await this.modalDialogService.openDialog({
          confirmButtonLabel: ePrcConfirmLabel.Confirm,
          text: ePrcModalMessages.CreateRequisitionWithCorrectSegmentsOnly,
        }))
      ) {
        return;
      }
    }

    if (!mergeItems) {
      const isConfirmed = await this.modalDialogService.openDialog({
        confirmButtonLabel: ePrcConfirmLabel.Confirm,
        text: ePrcModalMessages.CreateRequisitionPopup,
        jbDialog: {
          dialogHeader: ePrcModalMessages.CreateRequisition,
        },
      });

      if (!isConfirmed) {
        return;
      }
    }

    this.disableSaveButton = true;
    this.cdr.markForCheck();

    try {
      const { deliveryPort, deliveryUpcomingPort, deliveryDate, ...rest } =
        this.form.value;
      const { uid, name } = this.vessel;
      const [vesselDetails, { clientUid }] = await Promise.all([
        this.vesselService.getVesselDetailsByUid(this.vessel.uid).toPromise(),
        this.prcSharedService.getClientUid().toPromise(),
      ]);

      const dto: CreateRequisitionDto = {
        ...rest,
        mergeItems,
        deliveryPort: {
          id: deliveryPort,
          country: this.genericPort?.port_country,
          name: this.genericPort?.port_name,
          code: this.genericPort?.UN_LOCODE,
        },
        department: this.vesselDepartment,
        vessel: {
          uid,
          name,
          imoNo: vesselDetails.imoNo,
          managementCompanyCode: vesselDetails.managementCompanyCode,
          managementCompanyName: vesselDetails.managementCompanyName,
          shortName: vesselDetails.shortName,
          vesselClass: vesselDetails.vesselClass,
          vesselHullNo: vesselDetails.vesselHullNo,
          vesselId: vesselDetails.vesselId,
          vesselManagerEmail: vesselDetails.vesselManagerEmail,
          vesselManagerName: vesselDetails.vesselManagerName,
          vesselManagerPhoneNo: vesselDetails.vesselManagerPhoneNo,
          vesselName: vesselDetails.vesselName,
          vesselOwnerCode: vesselDetails.vesselOwnerCode,
          vesselOwnerName: vesselDetails.vesselOwnerName,
          yardNumber: vesselDetails.yardNumber,
        },
        deliveryDate: localToUTC(deliveryDate),
        itemListUids: [
          this.itemListUid,
          ...this.itemListDropdown.selectedValues,
        ],
        clientUid,
      };
      const { uid: requisitionUid, requisitionNumber } =
        await this.requsitionService.createRequisition(dto).toPromise();
      this.requisitionUid = requisitionUid;
      this.requisitionNumber = requisitionNumber;
      this.isDirectPO = directPOCheck(dto.poType?.type);
      this.requisitionCreatedPopupData = {
        requisitionNumber,
        requisitionUid,
        isDirectPO: this.isDirectPO,
      };
      this.requsitionCreated.emit();
    } catch (err) {
      this.handleCreateRequisitionErrors(err);
    }
    this.disableSaveButton = false;
    this.cdr.markForCheck();
  }

  private async handleCreateRequisitionErrors({
    error,
    message,
  }: PrcError<CreateRequisitionErrorData>): Promise<void> {
    if (error?.type === WARNING) {
      const isAlertPopupBoxType = error?.data?.popupBoxType === 'ALERT';

      const confirmed = await this.modalDialogService.openDialog({
        jbDialog: {
          dialogHeader: DUPLICATE_ITEMS_FOUND,
        },
        text: error.message,
        ...(isAlertPopupBoxType
          ? { confirmButtonLabel: ePrcConfirmLabel.OK }
          : {
              confirmButtonLabel: ePrcConfirmLabel.Confirm,
              rejectButtonLabel: ePrcConfirmLabel.Cancel,
            }),
      });

      if (!isAlertPopupBoxType && confirmed) {
        this.onSave(true);
      }
    } else {
      this.notificationService.error(error?.message ?? message);
    }
  }

  public onCancel(): void {
    this.isOpen = false;
    this.close.emit(false);
  }

  public closeCreateRequisitionPopup(): void {
    this.onCancel();
    this.cdr.markForCheck();
  }

  private updateItemListDropdown(config: Partial<IMultiSelectDropdown>): void {
    this.itemListDropdown = {
      ...this.itemListDropdown,
      ...config,
    };
  }

  private updateUrgencyDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.urgencyDropdown = {
      ...this.urgencyDropdown,
      ...config,
    };
  }
  closeDialog(): void {
    this.requisitionCreatedPopupData = undefined;
    this.closeCreateRequisitionPopup();
  }
}
