<aside class="sidebar-container" [class.sidebar-hidden]="sidebarHidden">
  <div class="sidebar-content" [style.width.px]="width">
    <ng-content select="[slot=sidebar-content]"></ng-content>
  </div>

  <div
    class="divider"
    [ngClass]="{ resizable: resizable }"
    (pointerdown)="startResize()"
  >
    <img
      alt="arrow-open"
      src="/assets/images/grid/arrow-open.svg"
      (click)="sidebarHidden = !sidebarHidden"
      class="toggle-sidebar-button"
    />
    <div class="vertical-line"></div>
  </div>
</aside>
<section class="content">
  <ng-content></ng-content>
</section>
