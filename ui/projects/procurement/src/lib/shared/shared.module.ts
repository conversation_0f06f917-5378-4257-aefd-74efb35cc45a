import { CommonModule, DatePipe } from '@angular/common';
import { NgModule, SkipSelf } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  eGridEvents,
  GridService,
  JibeComponentsModule,
} from 'jibe-components';
import {
  MenuModule,
  PaginatorModule,
  SharedModule as PrimeNgModule,
} from 'primeng';

import { RouterModule } from '@angular/router';
import { CoreModule, RoleModule } from 'j3-prc-components';
import { GridColumnService } from '../services/grid-column.service';
import { TableComponent as BasicTableComponent } from './basic-table/table.component';
import { CreateItemListPopupComponent } from './create-item-list-popup/create-item-list-popup.component';
import { DeclineObjectComponent } from './decline-object/decline-object.component';
import { DeliveryInstructionComponent } from './delivery-instruction/delivery-instruction.component';
import { DeliveryMethodFormComponent } from './delivery-method-form/delivery-method-form.component';
import { DeliveryPortCountryComponent } from './delivery-port-country/delivery-port-country.component';
import { DeliveryPortFormComponent } from './delivery-port-form/delivery-port-form.component';
import { EmailInputChipsComponent } from './email-chips-input/email-input-chips.component';
import { GenericHeaderComponent } from './generic-header/generic-header.component';
import { GenericPortSelectComponent } from './generic-port-select/generic-port-select.component';
import { GlAccountSelectComponent } from './gl-account-select/gl-account-select.component';
import { HandoverMenuComponent } from './handover-menu/handover-menu.component';
import { IconTemplateComponent } from './icon-template/icon-template.component';
import { PrcGetSegmentPipe } from './icon-template/prc-get-segment.pipe';
import { IconTextComponent } from './icon-text/icon-text.component';
import { MachineryComponent } from './machinery/machinery.component';
import { MaxFractionDigitsDirective } from './max-fraction-digits.directive';
import { MultiSelectChipsComponent } from './multi-select-chips/multi-select-chips.component';
import { ConfigureColumnsPipe } from './pipes/configure-columns.pipe';
import { FormatPortPipe } from './pipes/format-port.pipe';
import { HasPermissionsPipe } from './pipes/has-permissions.pipe';
import { JCDSItemPipe } from './pipes/jcds-item.pipe';
import { PrcGetRfqDisplayNamePipe } from './pipes/prc-get-rfq-display-name.pipe';
import { PrcGetRfqStatusPipe } from './pipes/prc-get-rfq-status.pipe';
import { PrcGetRfqSummaryPipe } from './pipes/prc-get-rfq-summary.pipe';
import { RobPipe } from './pipes/rob.pipe';
import { StripHtmlPipe } from './pipes/striphtml.pipe';
import { UtcToLocalPipe } from './pipes/utc-to-local.pipe';
import { VesselNamePipe } from './pipes/vessel-name.pipe';
import { RequisitionCreatedPopupComponent } from './requisition-created-popup/requisition-created-popup.component';
import { RightPaneCardComponent } from './right-pane/right-pane-card/right-pane-card.component';
import { RightPaneCountersComponent } from './right-pane/right-pane-counters/right-pane-counters.component';
import { RightPaneDetailsComponent } from './right-pane/right-pane-details/right-pane-details.component';
import { RightPaneDividerComponent } from './right-pane/right-pane-divider/right-pane-divider.component';
import { PrcRightPaneHeaderComponent } from './right-pane/right-pane-header/prc-right-pane-header.component';
import { RightPaneSectionComponent } from './right-pane/right-pane-section/right-pane-section.component';
import { RightPaneComponent } from './right-pane/right-pane.component';
import { SectionComponent } from './section/section.component';
import { SidebarComponent } from './sidebar/sidebar.component';
import { BaseSectionComponent } from './single-page-sections/base-section/base-section.component';
import { DescriptionPopupComponent } from './single-page-sections/job-section/description-popup/description-popup.component';
import { JobSectionComponent } from './single-page-sections/job-section/job-section.component';
import { SinglePageSubHeaderComponent } from './single-page-sub-header/single-page-sub-header.component';
import { StatusComponent } from './status/status.component';
import { SupplierSelectComponent } from './supplier-select/supplier-select.component';
import { TableComponent } from './table/table.component';
import { UpcomingPortSelectComponent } from './upcoming-port-select/upcoming-port-select.component';
import { WarningComponent } from './warning/warning.component';

const components = [
  BaseSectionComponent,
  BasicTableComponent,
  CreateItemListPopupComponent,
  RequisitionCreatedPopupComponent,
  EmailInputChipsComponent,
  DeclineObjectComponent,
  DeliveryMethodFormComponent,
  DeliveryPortCountryComponent,
  DeliveryPortFormComponent,
  DeliveryInstructionComponent,
  DescriptionPopupComponent,
  GenericHeaderComponent,
  GenericPortSelectComponent,
  GlAccountSelectComponent,
  HandoverMenuComponent,
  IconTemplateComponent,
  IconTextComponent,
  JobSectionComponent,
  MachineryComponent,
  MultiSelectChipsComponent,
  PrcRightPaneHeaderComponent,
  RightPaneCardComponent,
  RightPaneComponent,
  RightPaneCountersComponent,
  RightPaneDetailsComponent,
  RightPaneDividerComponent,
  RightPaneSectionComponent,
  SectionComponent,
  SidebarComponent,
  SinglePageSubHeaderComponent,
  StatusComponent,
  SupplierSelectComponent,
  TableComponent,
  UpcomingPortSelectComponent,
  WarningComponent,
];

const directives = [MaxFractionDigitsDirective];

const pipes = [
  ConfigureColumnsPipe,
  FormatPortPipe,
  HasPermissionsPipe,
  JCDSItemPipe,
  PrcGetRfqDisplayNamePipe,
  PrcGetRfqStatusPipe,
  PrcGetRfqSummaryPipe,
  PrcGetSegmentPipe,
  RobPipe,
  StripHtmlPipe,
  UtcToLocalPipe,
  VesselNamePipe,
];

export function gridServiceFactory(
  gridService: GridService,
  gridColumnService: GridColumnService
): GridService {
  gridService.storeState$.subscribe(({ type, gridName, payload }) => {
    if (type === eGridEvents.AdjustColumnsSaved) {
      gridColumnService.saveColumnPreferences(gridName, payload);
    }
  });

  /** Prevent invoking original saveAdjustedColumns */
  return new Proxy(gridService, {
    get(
      target: GridService,
      prop: string | symbol,
      receiver: unknown
    ): unknown {
      const value = target[prop];
      if (value instanceof Function && prop === 'saveAdjustedColumns') {
        return () => {};
      }

      return Reflect.get(target, prop, receiver);
    },
  });
}

@NgModule({
  declarations: [...components, ...pipes, ...directives],
  imports: [
    CommonModule,
    CoreModule,
    FormsModule,
    JibeComponentsModule,
    MenuModule,
    PaginatorModule,
    PrimeNgModule,
    ReactiveFormsModule,
    RouterModule,
    RoleModule,
  ],
  exports: [
    CoreModule,
    JibeComponentsModule,
    ...components,
    ...pipes,
    ...directives,
  ],
  providers: [
    DatePipe,
    FormatPortPipe,
    PrcGetSegmentPipe,
    PrcGetRfqDisplayNamePipe,
    PrcGetRfqStatusPipe,
    PrcGetRfqSummaryPipe,
    StripHtmlPipe,
    {
      provide: GridService,
      useFactory: gridServiceFactory,
      deps: [[new SkipSelf(), GridService], GridColumnService],
    },
  ],
})
export class SharedModule {}
