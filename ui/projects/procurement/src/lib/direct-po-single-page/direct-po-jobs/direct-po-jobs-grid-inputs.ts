import { createColumns, createFilters } from 'j3-prc-components';
import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  FilterListSet,
  GridRowActions,
} from 'jibe-components';

import { DirectPOJobDto } from '@j3-procurement/dtos/direct-po';

export type ColumnKey = Extract<
  keyof DirectPOJobDto,
  | 'code'
  | 'amount'
  | 'action'
  | 'title'
  | 'description'
  | 'dueDate'
  | 'unPrice'
  | 'discount'
  | 'amount'
  | 'leadDays'
  | 'catalogPath'
  | 'brandName'
  | 'model'
  | 'runningNumber'
  | 'activeStatus'
>;

export const gridName = 'directPOJobsGrid';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  ['Code', 'code', { hyperlink: true }],
  ['Title', 'title'],
  ['Description', 'description'],
  ['Due Date', 'dueDate'],
  [
    'Unit Price',
    'unPrice',
    {
      ControlType: eFieldControlType.Number,
      Precision: '1.2-2',
      Editable: true,
      ValidatorRequired: true,
      IsMandatory: true,
    },
  ],
  [
    'Discount',
    'discount',
    {
      ControlType: eFieldControlType.Number,
      Precision: '1.2-2',
      Editable: true,
    },
  ],
  ['Total Amount', 'amount'],
  [
    'Lead Days',
    'leadDays',
    {
      ControlType: eFieldControlType.Number,
      Editable: true,
      KeyFilter: 'pint',
    },
  ],
  ['Job Action', 'action', { IsVisible: false }],
  ['Brand / Make', 'brandName', { IsVisible: false }],
  ['Model', 'model', { IsVisible: false }],
]);

export const searchFields: ColumnKey[] = [
  'code',
  'title',
  'description',
  'catalogPath',
];

export enum gridActions {
  Add = 'Add to PO',
  Remove = 'Remove from PO',
}

const getAddGridAction = (hasEditServiceAccess: boolean): GridRowActions => ({
  name: gridActions.Add,
  icon: eIconNames.AddNew,
  color: eColor.JbBlack,
  disabled: !hasEditServiceAccess,
});

const getRemoveGridAction = (
  hasEditServiceAccess: boolean
): GridRowActions => ({
  name: gridActions.Remove,
  icon: eIconNames.Cancel,
  color: eColor.JbRed,
  disabled: !hasEditServiceAccess,
});

const setActionOnDemand = (
  dto: DirectPOJobDto,
  hasEditServiceAccess: boolean
): GridRowActions[] => {
  return dto.activeStatus
    ? [getRemoveGridAction(hasEditServiceAccess)]
    : [getAddGridAction(hasEditServiceAccess)];
};

export const getActions = (hasEditServiceAccess: boolean): GridRowActions[] => [
  {
    name: gridActions.Add,
    icon: eIconNames.AddNew,
    color: eColor.JbBlack,
    actionFunction: (dto: DirectPOJobDto) =>
      setActionOnDemand(dto, hasEditServiceAccess),
  },
];
export type FilterKey = 'activeStatus';
export const filters = createFilters<string, FilterKey>(
  [['Status', 'activeStatus', { DisplayCode: 'status' }]],
  { gridName }
);

export const filtersLists: FilterListSet = {
  activeStatus: {
    list: [
      {
        label: 'Active',
        value: true,
      },
      {
        label: 'Removed',
        value: false,
      },
    ],
    type: eGridCellType.Multiselect,
    odataKey: 'activeStatus',
  },
};
