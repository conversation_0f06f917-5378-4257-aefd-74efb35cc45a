<div class="delivery-method-form">
  <p-accordion [activeIndex]="0">
    <p-accordionTab header="Delivery Method">
      <form class="formcontainer" [formGroup]="deliveryMethodForm">
        <div
          [class.two-columns]="
            forceShowTwoColumns || supplierLabels[deliveryMethod]
          "
        >
          <div class="column">
            <prc-form-label label="Delivery Method" class="form-label">
              <jb-single-select-dropdown
                appendTo="body"
                [content]="deliveryMethodDropdown"
                [readOnlyValidator]="readonly"
              ></jb-single-select-dropdown>
            </prc-form-label>

            <ng-container
              *ngIf="supplierLabels[deliveryMethod] as supplierLabel"
            >
              <prc-form-label
                class="form-label"
                [label]="supplierLabel"
                [required]="!readonly"
              >
                <jb-single-select-dropdown
                  appendTo="body"
                  [content]="supplierDropdown"
                  [readOnlyValidator]="readonly"
                ></jb-single-select-dropdown>
              </prc-form-label>

              <prc-form-label
                label="Address"
                class="form-label"
                [required]="!readonly"
              >
                <ng-template #selectedItemTemplateTest let-rowData>
                  <span class="address" [pTooltip]="rowData.label">
                    {{ rowData.label }}
                  </span>
                </ng-template>

                <jb-single-select-dropdown
                  appendTo="body"
                  [content]="addressDropdown"
                  [readOnlyValidator]="readonly"
                  [selectedItemTemplate]="selectedItemTemplateTest"
                ></jb-single-select-dropdown>
              </prc-form-label>

              <prc-form-label label="Delivery Date" class="form-label">
                <p-calendar
                  appendTo="body"
                  formControlName="deliveryDate"
                  placeholder="Date"
                  showButtonBar="true"
                  [dateFormat]="userDateFormat"
                  [inputStyleClass]="readonly ? 'jb-text readonly' : 'jb-text'"
                  [monthNavigator]="true"
                  [showIcon]="true"
                ></p-calendar>
              </prc-form-label>
            </ng-container>
          </div>

          <div class="column">
            <ng-container *ngIf="supplierLabels[deliveryMethod]">
              <prc-form-label label="PIC Details" class="form-label">
                <jb-textarea
                  [content]="remarkTextArea"
                  [readonly]="readonly"
                ></jb-textarea>
              </prc-form-label>

              <prc-form-label class="form-label" label="Airway Bill Number">
                <input
                  autocomplete="off"
                  class="jb-text"
                  formControlName="airwayBillNumber"
                  pInputText
                  [readonly]="readonly"
                />
              </prc-form-label>
            </ng-container>
          </div>
        </div>
      </form>
    </p-accordionTab>
  </p-accordion>
</div>
