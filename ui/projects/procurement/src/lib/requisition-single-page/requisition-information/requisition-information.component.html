<form class="formcontainer" [formGroup]="form">
  <div class="two-columns">
    <div class="column">
      <prc-form-label label="PO Type">
        <jb-single-select-dropdown
          [class.readonly]="!isEditMode"
          [content]="poTypesDropdown"
          [disabled]="
            readonly ||
            reviewMode ||
            !(permissions.EditGeneralInfo | hasPermissions) ||
            disablePoType
          "
        ></jb-single-select-dropdown>
      </prc-form-label>

      <prc-form-label label="Segments">
        <prc-label
          *ngFor="let uid of segmentUids"
          [label]="(uid | jcdsItem : 'segment' | async)?.segment_name"
        ></prc-label>
      </prc-form-label>
    </div>

    <div class="column">
      <prc-form-label label="Number Of Items">
        <input
          class="jb-text no-items"
          disabled="true"
          pInputText
          formControlName="totalItems"
          autocomplete="off"
        />
      </prc-form-label>
      
      <prc-form-label label="Department">
        <jb-single-select-dropdown
          [readOnlyValidator]="!isEditMode"
          [content]="departmentTypeDropdown"
          [disabled]="
            readonly || !(permissions.EditGeneralInfo | hasPermissions)
          "
        ></jb-single-select-dropdown>
      </prc-form-label>
    </div>
  </div>

  <prc-form-label label="Subject">
    <div class="borderPix">
      <jb-textarea [content]="subjectTxt"> </jb-textarea>
    </div>
  </prc-form-label>
</form>
