import { PrcFunctionCodes, PrcModuleCodes } from '@j3-procurement/dtos';
import { eGridColumnsWidth, IJbAttachment } from 'jibe-components';

export const attachmentConfig: IJbAttachment = {
  Module_Code: PrcModuleCodes.Procurement,
  Function_Code: PrcFunctionCodes.PoServiceAttachments,
  actions: [
    {
      name: 'Download',
      icon: 'icons8-download',
    },
    {
      name: 'Delete',
      icon: 'icons8-delete',
      color: 'red',
    },
  ],
  AttachCols: [
    {
      width: eGridColumnsWidth.ShortDescription,
      DisplayText: 'Name',
      FieldName: 'upload_file_name',
      IsMandatory: true,
      IsVisible: true,
      OrderBy: 1,
    },
    {
      width: eGridColumnsWidth.ShortDescription,
      DisplayText: 'Size (KB)',
      FieldName: 'size',
      IsMandatory: true,
      IsVisible: true,
      OrderBy: 2,
    },
    {
      width: eGridColumnsWidth.Date,
      DisplayText: 'Date Attached',
      FieldName: 'date_of_creation',
      IsMandatory: true,
      IsVisible: true,
      OrderBy: 3,
      FieldType: 'date',
    },
  ],
};
