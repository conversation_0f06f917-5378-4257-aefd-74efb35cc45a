<jb-dialog
  [dialogContent]="VoyageNoDialogContent"
  [(dialogVisible)]="voyageNoDialog"
  (closeDialog)="closeVoyageNoDialog()"
>
  <div jb-dialog-body>
    <div class="voyagenocontainer">
      <jb-grid
        *ngIf="voyageDetails$ | async as voyageDetails"
        gridName="voyageNumberGrid"
        [colData]="gridInputs.columns"
        [filterData]="gridInputs.filters"
        [filterListsSet]="gridInputs.filtersLists"
        [getStyleByContainer]="true"
        [paginator]="false"
        [searchFields]="gridInputs.searchFields"
        [selectionMode]="selectionMode"
        [tableData]="voyageDetails"
        (action)="selectedVoyageNoAction($event)"
      ></jb-grid>
    </div>
    <div class="p-grid-supgrid">
      <div class="p-col-6 left">
        <jb-button
          type="NoButton"
          class="moveleft"
          buttonType="button"
          label="Cancel"
          (click)="closeVoyageNoDialog()"
        ></jb-button>
      </div>
      <div class="p-col-6 right">
        <jb-button
          type="Standard"
          class="moveright"
          label="Select"
          (click)="selectVoyageNumber()"
        ></jb-button>
      </div>
    </div>
  </div>
</jb-dialog>
