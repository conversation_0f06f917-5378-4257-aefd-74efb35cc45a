<jb-grid
  [button]="gridInputs.gridButton"
  [colData]="gridInputs.columns"
  [filterData]="gridInputs.filters"
  [filterListsSet]="gridInputs.filtersLists"
  [getStyleByContainer]="true"
  [gridName]="gridInputs.gridName"
  [isDisplayAdvancedFilter]="false"
  [searchFields]="gridInputs.searchFields"
  [tableData]="gridInputs.data"
></jb-grid>
<ng-template #remainingBudgetTemplate let-rowData>
  <div class="template-column">
    <span
      *ngIf="rowData.remainingBudget"
      [class.remaining-budget]="rowData.remainingBudget < 0"
    >
      USD {{ rowData.remainingBudget }}
      <i
        class="icons8-high-priority-3 warning-icon"
        *ngIf="rowData.remainingBudget < 0"
      ></i>
    </span>
  </div>
</ng-template>
<ng-template #vesselTemplate let-rowData>
  <span *ngIf="rowData.vesselUid as vesselUid">{{
    vesselUid | vesselName | async
  }}</span>
</ng-template>
