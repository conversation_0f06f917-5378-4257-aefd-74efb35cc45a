<prc-right-pane [headerDetails]="headerDetails" (close)="onClose($event)">
  <prc-right-pane-details [itemList]="mainDetailsList"></prc-right-pane-details>
  <prc-right-pane-divider></prc-right-pane-divider>
  <div class="right-pane-content">
    <prc-form-label class="form-label full-size" label="Subject">
      <input
        class="jb-text readonly text-ellipsis"
        [prcTooltip]="requisitionSummary?.description"
        pInputText
        [value]="requisitionSummary?.description"
        readonly="true"
      />
    </prc-form-label>
    <prc-form-label class="form-label" label="Raised">
      <input
        class="jb-text readonly"
        pInputText
        [value]="requisitionSummary?.creationDate | jbDate"
        readonly="true"
      />
    </prc-form-label>
    <prc-form-label class="form-label" label="Assignee">
      <input
        class="jb-text readonly"
        pInputText
        [value]="requisitionSummary?.assigneeName"
        readonly="true"
      />
    </prc-form-label>
  </div>
  <prc-right-pane-divider></prc-right-pane-divider>
  <prc-right-pane-section
    class="right-pane-section"
    text="Quotation"
    iconClass="icons8-resume-template"
  >
    <div *ngFor="let rfq of rfqs; trackBy: trackByUid">
      <prc-right-pane-card
        *ngIf="rfq.supplierName"
        [title]="rfq.supplierName"
        [status]="rfq.status"
        [rfqStatus]="rfq.rfqStatusUid"
        [noQuotation]="rfq.noQuotation"
        [warnings]="rfq.warnings"
        [declineRemark]="rfq.declineRemark"
        [declineReasons]="rfq.declineReasons"
      >
        <div
          class="card-content-container"
          *ngIf="
            rfq.noQuotation ||
            (rfq.rfqStatusUid | prcGetRfqDisplayName | async) === 'Received'
          "
        >
          <div class="quotation-details">
            <prc-icon-text
              iconClass="icons8-shopping-cart-2"
              text="{{ rfq.amount }} {{ rfq.currencyCode }}"
            ></prc-icon-text>
            <prc-icon-text
              tooltip="Lead Days"
              iconClass="icons8-watch"
              text="{{ rfq.leadDays }} Days"
            ></prc-icon-text>
          </div>
          <div class="delivery-details">
            <div>{{ rfq.deliveryPortName }}</div>
            <div>{{ rfq.deliveryDate | jbDate }}</div>
          </div>
        </div>
      </prc-right-pane-card>
    </div>
  </prc-right-pane-section>
</prc-right-pane>
