import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ControlContainer } from '@angular/forms';
import { Label } from '@j3-procurement/dtos/label';
import { AgentType, VesselMovement } from '@j3-procurement/dtos/po';
import { SupplierDto } from '@j3-procurement/dtos/supplier';
import {
  CentralizedDataService,
  eDateFormats,
  IJbTextArea,
  ISingleSelectDropdown,
  JbControlOutputService,
  WebApiRequest,
} from 'jibe-components';
import f from 'odata-filter-builder';
import { filter, startWith, takeUntil } from 'rxjs/operators';

import {
  transformDateFormat,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { IJbCalender } from 'jibe-components';
import { Port } from '../../models/interfaces/port';
import { OperationsService } from '../../services/operations.service';
import { SupplierService } from '../../services/supplier/supplier.service';
import {
  extractDropdownData,
  getDropdownControlName,
} from '../../utils/label-utils';
import { GenericPort } from '../generic-port-select/generic-port';
import { UpcomingPort } from '../upcoming-port-select/upcoming-port';
import { DeliveryPortForm, DeliveryPortFormKey } from './delivery-port-form';
import {
  dateContent,
  getAgentDetailsTextArea,
  getAgentTypeDropdown,
  getRemarkTextArea,
  getVesselMovementDropdown,
} from './delivery-port-form-dropdowns';

@Component({
  selector: 'prc-delivery-port-form',
  templateUrl: './delivery-port-form.component.html',
  styleUrls: ['./delivery-port-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeliveryPortFormComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() forceShowTwoColumns: boolean;
  @Input() port: Port;
  @Input() readonly: boolean;
  @Input() vesselId: number;
  public etaContent: IJbCalender = {
    ...dateContent,
    showTime: true,
    id: 'eta',
  };
  public etdContent: IJbCalender = {
    ...dateContent,
    showTime: true,
    id: 'etd',
  };
  public requestedDeliveryContent: IJbCalender = {
    ...dateContent,
    id: 'requestedDelivery',
  };
  public agentDetailsTextArea: IJbTextArea = getAgentDetailsTextArea();
  public agentTypeDropdown: ISingleSelectDropdown = getAgentTypeDropdown();
  public agentUid: string;
  public deliveryPortForm: TypedFormGroup<DeliveryPortForm>;
  public remarkTextArea: IJbTextArea = getRemarkTextArea();
  public agentListRequest: WebApiRequest;
  public userDateFormat: string;

  public vesselMovemement: VesselMovement;
  public vesselMovementDropdown: ISingleSelectDropdown =
    getVesselMovementDropdown();

  private agentTypeIdMap: Partial<Record<AgentType, string>>;

  constructor(
    private readonly operationsService: OperationsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly cds: CentralizedDataService,
    private readonly controlContainer: ControlContainer,
    private readonly jbControlService: JbControlOutputService,
    private readonly supplierService: SupplierService
  ) {
    super();
  }

  ngOnInit(): void {
    this.deliveryPortForm = this.controlContainer
      .control as TypedFormGroup<DeliveryPortForm>;

    const userDateFormat = this.cds.userDetails.Date_Format?.toUpperCase();
    this.userDateFormat = transformDateFormat(
      userDateFormat ?? eDateFormats.DefaultFormat
    );
    this.etaContent.calenderFormat = userDateFormat;
    this.etdContent.calenderFormat = userDateFormat;
    this.requestedDeliveryContent.calenderFormat = userDateFormat;

    this.agentListRequest = this.supplierService.getAgentListReq();

    this.updateAgentTypeDropdown({
      selectedValue: this.deliveryPortForm.value.agentType,
    });

    this.setSubscriptions();
  }

  public async getAgentId(agentType: AgentType): Promise<string> {
    if (!this.agentTypeIdMap) {
      const portId = this.deliveryPortForm.controls.portId.value;
      const { records } = portId
        ? await this.operationsService.getUpcomingPorts({
            filter: f().eq('Port_ID', portId),
          })
        : { records: [] };
      this.setAgentTypeIdMap(records[0]);
    }

    return this.agentTypeIdMap[agentType];
  }

  public async onAgentChange(supplier: SupplierDto): Promise<void> {
    if (!supplier) {
      return;
    }

    const { agentDetails, agentName } = await this.supplierService.getAgent(
      supplier.uid
    );
    this.deliveryPortForm.patchValue({
      agentDetail: agentDetails,
      agentName,
      agentUid: supplier.uid,
    });

    this.cdr.markForCheck();
  }

  public onDeliveryPortChange(event: GenericPort): void {
    if (!event) {
      return;
    }

    this.port = {
      country: event.port_country,
      name: event.port_name,
    };

    this.deliveryPortForm.patchValue({
      portId: event.Port_ID ?? event.PORT_ID,
      portName: event.port_name,
      portCountry: event.port_country,
      portCode: event.UN_LOCODE,
    });
  }

  public onUpcomingPortChange(event: UpcomingPort): void {
    if (!event) {
      return;
    }

    const { Port_ID, Port_Name, arrival, country, departure_date } = event;
    const vesselEta = arrival && new Date(arrival);
    const vesselEtd = departure_date && new Date(departure_date);

    this.setAgentTypeIdMap(event);
    const agentType = this.deliveryPortForm.controls.agentType.value;
    if (agentType) {
      this.setAgentData(agentType);
    }

    this.port = {
      country: country,
      name: Port_Name,
    };

    const patchValues: Partial<DeliveryPortForm> = {
      portCountry: country,
      portId: Port_ID,
      portName: Port_Name,
      portCode: event.UN_LOCODE,
      requestedDeliveryDate: vesselEta,
      vesselEta,
      vesselEtd,
    };

    this.deliveryPortForm.patchValue(patchValues);
  }

  private async setAgentData(agentType: AgentType): Promise<void> {
    const agentId = await this.getAgentId(agentType);
    const agent =
      agentId && (await this.supplierService.getAgentBySupplierId(agentId));

    const { agentName: name, agentDetails, uid } = agent ?? {};

    this.deliveryPortForm.patchValue({
      agentDetail: agentDetails,
      agentName: name,
      agentUid: uid,
    });

    this.cdr.markForCheck();
  }

  private setAgentTypeIdMap(port: UpcomingPort): void {
    const { Owners_ID, Charter_ID } = port ?? {};

    this.agentTypeIdMap = {
      owner: Owners_ID,
      charterer: Charter_ID,
    };
  }

  private setSubscriptions(): void {
    const { controls } = this.deliveryPortForm;

    this.jbControlService.dynamicControl
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((dropdown: ISingleSelectDropdown) => {
          const controlName = getDropdownControlName(dropdown);
          return this.deliveryPortForm.contains(controlName);
        })
      )
      .subscribe((dropdown: ISingleSelectDropdown) => {
        const [controlName, label] = extractDropdownData(dropdown);

        controls[controlName].markAsDirty();
        this.deliveryPortForm
          .get(controlName as DeliveryPortFormKey)
          .patchValue((label as Label)?.uid);
      });

    controls.agentType.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async (agentType) => {
        this.updateAgentTypeDropdown({ selectedValue: agentType });

        if (this.deliveryPortForm.dirty && agentType) {
          this.setAgentData(agentType);
        }

        this.cdr.markForCheck();
      });

    controls.agentUid.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(controls.agentUid.value)
      )
      .subscribe((agentUid) => {
        if (agentUid) {
          controls.agentDetail.enable();
        } else {
          controls.agentDetail.disable();
        }
      });

    controls.movement.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(controls.movement.value),
        filter((movement) => this.vesselMovemement !== movement)
      )
      .subscribe((movement) => {
        this.vesselMovemement = movement;
        this.updateVesselMovementDropdown({ selectedValue: movement });

        if (!movement) {
          this.deliveryPortForm.reset();
          this.port = undefined;
        }

        this.cdr.markForCheck();
      });

    controls.portId.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(controls.portId.value)
      )
      .subscribe((portId: number) => {
        if (portId) {
          controls.agentType.enable();
        } else {
          controls.agentType.disable();
        }
      });

    controls.vesselEta.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(controls.vesselEta.value)
      )
      .subscribe((vesselEta) => {
        this.etdContent.calendarMin = vesselEta;
        this.requestedDeliveryContent.calendarMin = vesselEta;
        this.cdr.markForCheck();
      });

    controls.vesselEtd.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        startWith(controls.vesselEtd.value)
      )
      .subscribe((vesselEtd) => {
        this.etaContent.calendarMax = vesselEtd;
        this.requestedDeliveryContent.calendarMax = vesselEtd;
        this.cdr.markForCheck();
      });
  }

  private updateAgentTypeDropdown(
    config: Partial<ISingleSelectDropdown>
  ): void {
    this.agentTypeDropdown = {
      ...this.agentTypeDropdown,
      ...config,
    };
  }

  private updateVesselMovementDropdown(
    config: Partial<ISingleSelectDropdown>
  ): void {
    this.vesselMovementDropdown = {
      ...this.vesselMovementDropdown,
      ...config,
    };
  }
}
