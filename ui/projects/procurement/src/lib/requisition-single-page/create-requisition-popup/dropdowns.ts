import { getJCDSDataReq } from 'j3-prc-components';
import { IMultiSelectDropdown, ISingleSelectDropdown } from 'jibe-components';
import f from 'odata-filter-builder';

export const getItemListDropdown = (): IMultiSelectDropdown => ({
  label: 'name',
  value: 'uid',
  id: 'itemListUids',
  isValidation: false,
  selectedValues: [],
  dataSource: [],
});

export const urgencyDropdown: ISingleSelectDropdown = {
  label: 'display_name',
  value: 'uid',
  id: 'urgency',
  isValidation: true,
  apiRequest: getJCDSDataReq('Urgencys', {
    filter: f().eq('active_status', true),
    count: 'false',
    orderby: 'urgencys',
  }),
};
