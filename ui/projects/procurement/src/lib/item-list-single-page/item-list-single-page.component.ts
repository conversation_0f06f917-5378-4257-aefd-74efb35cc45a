import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  Inject,
  OnInit,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { IJbAttachment, ITaskMangerDetails } from 'jibe-components';
import { MenuItem } from 'primeng';
import { Observable, Subject } from 'rxjs';

import {
  CreateItemListDto,
  ItemListBaseDto,
  ItemListDto,
  ItemListStatus,
} from '@j3-procurement/dtos/item-list';

import { PrcFunctionCodes, PrcModuleCodes } from '@j3-procurement/dtos';
import { SegmentType } from '@j3-procurement/dtos';
import { IdLabel } from '@j3-procurement/dtos/label';
import {
  ExtNavigationService,
  ModalDialogOpenParameters,
  ModalDialogService,
  NotificationService,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { takeUntil } from 'rxjs/operators';
import {
  DEFAULT_LIST_ACTIVE_STATUS,
  DEFAULT_SEPARATOR,
} from '../models/constants';
import { eApiResponseType } from '../models/enums/prc-api-response-type.enum';
import {
  ePrcConfirmLabel,
  ePrcErrorMessages,
  ePrcModalMessages,
  ePrcSuccessMessages,
  ePrcWarnMessages,
} from '../models/enums/prc-messages.enum';
import { ePrcPermission } from '../models/enums/prc-permission.enum';
import { ePageRoutes } from '../models/enums/prc-routes.enum';
import { ePrcWorklistType } from '../models/enums/prc-worklist-type.enum';
import { IDiscussionWorkflow } from '../models/interfaces/discussion.interface';
import { FeatureSwitch } from '../models/interfaces/feature-switch';
import { HeaderStatus } from '../models/interfaces/header-status';
import { Vessel } from '../models/interfaces/vessel';
import { FEATURE_SWITCH } from '../services/feature-switch';
import { FeedDiscussionService } from '../services/feed-discussion.service';
import { PrcHelpMaterialService } from '../services/help-button/prc-help-material.service';
import { ItemListService } from '../services/item-list/item-list.service';
import { PermissionService } from '../services/permission/permission.service';
import { SidebarMenuService } from '../services/sidebar-menu/sidebar-menu.service';
import { VesselService } from '../services/vessel/vessel.service';
import { CreateItemListPopupData } from '../shared/create-item-list-popup/types';
import { FormStateManager } from '../shared/form-state-manager';
import { HeaderButton } from '../shared/generic-header/header-button';
import { HeaderSection } from '../shared/generic-header/header-section';
import { JobEvent } from '../shared/single-page-sections/job-section/types';
import { getEnvironmentByRecordNumber } from '../utils/entity-environment';
import { WorkflowService } from '../workflow/services/workflow.service';
import {
  attachmentActions,
  createRequisitionAllowedStatuses,
  createRequistionDisabledTooltip,
  DUPLICATE_ITEM_LIST_CONFIRMATION_HEADER,
  DUPLICATE_ITEM_LIST_CONFIRMATION_TEXT,
  headerControlKeys,
  headerMenuItemLabel,
} from './constants';
import { ItemEvent } from './item-list-items/types';
import { ItemListHeaderStatuses } from './item-list-status';
import { RouteConstants } from './route-constants';
import {
  defaultSectionEditMode,
  SectionEditMode,
  SectionKey,
} from './section-edit-mode';
import { Section, sidebarMenu, View } from './sidebar-menu';
import { ItemListForm, MenuItemConfig } from './types';

@Component({
  selector: 'prc-item-list-single-page',
  templateUrl: './item-list-single-page.component.html',
  styleUrls: ['./item-list-single-page.component.scss'],
  providers: [PrcHelpMaterialService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ItemListSinglePageComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public currentView: View = 'list-details';
  public confirmationDialogParams: ModalDialogOpenParameters = {
    confirmButtonLabel: ePrcConfirmLabel.Confirm,
    text: DUPLICATE_ITEM_LIST_CONFIRMATION_TEXT,
    jbDialog: {
      dialogHeader: DUPLICATE_ITEM_LIST_CONFIRMATION_HEADER,
    },
  };
  public createRequisitionDto: Pick<
    ItemListBaseDto,
    'name' | 'urgency' | 'vesselDepartment'
  >;
  public disabledFeature = {
    browseItems: true,
    browseJobs: true,
  };
  public duplicateItemListPopupData: CreateItemListPopupData;
  public feedDetail: IDiscussionWorkflow;
  public functionCode: string = PrcFunctionCodes.ItemListDetails;
  public headerButtons: HeaderButton[];
  public headerMenu: MenuItem[];
  public headerSections: HeaderSection[];
  public isDialogConfirmVisible = false;
  public itemEvent$: Observable<ItemEvent>;
  public itemListCount: number;
  public itemListCreateDate: Date;
  public itemListForm: TypedFormGroup<ItemListForm>;
  public itemListNumber: string;
  public itemListUid: string;
  public linkedRecordsDetails: ITaskMangerDetails;
  public jobEvent$: Observable<JobEvent>;
  public moduleCode: string = PrcModuleCodes.Procurement;
  public sectionEditMode: SectionEditMode = { ...defaultSectionEditMode };
  public attachmentConfig: IJbAttachment;
  public headerStatus: HeaderStatus = ItemListHeaderStatuses.Active;
  public segmentUids: string[];
  public vessel: Vessel;

  public canContribute = false;
  public canCreateRequisition = false;
  public canSave = false;
  public refreshMachinery$ = new Subject();

  private handover?: boolean;
  private itemEventSubject = new Subject<ItemEvent>();
  private itemListTimestamp: Date;
  private jobEventSubject = new Subject<JobEvent>();
  private formStateManager: FormStateManager<ItemListForm>;

  @HostListener('window:beforeunload')
  canDeactivate(): boolean {
    return !this.itemListForm.dirty;
  }

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly extNavigationService: ExtNavigationService,
    private readonly feedDiscussionService: FeedDiscussionService,
    @Inject(FEATURE_SWITCH) private readonly featureSwitch: FeatureSwitch,
    private readonly formBuilder: FormBuilder,
    private readonly helpMaterialService: PrcHelpMaterialService,
    private readonly itemListService: ItemListService,
    private readonly modalDialogService: ModalDialogService,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly router: Router,
    private readonly sidebarMenuService: SidebarMenuService<View, Section>,
    private readonly vesselService: VesselService,
    private readonly workflowService: WorkflowService
  ) {
    super();
    this.itemListForm = this.formBuilder.group<ItemListBaseDto>({
      assignedTo: [],
      contributors: [],
      name: ['', Validators.required],
      description: [],
      items: [],
      jobs: [],
      owner: [null, Validators.required],
      status: [],
      tags: [],
      tasks: [[]],
      urgency: [],
      vesselDepartment: [],
      viewers: [],
    });
    this.itemEvent$ = this.itemEventSubject.asObservable();
    this.jobEvent$ = this.jobEventSubject.asObservable();
  }

  ngOnInit(): void {
    const { snapshot } = this.activatedRoute;
    const itemListUid = snapshot.params[RouteConstants.itemListUid];
    const view = snapshot.queryParams[RouteConstants.view];
    this.itemListUid = itemListUid;

    this.checkAccess();
    this.formStateManager = new FormStateManager(
      this.itemListForm,
      headerControlKeys
    );

    this.initializeAttachments(this.itemListUid);
    this.loadSidebarMenu(view);
    this.loadData();
    this.helpMaterialService.init(PrcFunctionCodes.ItemListSinglePage);
  }

  public async completeItemList(): Promise<void> {
    this.changeStatus('Completed');
    this.setHeaderButtons();
  }

  public async navigateToGeneralBrowsing(
    segmentTypes: SegmentType | SegmentType[]
  ): Promise<void> {
    if (!(await this.dirtyCheckAndSave())) {
      return;
    }

    await this.itemListService
      .changeActiveItemList(this.vessel?.uid, this.itemListUid)
      .toPromise()
      .catch((_) => {
        /**  ignore api errors when the vesselUid is undefined or if itemList does not belong to vessel */
      });

    this.router.navigate(
      [ePageRoutes.GeneralBrowsing, 'item-list', this.itemListUid],
      { queryParams: { segmentTypes, vesselUid: this.vessel?.uid } }
    );
  }

  public onCloseCreateRequisitionPopup(): void {
    this.workflowService.markTransition(this.itemListUid, false);
  }

  public onCloseDuplicateItemListPopup(data?: CreateItemListDto): void {
    if (data) {
      this.duplicateItemList(data);
    } else {
      this.duplicateItemListPopupData = undefined;
    }
  }

  public onHandoverChange(handover: boolean): void {
    this.handover = handover;
    this.setHeaderButtons();
  }

  public async saveItemList(showNotification = true): Promise<void> {
    if (this.itemListForm.invalid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }

    const itemList = this.itemListForm.getRawValue();

    try {
      this.setHeaderButtons(true /* disableSave */);
      await this.updateItemList(itemList);
      this.formStateManager.updateOriginalFormValue();
      this.itemListForm.markAsPristine();
      if (showNotification) {
        this.notificationService.success(
          ePrcSuccessMessages.ItemListSaveSuccess
        );
      }
    } catch (err) {
      const { message, name } = err.error;
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message
      );
    } finally {
      this.setHeaderButtons();
      this.cdr.markForCheck();
    }
  }

  public setSectionEditMode(key: SectionKey): void {
    this.sectionEditMode = { ...defaultSectionEditMode, [key]: true };
  }

  private changeStatus(status: ItemListStatus): void {
    this.itemListForm.patchValue({ status });
    this.setHeaderMenuAndStatus();
  }

  private async checkAccess(): Promise<void> {
    this.canCreateRequisition = await this.permissionService.hasPermissions(
      ePrcPermission.ListCreateRequisition
    );
    this.canSave = await this.permissionService.hasAtLeastOnePermission([
      ePrcPermission.ListAddEditChecklist,
      ePrcPermission.ListAddItems,
      ePrcPermission.ListAddServices,
      ePrcPermission.ListDeleteItems,
      ePrcPermission.ListDeleteServices,
      ePrcPermission.ListEditContributingTeam,
      ePrcPermission.ListEditListGeneralDetails,
      ePrcPermission.ListEditListUrgency,
    ]);
  }

  private async createHeaderMenuItem([
    key,
    permission,
    command,
  ]: MenuItemConfig): Promise<MenuItem> {
    return {
      label: headerMenuItemLabel[key],
      disabled: !(await this.permissionService.hasPermissions(permission)),
      command,
    };
  }

  private async dirtyCheckAndSave(): Promise<boolean> {
    if (!this.itemListForm.dirty) {
      return true;
    }

    const confirmed = await this.modalDialogService.openDialog({
      jbDialog: { dialogHeader: ePrcConfirmLabel.Save },
      text: ePrcWarnMessages.SaveListConfirmation,
      confirmButtonLabel: ePrcConfirmLabel.Save,
      rejectButtonLabel: ePrcConfirmLabel.Cancel,
    });

    if (confirmed) {
      await this.saveItemList(false);
    }

    return confirmed;
  }

  private disableControls(controlKeys: (keyof ItemListBaseDto)[]): void {
    controlKeys.forEach((key) => this.itemListForm.controls[key].disable());
  }

  private disableForm(): void {
    this.itemListForm.disable({ emitEvent: false });
    this.setDisabledFeature();
  }

  private async duplicateItemList(itemList: CreateItemListDto): Promise<void> {
    try {
      const uid = await this.itemListService.cloneItemList(
        this.itemListUid,
        itemList
      );
      this.extNavigationService.navigate([ePageRoutes.ItemListSingle, uid]);
      this.duplicateItemListPopupData = undefined;
      this.cdr.markForCheck();
    } catch (err) {
      this.notificationService.error(err?.message);
    }
  }

  private async enableForm(): Promise<void> {
    if (!this.canContribute) {
      return this.disableForm();
    }

    this.itemListForm.enable({ emitEvent: false });

    const [
      hasListAddEditChecklistPermission,
      hasListEditListGeneralDetailsPermission,
      hasListEditContributingTeamPermission,
      hasListEditListUrgencyPermission,
    ] = await Promise.all([
      this.permissionService.hasPermissions(
        ePrcPermission.ListAddEditChecklist
      ),
      this.permissionService.hasPermissions(
        ePrcPermission.ListEditListGeneralDetails
      ),
      this.permissionService.hasPermissions(
        ePrcPermission.ListEditContributingTeam
      ),
      this.permissionService.hasPermissions(ePrcPermission.ListEditListUrgency),
    ]);

    if (!hasListAddEditChecklistPermission) {
      this.disableControls(['tasks']);
    }

    if (!hasListEditListGeneralDetailsPermission) {
      this.disableControls([
        'assignedTo',
        'description',
        'name',
        'status',
        'tags',
        'vesselDepartment',
      ]);
    }

    if (!hasListEditContributingTeamPermission) {
      this.disableControls(['contributors', 'owner', 'viewers']);
    }

    if (!hasListEditListUrgencyPermission) {
      this.disableControls(['urgency']);
    }

    this.setDisabledFeature();
  }

  private initializeAttachments(id: string): void {
    this.attachmentConfig = {
      Module_Code: PrcModuleCodes.Procurement,
      Function_Code: PrcFunctionCodes.ItemListAttachments,
      Key1: id,
    };
  }

  private isBrowseButtonDisabled(
    controlDisabled: boolean,
    hasPermission: boolean
  ): boolean {
    return controlDisabled || !(hasPermission && this.canContribute);
  }

  private async loadData(): Promise<void> {
    const [itemList] = await Promise.all([
      this.itemListService.getItemList(this.itemListUid).toPromise(),
      this.vesselService.getVessels().toPromise(),
      this.workflowService.getActionState(this.itemListUid, false).toPromise(), // get (or insert) Transition
    ]);

    await this.setItemListData(itemList);
    this.formStateManager.updateOriginalFormValue();
    this.setFeedAndDiscussionDetails();
    this.setLinkedRecordsDetails();
    this.cdr.markForCheck();
  }

  private getCreateRequisitionButtonTooltip(): string {
    const status = this.itemListForm.get('status').value;
    const createRequisitionConditions: [boolean, string][] = [
      [this.canContribute, createRequistionDisabledTooltip.notContributor],
      [this.canCreateRequisition, createRequistionDisabledTooltip.noPermission],
      [
        createRequisitionAllowedStatuses.includes(status),
        createRequistionDisabledTooltip.otherListStatus,
      ],
      [this.itemListCount > 0, createRequistionDisabledTooltip.noItems],
    ];

    return createRequisitionConditions
      .map(([condition, tooltip]) => !condition && tooltip)
      .filter(Boolean)
      .join(DEFAULT_SEPARATOR);
  }

  private async hasRoles(owner: IdLabel, roles: IdLabel[]): Promise<boolean> {
    return (
      !this.featureSwitch.enableContributingTeamMemberCheck ||
      !roles?.length ||
      (await this.permissionService.hasAtLeastOneRole([
        ...(owner?.id ? [owner.id] : []),
        ...roles.map(({ id }) => id),
      ]))
    );
  }

  private loadSidebarMenu(view: View = 'list-details'): void {
    this.currentView = view;
    this.sidebarMenuService.init(sidebarMenu, this.componentDestroyed$);
    this.sidebarMenuService.selectedView$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((nextView: View) => {
        if (nextView === this.currentView) {
          return;
        }
        const hasChanges = this.formStateManager.hasViewChanges();

        if (hasChanges) {
          if (!confirm(ePrcModalMessages.UnsavedChanges)) {
            return;
          }
          this.formStateManager.discardViewChanges();
        }

        this.sectionEditMode = { ...defaultSectionEditMode };
        this.currentView = nextView;
        this.cdr.markForCheck();
      });
    const openMenu = sidebarMenu.find((menu) => menu.id === this.currentView);
    if (openMenu) {
      openMenu.isOpen = true;
    }
  }

  private async refreshSegments(): Promise<void> {
    this.segmentUids = await this.itemListService
      .getItemListSegments(this.itemListUid)
      .toPromise();
    this.cdr.markForCheck();
  }

  private async setDisabledFeature(): Promise<void> {
    const [canAddItems, canAddJobs] = await Promise.all([
      this.permissionService.hasPermissions(ePrcPermission.ListAddItems),
      this.permissionService.hasPermissions(ePrcPermission.ListAddServices),
    ]);

    this.disabledFeature = {
      browseItems: this.isBrowseButtonDisabled(
        this.itemListForm.controls.items.disabled,
        canAddItems
      ),
      browseJobs: this.isBrowseButtonDisabled(
        this.itemListForm.controls.jobs.disabled,
        canAddJobs
      ),
    };

    this.cdr.markForCheck();
  }

  private setFeedAndDiscussionDetails(): void {
    const entityEnvironment = getEnvironmentByRecordNumber(this.itemListNumber);
    this.feedDetail = this.feedDiscussionService.getDetails(
      this.itemListUid,
      this.vessel?.vesselId,
      entityEnvironment,
      ePrcWorklistType.ItemList,
      this.itemListNumber
    );
  }

  private setHeaderButtons(disableSave = false): void {
    const tooltip = this.getCreateRequisitionButtonTooltip();
    this.headerButtons = [
      {
        buttonType: 'NoButton',
        command: async () => await this.showCreateRequisitionPopup(),
        label: 'Create requisition',
        buttonClass: 'save',
        disabled: Boolean(tooltip),
        tooltip,
      },
      {
        buttonType: 'Standard',
        command: () => this.saveItemList(),
        label: 'Save list',
        buttonClass: 'save',
        disabled: disableSave || this.handover || !this.canSave,
      },
    ];
  }

  private async setHeaderMenu(menuItemConfig: MenuItemConfig[]): Promise<void> {
    this.headerMenu = await Promise.all(
      menuItemConfig.map((config) => this.createHeaderMenuItem(config))
    );
    this.cdr.markForCheck();
  }

  private setHeaderMenuAndStatus(): void {
    const status: ItemListStatus = this.itemListForm.get('status').value;
    this.headerMenu = [];

    switch (status) {
      case 'Active':
        this.headerStatus = ItemListHeaderStatuses.Active;
        this.enableForm();
        this.setHeaderButtons();
        this.setHeaderMenu([
          [
            'markAsReady',
            ePrcPermission.ListEditListGeneralDetails,
            () => this.changeStatus('Ready'),
          ],
          [
            'discard',
            ePrcPermission.ListEditListGeneralDetails,
            () => this.changeStatus('Discarded'),
          ],
          [
            'duplicate',
            ePrcPermission.ListCreateList,
            () => this.showDuplicateItemListPopup(),
          ],
        ]);
        this.attachmentConfig.actions = attachmentActions;
        break;
      case 'Discarded':
        this.headerStatus = ItemListHeaderStatuses.Discarded;
        this.disableForm();
        this.setHeaderButtons();
        this.setHeaderMenu([
          [
            'activate',
            ePrcPermission.ListEditListGeneralDetails,
            () => this.changeStatus('Active'),
          ],
          [
            'duplicate',
            ePrcPermission.ListCreateList,
            () => this.showDuplicateItemListPopup(),
          ],
        ]);
        this.attachmentConfig.actions = [];
        break;
      case 'Ready':
        this.headerStatus = ItemListHeaderStatuses.Ready;
        this.enableForm();
        this.setHeaderButtons();
        this.setHeaderMenu([
          [
            'activate',
            ePrcPermission.ListEditListGeneralDetails,
            () => this.changeStatus('Active'),
          ],
          [
            'discard',
            ePrcPermission.ListEditListGeneralDetails,
            () => this.changeStatus('Discarded'),
          ],
          [
            'duplicate',
            ePrcPermission.ListCreateList,
            () => this.showDuplicateItemListPopup(),
          ],
        ]);
        this.attachmentConfig.actions = attachmentActions;
        break;
      case 'Completed':
        this.headerStatus = ItemListHeaderStatuses.Completed;
        this.disableForm();
        this.setHeaderButtons(true);
        this.setHeaderMenu([
          [
            'duplicate',
            ePrcPermission.ListCreateList,
            () => this.showDuplicateItemListPopup(),
          ],
        ]);
        this.attachmentConfig.actions = [];
        break;
    }

    this.cdr.markForCheck();
  }

  private setHeaderSections(): void {
    this.headerSections = [
      {
        label: this.vessel?.name,
        labelClass: '',
        labelColor: 'var(--jbGreyBlue500)',
        iconClass: 'icons8-water-transportation',
        iconColor: 'var(--jbBrandBlue500)',
      },
    ];

    if (this.itemListNumber) {
      this.headerSections.push({
        label: this.itemListNumber,
        labelClass: '',
        labelColor: 'var(--jbBrandBlue500)',
        iconClass: 'icons8-document-4',
        iconColor: '',
      });
    }
  }

  private async setItemListData(itemList: ItemListDto): Promise<void> {
    const { owner } = itemList;
    const canView = await this.hasRoles(owner, itemList.viewers);

    if (!canView) {
      this.notificationService.error(ePrcWarnMessages.NoPermissionToViewList);
      this.router.navigate([ePageRoutes.ItemListMain]);
      return;
    }

    this.itemListCount = itemList.itemCount;
    this.itemListCreateDate = new Date(itemList.createDate);
    this.itemListNumber = itemList.listNumber;
    this.itemListTimestamp = itemList.timestamp;
    this.segmentUids = itemList.segmentUids;
    this.vessel = this.vesselService.getVesselByUid(itemList.vesselUid);

    this.canContribute = await this.hasRoles(owner, itemList.contributors);
    this.itemListForm.patchValue({
      ...itemList,
      status: itemList.status ?? DEFAULT_LIST_ACTIVE_STATUS,
    });
    this.setHeaderMenuAndStatus();
    this.setHeaderSections();

    this.cdr.markForCheck();
  }

  private setLinkedRecordsDetails(): void {
    this.linkedRecordsDetails = {
      function_code: this.functionCode,
      module_code: this.moduleCode,
      uid: this.itemListUid,
      Vessel_ID: this.vessel.vesselId,
      Vessel_Name: this.vessel.name,
      vessel_uid: this.vessel.uid,
      WL_TYPE: ePrcWorklistType.ItemList,
    };
  }

  private async showCreateRequisitionPopup(): Promise<void> {
    const saved = await this.dirtyCheckAndSave();
    if (!saved) {
      return;
    }
    try {
      await this.workflowService.markTransition(this.itemListUid);
      const { name, urgency, vesselDepartment } = this.itemListForm.value;
      this.createRequisitionDto = { name, urgency, vesselDepartment }; // show popup
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message || err.message
      );
    }
    this.cdr.markForCheck();
  }
  private showDuplicateItemListPopup(): void {
    const { name, owner, vesselDepartment, urgency } = this.itemListForm.value;
    this.duplicateItemListPopupData = {
      name,
      owner,
      urgency,
      vesselDepartment,
      vesselUid: this.vessel.uid,
    };
  }

  private async updateItemList(itemList: ItemListBaseDto): Promise<void> {
    const { timestamp, total } = await this.itemListService
      .updateItemList(this.itemListUid, {
        ...itemList,
        timestamp: this.itemListTimestamp,
      })
      .toPromise();
    this.itemListTimestamp = timestamp;

    if (total !== undefined && total !== this.itemListCount) {
      this.itemListCount = total;
      this.refreshSegments();
      this.refreshMachinery$.next(true);
      this.itemEventSubject.next({ type: 'refreshGrid' });
      this.jobEventSubject.next({ type: 'refreshGrid' });
    }
    this.itemListForm.get('items').reset();
    this.itemListForm.get('jobs').reset();

    this.setHeaderMenuAndStatus();
  }
}
