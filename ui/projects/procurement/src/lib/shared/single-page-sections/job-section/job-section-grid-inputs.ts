import {
  eColor,
  eFieldControlType,
  eGridCellType,
  eGridColumnsWidth,
  eIconNames,
  GridRowActions,
} from 'jibe-components';

import { createColumns } from 'j3-prc-components';
import { eGridRowActions } from './grid-constants';
import { ColumnKey } from './types';

export const gridName = 'jobSection';

export const columns = createColumns<string, ColumnKey>([
  ['#', 'runningNumber', { width: eGridColumnsWidth.ShortNumber }],
  ['Component', 'catalogPath', { width: eGridColumnsWidth.LongDescription }],
  [
    'Code',
    'code',
    { width: eGridColumnsWidth.LongDescription, hyperlink: true },
  ],
  ['Title', 'jobName', { width: eGridColumnsWidth.LongDescription }],
  ['Description', 'description', { width: eGridColumnsWidth.LongDescription }],
  [
    'Due Date',
    'dueDate',
    {
      ControlType: eFieldControlType.Date,
      FieldType: eGridCellType.Date,
      width: eGridColumnsWidth.Date,
    },
  ],
]);

export const additionalColumns = createColumns<string, ColumnKey>(
  [
    ['Job Action', 'action', { width: eGridColumnsWidth.LongDescription }],
    ['Brand / Make', 'brandName', { width: eGridColumnsWidth.LongDescription }],
    ['Model', 'modelNumber', { width: eGridColumnsWidth.LongDescription }],
    ['Department ', 'department', { width: eGridColumnsWidth.LongDescription }],
  ],
  { IsVisible: false }
);

export const searchFields: ColumnKey[] = [
  'code',
  'jobName',
  'description',
  'catalogName',
];

export function getActions(isRemoveServiceDisabled = true): GridRowActions[] {
  return [
    {
      name: eGridRowActions.RemoveService,
      icon: eIconNames.Cancel,
      color: eColor.JbRed,
      disabled: isRemoveServiceDisabled,
    },
  ];
}
