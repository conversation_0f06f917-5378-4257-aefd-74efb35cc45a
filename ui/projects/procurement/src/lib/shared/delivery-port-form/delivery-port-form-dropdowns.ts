import { AgentType, VesselMovement } from '@j3-procurement/dtos/po';
import {
  eApiBase,
  eCrud,
  eDateTimeZoneRepresentation,
  eEntities,
  IJbCalender,
  IJbTextArea,
  ISingleSelectDropdown,
} from 'jibe-components';
import ODataFilterBuilder from 'odata-filter-builder';

import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { JbDropdownOption } from '../../models/interfaces/jb-dropdown-option';

export function getAgentDetailsTextArea(): IJbTextArea {
  return {
    placeholder: '',
    id: 'agentDetail',
    maxlength: 2000,
    rows: 5,
    tooltipValue: 'Text Range: Minimum: 0 Maximum: 2000',
  };
}

export function getAgentTypeDropdown(): ISingleSelectDropdown {
  const dataSource: JbDropdownOption<AgentType>[] = [
    { label: 'Owner', value: 'owner' },
    { label: 'Charterer', value: 'charterer' },
  ];
  return { id: 'agentType', dataSource };
}

export function getDeliveryPortDropdown(): ISingleSelectDropdown {
  return {
    label: 'port_name',
    value: 'PORT_ID',
    id: 'port_name',
    isValidation: false,
    apiRequest: {
      apiBase: eApiBase.MasterAPI,
      entity: eEntities.Library,
      crud: eCrud.Post,
      params: `libraryCode=port`,
      action: ePrcRequestAction.GetLibraryDataByCode,
      odata: {
        filter: new ODataFilterBuilder().eq('active_status', true),
        count: 'false',
        orderby: 'port_name',
      },
    },
  };
}

export function getRemarkTextArea(): IJbTextArea {
  return {
    id: 'remark',
    maxlength: 1000,
    rows: 5,
    tooltipValue: 'Text Range: Minimum: 0 Maximum: 1000',
    placeholder: 'Add your comments here',
  };
}

export function getVesselMovementDropdown(): ISingleSelectDropdown {
  const dataSource: JbDropdownOption<VesselMovement>[] = [
    { label: 'Select Upcoming Ports', value: 'port' },
    { label: 'Other', value: 'other' },
  ];

  return { id: 'movement', dataSource };
}

export const dateContent: IJbCalender = {
  type: 'date',
  placeholder: 'Date',
  buttonBarShow: true,
  calendarWithInputIcon: true,
  timeZoneRepresentation: eDateTimeZoneRepresentation.Absolute,
};
